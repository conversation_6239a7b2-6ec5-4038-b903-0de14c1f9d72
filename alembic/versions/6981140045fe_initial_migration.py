"""Initial migration

Revision ID: 6981140045fe
Revises: 
Create Date: 2025-08-05 11:01:38.747665

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6981140045fe'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_device_access')
    op.alter_column('devices', 'binding_code',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=10),
               existing_nullable=True)
    op.alter_column('devices', 'binding_qr_code_data',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('orders', 'video_stream_id',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('orders', 'video_stream_id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('devices', 'binding_qr_code_data',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('devices', 'binding_code',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=10),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.create_table('user_device_access',
    sa.Column('id', sa.VARCHAR(), nullable=False),
    sa.Column('user_id', sa.VARCHAR(), nullable=False),
    sa.Column('device_id', sa.VARCHAR(), nullable=False),
    sa.Column('access_granted_at', sa.DATETIME(), nullable=False),
    sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
