#!/usr/bin/env python3
"""
测试登录功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1/auth"

def test_login():
    """测试登录功能"""
    print("🧪 测试用户登录")
    
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/login", json=login_data)
        print(f"登录请求状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功!")
            print(f"用户名: {result.get('username')}")
            print(f"角色: {result.get('role')}")
            print(f"令牌: {result.get('access_token', '')[:20]}...")
            return result.get("access_token")
        else:
            print(f"❌ 登录失败")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None

def test_register_new_user():
    """注册一个新用户"""
    print("\n🧪 注册新用户")
    
    import time
    username = f"user_{int(time.time())}"  # 使用时间戳确保唯一性
    
    register_data = {
        "username": username,
        "password": "testpass123",
        "role": "user"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/register", json=register_data)
        print(f"注册请求状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 注册成功!")
            print(f"用户名: {result.get('username')}")
            print(f"角色: {result.get('role')}")
            
            # 立即测试登录
            print(f"\n🧪 测试新用户登录")
            login_data = {
                "username": username,
                "password": "testpass123"
            }
            
            login_response = requests.post(f"{BASE_URL}/login", json=login_data)
            print(f"新用户登录状态码: {login_response.status_code}")
            
            if login_response.status_code == 200:
                login_result = login_response.json()
                print(f"✅ 新用户登录成功!")
                return login_result.get("access_token")
            else:
                print(f"❌ 新用户登录失败: {login_response.text}")
                
        else:
            print(f"❌ 注册失败")
            
    except Exception as e:
        print(f"❌ 注册请求失败: {str(e)}")
    
    return None

def main():
    """主测试函数"""
    print("🚀 开始登录测试")
    print("="*50)
    
    # 测试现有用户登录
    token = test_login()
    
    if not token:
        # 如果现有用户登录失败，尝试注册新用户
        print("\n现有用户登录失败，尝试注册新用户...")
        token = test_register_new_user()
    
    if token:
        print(f"\n🎉 成功获得访问令牌!")
    else:
        print(f"\n❌ 所有登录尝试都失败了")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
