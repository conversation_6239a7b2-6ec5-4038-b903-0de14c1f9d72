version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17
    environment:
      POSTGRES_DB: robot_db
      POSTGRES_USER: robot_user
      POSTGRES_PASSWORD: robot_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U robot_user -d robot_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Robot Control API
  api:
    build: .
    environment:
      DATABASE_URL: ************************************************/robot_db
      SECRET_KEY: your-production-secret-key-change-this
      DEBUG: "false"
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  postgres_data:
