"""
分页工具模块
提供统一的分页功能
"""

from typing import Generic, TypeVar, List, Optional, Dict, Any
from pydantic import BaseModel, Field
from sqlmodel import Session, select, func
from sqlalchemy import Select
from math import ceil

T = TypeVar('T')


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量，最大100")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T] = Field(description="当前页数据")
    total: int = Field(description="总记录数")
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页数量")
    total_pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    
    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        page: int,
        page_size: int
    ) -> "PaginatedResponse[T]":
        """创建分页响应"""
        total_pages = ceil(total / page_size) if page_size > 0 else 0
        
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )


class Paginator:
    """分页器"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def paginate(
        self,
        statement: Select,
        pagination: PaginationParams,
        transform_func: Optional[callable] = None
    ) -> PaginatedResponse:
        """
        执行分页查询
        
        Args:
            statement: SQLModel查询语句
            pagination: 分页参数
            transform_func: 数据转换函数，用于将数据库对象转换为响应模型
        
        Returns:
            分页响应
        """
        # 获取总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()
        
        # 执行分页查询
        paginated_statement = statement.offset(pagination.offset).limit(pagination.page_size)
        items = self.session.exec(paginated_statement).all()
        
        # 转换数据
        if transform_func:
            transformed_items = [transform_func(item) for item in items]
        else:
            transformed_items = list(items)
        
        return PaginatedResponse.create(
            items=transformed_items,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size
        )


class FilterParams(BaseModel):
    """通用过滤参数"""
    search: Optional[str] = Field(default=None, description="搜索关键词")
    status: Optional[str] = Field(default=None, description="状态过滤")
    created_from: Optional[str] = Field(default=None, description="创建时间起始")
    created_to: Optional[str] = Field(default=None, description="创建时间结束")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


class OrderFilterParams(FilterParams):
    """订单过滤参数"""
    device_id: Optional[str] = Field(default=None, description="设备ID过滤")
    min_amount: Optional[float] = Field(default=None, ge=0, description="最小金额")
    max_amount: Optional[float] = Field(default=None, ge=0, description="最大金额")


class UserFilterParams(FilterParams):
    """用户过滤参数"""
    role: Optional[str] = Field(default=None, description="角色过滤")
    is_active: Optional[bool] = Field(default=None, description="激活状态过滤")


class DeviceFilterParams(FilterParams):
    """设备过滤参数"""
    device_status: Optional[str] = Field(default=None, description="设备状态过滤")


def apply_order_filters(statement: Select, filters: OrderFilterParams) -> Select:
    """应用订单过滤条件"""
    from models.order import Order
    
    if filters.search:
        # 搜索订单ID或设备ID
        statement = statement.where(
            (Order.id.contains(filters.search)) |
            (Order.device_id.contains(filters.search))
        )
    
    if filters.status:
        statement = statement.where(Order.status == filters.status)
    
    if filters.device_id:
        statement = statement.where(Order.device_id == filters.device_id)
    
    if filters.min_amount is not None:
        statement = statement.where(Order.total_amount >= filters.min_amount)
    
    if filters.max_amount is not None:
        statement = statement.where(Order.total_amount <= filters.max_amount)
    
    if filters.created_from:
        try:
            from datetime import datetime
            created_from = datetime.fromisoformat(filters.created_from.replace('Z', '+00:00'))
            statement = statement.where(Order.created_at >= created_from)
        except ValueError:
            pass  # 忽略无效的日期格式
    
    if filters.created_to:
        try:
            from datetime import datetime
            created_to = datetime.fromisoformat(filters.created_to.replace('Z', '+00:00'))
            statement = statement.where(Order.created_at <= created_to)
        except ValueError:
            pass
    
    # 应用排序
    if filters.sort_by and hasattr(Order, filters.sort_by):
        sort_column = getattr(Order, filters.sort_by)
        if filters.sort_order == "asc":
            statement = statement.order_by(sort_column.asc())
        else:
            statement = statement.order_by(sort_column.desc())
    else:
        # 默认按创建时间倒序
        statement = statement.order_by(Order.created_at.desc())
    
    return statement


def apply_user_filters(statement: Select, filters: UserFilterParams) -> Select:
    """应用用户过滤条件"""
    from models.user import User
    
    if filters.search:
        statement = statement.where(User.username.contains(filters.search))
    
    if filters.role:
        statement = statement.where(User.role == filters.role)
    
    if filters.is_active is not None:
        statement = statement.where(User.is_active == filters.is_active)
    
    if filters.created_from:
        try:
            from datetime import datetime
            created_from = datetime.fromisoformat(filters.created_from.replace('Z', '+00:00'))
            statement = statement.where(User.created_at >= created_from)
        except ValueError:
            pass
    
    if filters.created_to:
        try:
            from datetime import datetime
            created_to = datetime.fromisoformat(filters.created_to.replace('Z', '+00:00'))
            statement = statement.where(User.created_at <= created_to)
        except ValueError:
            pass
    
    # 应用排序
    if filters.sort_by and hasattr(User, filters.sort_by):
        sort_column = getattr(User, filters.sort_by)
        if filters.sort_order == "asc":
            statement = statement.order_by(sort_column.asc())
        else:
            statement = statement.order_by(sort_column.desc())
    else:
        statement = statement.order_by(User.created_at.desc())
    
    return statement


def apply_device_filters(statement: Select, filters: DeviceFilterParams) -> Select:
    """应用设备过滤条件"""
    from models.user_device_binding import Device

    if filters.search:
        statement = statement.where(
            (Device.device_id.contains(filters.search)) |
            (Device.device_name.contains(filters.search))
        )

    if filters.device_status:
        statement = statement.where(Device.status == filters.device_status)

    if filters.created_from:
        try:
            from datetime import datetime
            created_from = datetime.fromisoformat(filters.created_from.replace('Z', '+00:00'))
            if hasattr(Device, 'created_at'):
                statement = statement.where(Device.created_at >= created_from)
        except ValueError:
            pass

    if filters.created_to:
        try:
            from datetime import datetime
            created_to = datetime.fromisoformat(filters.created_to.replace('Z', '+00:00'))
            if hasattr(Device, 'created_at'):
                statement = statement.where(Device.created_at <= created_to)
        except ValueError:
            pass

    # 应用排序 - 使用安全的排序字段
    if filters.sort_by and hasattr(Device, filters.sort_by):
        sort_column = getattr(Device, filters.sort_by)
        if filters.sort_order == "asc":
            statement = statement.order_by(sort_column.asc())
        else:
            statement = statement.order_by(sort_column.desc())
    else:
        # 使用设备ID作为默认排序，因为last_heartbeat_at可能不存在
        statement = statement.order_by(Device.device_id.desc())

    return statement


# 便捷函数
def get_pagination_params(
    page: int = 1,
    page_size: int = 20
) -> PaginationParams:
    """获取分页参数的便捷函数"""
    return PaginationParams(page=page, page_size=page_size)
