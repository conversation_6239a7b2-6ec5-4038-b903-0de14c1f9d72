"""
统一错误处理模块
提供标准化的错误响应和异常处理机制
"""

import traceback
from typing import Dict, Any, Optional
from fastapi import HTTPException, WebSocket
from fastapi.responses import JSONResponse
from utils.logger import get_logger

logger = get_logger(__name__)


class WebRTCError(Exception):
    """WebRTC相关错误基类"""
    
    def __init__(self, message: str, error_code: str = "WEBRTC_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(WebRTCError):
    """认证错误"""
    
    def __init__(self, message: str = "认证失败", details: Optional[Dict] = None):
        super().__init__(message, "AUTH_ERROR", details)


class AuthorizationError(WebRTCError):
    """授权错误"""
    
    def __init__(self, message: str = "权限不足", details: Optional[Dict] = None):
        super().__init__(message, "AUTHORIZATION_ERROR", details)


class ConnectionError(WebRTCError):
    """连接错误"""
    
    def __init__(self, message: str = "连接失败", details: Optional[Dict] = None):
        super().__init__(message, "CONNECTION_ERROR", details)


class SessionError(WebRTCError):
    """会话错误"""
    
    def __init__(self, message: str = "会话错误", details: Optional[Dict] = None):
        super().__init__(message, "SESSION_ERROR", details)


class ValidationError(WebRTCError):
    """数据验证错误"""
    
    def __init__(self, message: str = "数据验证失败", details: Optional[Dict] = None):
        super().__init__(message, "VALIDATION_ERROR", details)


def create_error_response(
    error: Exception,
    status_code: int = 500,
    include_traceback: bool = False
) -> JSONResponse:
    """
    创建标准化的错误响应
    
    Args:
        error: 异常对象
        status_code: HTTP状态码
        include_traceback: 是否包含堆栈跟踪（仅开发环境）
        
    Returns:
        标准化的JSON错误响应
    """
    error_data = {
        "success": False,
        "error": {
            "message": str(error),
            "type": type(error).__name__
        }
    }
    
    # 如果是自定义WebRTC错误，添加错误代码和详细信息
    if isinstance(error, WebRTCError):
        error_data["error"]["code"] = error.error_code
        if error.details:
            error_data["error"]["details"] = error.details
    
    # 开发环境下包含堆栈跟踪
    if include_traceback:
        error_data["error"]["traceback"] = traceback.format_exc()
    
    # 记录错误日志
    logger.error(f"API错误: {error_data['error']['message']}", extra={
        "error_type": type(error).__name__,
        "status_code": status_code,
        "error_details": error_data["error"]
    })
    
    return JSONResponse(
        status_code=status_code,
        content=error_data
    )


async def handle_websocket_error(websocket: WebSocket, error: Exception, close_code: int = 1011):
    """
    处理WebSocket错误
    
    Args:
        websocket: WebSocket连接
        error: 异常对象
        close_code: WebSocket关闭代码
    """
    error_message = {
        "type": "error",
        "payload": {
            "message": str(error),
            "error_type": type(error).__name__
        }
    }
    
    # 如果是自定义WebRTC错误，添加错误代码
    if isinstance(error, WebRTCError):
        error_message["payload"]["code"] = error.error_code
        if error.details:
            error_message["payload"]["details"] = error.details
    
    try:
        # 尝试发送错误消息
        await websocket.send_json(error_message)
        
        # 关闭连接
        await websocket.close(code=close_code, reason=str(error)[:123])  # WebSocket reason限制123字符
        
    except Exception as send_error:
        logger.error(f"发送WebSocket错误消息失败: {send_error}")
        try:
            # 强制关闭连接
            await websocket.close(code=close_code, reason="Internal error")
        except Exception:
            pass  # 连接可能已经关闭
    
    # 记录错误日志
    logger.error(f"WebSocket错误: {str(error)}", extra={
        "error_type": type(error).__name__,
        "close_code": close_code,
        "websocket_state": getattr(websocket, 'client_state', 'unknown')
    })


def handle_api_exception(error: Exception) -> HTTPException:
    """
    将异常转换为HTTPException
    
    Args:
        error: 异常对象
        
    Returns:
        HTTPException对象
    """
    if isinstance(error, AuthenticationError):
        return HTTPException(status_code=401, detail=str(error))
    elif isinstance(error, AuthorizationError):
        return HTTPException(status_code=403, detail=str(error))
    elif isinstance(error, ValidationError):
        return HTTPException(status_code=400, detail=str(error))
    elif isinstance(error, (ConnectionError, SessionError)):
        return HTTPException(status_code=409, detail=str(error))
    elif isinstance(error, WebRTCError):
        return HTTPException(status_code=500, detail=str(error))
    else:
        # 未知错误
        logger.error(f"未处理的异常: {str(error)}", exc_info=True)
        return HTTPException(status_code=500, detail="内部服务器错误")


class ErrorHandler:
    """错误处理器类 - 提供装饰器和上下文管理器"""
    
    @staticmethod
    def api_error_handler(include_traceback: bool = False):
        """API错误处理装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except HTTPException:
                    raise  # 重新抛出HTTPException
                except Exception as e:
                    http_exception = handle_api_exception(e)
                    raise http_exception
            return wrapper
        return decorator
    
    @staticmethod
    def websocket_error_handler(close_code: int = 1011):
        """WebSocket错误处理装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                websocket = None
                # 尝试从参数中找到WebSocket对象
                for arg in args:
                    if isinstance(arg, WebSocket):
                        websocket = arg
                        break
                
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if websocket:
                        await handle_websocket_error(websocket, e, close_code)
                    else:
                        logger.error(f"WebSocket错误处理失败，未找到WebSocket对象: {e}")
                    raise
            return wrapper
        return decorator


# 全局错误处理器实例
error_handler = ErrorHandler()


def get_error_details(error: Exception) -> Dict[str, Any]:
    """
    获取错误详细信息
    
    Args:
        error: 异常对象
        
    Returns:
        错误详细信息字典
    """
    details = {
        "error_type": type(error).__name__,
        "message": str(error),
        "module": getattr(error, '__module__', 'unknown')
    }
    
    if isinstance(error, WebRTCError):
        details["error_code"] = error.error_code
        details["custom_details"] = error.details
    
    return details
