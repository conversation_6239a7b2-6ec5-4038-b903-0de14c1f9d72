"""
性能优化工具模块
提供缓存、监控、优化等功能
"""

import time
import functools
import asyncio
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime, timedelta
import json
import hashlib

from utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录性能指标"""
        timestamp = datetime.utcnow()
        
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append({
            "value": value,
            "timestamp": timestamp,
            "tags": tags or {}
        })
        
        # 保持最近1000条记录
        if len(self.metrics[name]) > 1000:
            self.metrics[name] = self.metrics[name][-1000:]
    
    def get_average(self, name: str, minutes: int = 5) -> Optional[float]:
        """获取指定时间内的平均值"""
        if name not in self.metrics:
            return None
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        recent_values = [
            m["value"] for m in self.metrics[name]
            if m["timestamp"] > cutoff_time
        ]
        
        if not recent_values:
            return None
        
        return sum(recent_values) / len(recent_values)
    
    def get_stats(self, name: str) -> Dict[str, Any]:
        """获取指标统计信息"""
        if name not in self.metrics:
            return {}
        
        values = [m["value"] for m in self.metrics[name]]
        if not values:
            return {}
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1] if values else None
        }


# 全局性能监控器
performance_monitor = PerformanceMonitor()


def timing_decorator(metric_name: Optional[str] = None):
    """性能计时装饰器"""
    def decorator(func: Callable) -> Callable:
        name = metric_name or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    performance_monitor.record_metric(f"{name}.duration", duration)
                    
                    if duration > 1.0:  # 记录慢查询
                        logger.warning(f"Slow operation: {name} took {duration:.2f}s")
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    performance_monitor.record_metric(f"{name}.duration", duration)
                    
                    if duration > 1.0:  # 记录慢查询
                        logger.warning(f"Slow operation: {name} took {duration:.2f}s")
            
            return sync_wrapper
    
    return decorator


class SimpleCache:
    """简单内存缓存"""
    
    def __init__(self, default_ttl: int = 300):
        self.cache = {}
        self.default_ttl = default_ttl
    
    def _generate_key(self, key: str, *args, **kwargs) -> str:
        """生成缓存键"""
        if args or kwargs:
            # 包含参数的键
            params = json.dumps({"args": args, "kwargs": kwargs}, sort_keys=True)
            key_hash = hashlib.md5(params.encode()).hexdigest()[:8]
            return f"{key}:{key_hash}"
        return key
    
    def get(self, key: str, *args, **kwargs) -> Any:
        """获取缓存值"""
        cache_key = self._generate_key(key, *args, **kwargs)
        
        if cache_key in self.cache:
            value, expire_time = self.cache[cache_key]
            if time.time() < expire_time:
                performance_monitor.record_metric("cache.hit", 1, {"key": key})
                return value
            else:
                # 过期删除
                del self.cache[cache_key]
        
        performance_monitor.record_metric("cache.miss", 1, {"key": key})
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, *args, **kwargs):
        """设置缓存值"""
        cache_key = self._generate_key(key, *args, **kwargs)
        expire_time = time.time() + (ttl or self.default_ttl)
        self.cache[cache_key] = (value, expire_time)
        
        performance_monitor.record_metric("cache.set", 1, {"key": key})
    
    def delete(self, key: str, *args, **kwargs):
        """删除缓存值"""
        cache_key = self._generate_key(key, *args, **kwargs)
        if cache_key in self.cache:
            del self.cache[cache_key]
            performance_monitor.record_metric("cache.delete", 1, {"key": key})
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        performance_monitor.record_metric("cache.clear", 1)
    
    def cleanup_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expire_time) in self.cache.items()
            if current_time >= expire_time
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
            performance_monitor.record_metric("cache.cleanup", len(expired_keys))


# 全局缓存实例
simple_cache = SimpleCache()


def cache_result(ttl: int = 300, key_prefix: str = ""):
    """结果缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache_key = key_prefix or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # 尝试从缓存获取
                cached_result = simple_cache.get(cache_key, *args, **kwargs)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = await func(*args, **kwargs)
                simple_cache.set(cache_key, result, ttl, *args, **kwargs)
                return result
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # 尝试从缓存获取
                cached_result = simple_cache.get(cache_key, *args, **kwargs)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                simple_cache.set(cache_key, result, ttl, *args, **kwargs)
                return result
            
            return sync_wrapper
    
    return decorator


class DatabaseOptimizer:
    """数据库优化工具"""
    
    @staticmethod
    def add_indexes_suggestions() -> Dict[str, list]:
        """数据库索引建议"""
        return {
            "orders": [
                "CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);",
                "CREATE INDEX IF NOT EXISTS idx_orders_device_id ON orders(device_id);",
                "CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);",
                "CREATE INDEX IF NOT EXISTS idx_orders_status_created ON orders(status, created_at);"
            ],
            "order_items": [
                "CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);",
                "CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);"
            ],
            "payment_transactions": [
                "CREATE INDEX IF NOT EXISTS idx_payment_order_id ON payment_transactions(order_id);",
                "CREATE INDEX IF NOT EXISTS idx_payment_status ON payment_transactions(status);",
                "CREATE INDEX IF NOT EXISTS idx_payment_method ON payment_transactions(payment_method);"
            ],
            "devices": [
                "CREATE INDEX IF NOT EXISTS idx_devices_device_id ON devices(device_id);",
                "CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);"
            ],
            "user_device_bindings": [
                "CREATE INDEX IF NOT EXISTS idx_bindings_user_id ON user_device_bindings(user_id);",
                "CREATE INDEX IF NOT EXISTS idx_bindings_device_id ON user_device_bindings(device_id);"
            ]
        }
    
    @staticmethod
    def get_query_optimization_tips() -> list:
        """查询优化建议"""
        return [
            "使用 SELECT 指定字段而不是 SELECT *",
            "在 WHERE 条件中使用索引字段",
            "避免在 WHERE 条件中使用函数",
            "使用 LIMIT 限制返回结果数量",
            "使用 JOIN 代替子查询（在适当的情况下）",
            "定期更新表统计信息",
            "监控慢查询日志"
        ]


def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    uptime = time.time() - performance_monitor.start_time
    
    report = {
        "uptime_seconds": uptime,
        "uptime_formatted": str(timedelta(seconds=int(uptime))),
        "cache_stats": {
            "size": len(simple_cache.cache),
            "hit_rate": _calculate_cache_hit_rate(),
        },
        "metrics": {}
    }
    
    # 添加各项指标统计
    for metric_name in performance_monitor.metrics:
        report["metrics"][metric_name] = performance_monitor.get_stats(metric_name)
    
    return report


def _calculate_cache_hit_rate() -> float:
    """计算缓存命中率"""
    hits = sum(m["value"] for m in performance_monitor.metrics.get("cache.hit", []))
    misses = sum(m["value"] for m in performance_monitor.metrics.get("cache.miss", []))
    
    total = hits + misses
    if total == 0:
        return 0.0
    
    return hits / total * 100


# 定期清理过期缓存
async def periodic_cache_cleanup():
    """定期清理缓存"""
    while True:
        try:
            simple_cache.cleanup_expired()
            await asyncio.sleep(300)  # 每5分钟清理一次
        except Exception as e:
            logger.error(f"Cache cleanup error: {str(e)}")
            await asyncio.sleep(60)  # 出错后1分钟重试
