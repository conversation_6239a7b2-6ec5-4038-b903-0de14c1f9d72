import logging
import os
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler
from pythonjsonlogger import json as jsonlogger_lib
from config import settings


def setup_logging():
    """Setup structured JSON logging with daily rotation"""
    # Create logs directory if it doesn't exist
    log_dir = Path(settings.log_file).parent
    log_dir.mkdir(exist_ok=True)

    # Create formatter
    formatter = jsonlogger_lib.JsonFormatter(
        fmt='%(asctime)s %(name)s %(levelname)s %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        json_ensure_ascii=False
    )

    # Setup rotating file handler with configurable rotation
    # 文件名格式: app.log, app.log.2025-07-30, app.log.2025-07-29, etc.
    file_handler = TimedRotatingFileHandler(
        filename=settings.log_file,
        when=settings.log_rotation_when,      # 轮转时间（可配置）
        interval=settings.log_rotation_interval,  # 轮转间隔（可配置）
        backupCount=settings.log_backup_count,    # 保留日志数量（可配置）
        encoding='utf-8',
        delay=False,
        utc=False                 # 使用本地时间
    )
    file_handler.setFormatter(formatter)

    # 设置日志文件名后缀格式
    if settings.log_rotation_when == 'midnight' or settings.log_rotation_when.startswith('D'):
        file_handler.suffix = "%Y-%m-%d"
    elif settings.log_rotation_when.startswith('H'):
        file_handler.suffix = "%Y-%m-%d_%H"
    elif settings.log_rotation_when.startswith('W'):
        file_handler.suffix = "%Y-%m-%d"
    else:
        file_handler.suffix = "%Y-%m-%d_%H-%M-%S"

    # Setup console handler with UTF-8 encoding for Windows compatibility
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 在 Windows 系统上设置控制台编码
    import sys
    if sys.platform == "win32":
        try:
            # 尝试设置控制台为 UTF-8 模式
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
        except Exception:
            # 如果设置失败，使用安全的 ASCII 编码
            pass

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)


def get_logger(name: str) -> logging.Logger:
    """Get logger instance"""
    return logging.getLogger(name)


def cleanup_old_logs():
    """手动清理超过保留期限的日志文件"""
    try:
        log_dir = Path(settings.log_file).parent
        log_name = Path(settings.log_file).name

        if not log_dir.exists():
            return

        # 查找所有相关的日志文件
        log_files = []
        for file_path in log_dir.glob(f"{log_name}.*"):
            if file_path.is_file():
                log_files.append(file_path)

        # 按修改时间排序，删除最旧的文件
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        # 保留指定数量的文件
        files_to_delete = log_files[settings.log_backup_count:]

        for file_path in files_to_delete:
            try:
                file_path.unlink()
                print(f"已删除旧日志文件: {file_path}")
            except Exception as e:
                print(f"删除日志文件失败 {file_path}: {e}")

    except Exception as e:
        print(f"清理日志文件时出错: {e}")


def get_log_info() -> dict:
    """获取日志配置信息"""
    log_dir = Path(settings.log_file).parent
    log_files = []
    total_size = 0

    if log_dir.exists():
        for file_path in log_dir.glob("*.log*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                log_files.append({
                    "name": file_path.name,
                    "size": size,
                    "size_mb": round(size / (1024 * 1024), 2),
                    "modified": file_path.stat().st_mtime
                })
                total_size += size

    return {
        "log_level": settings.log_level,
        "log_file": settings.log_file,
        "rotation_when": settings.log_rotation_when,
        "rotation_interval": settings.log_rotation_interval,
        "backup_count": settings.log_backup_count,
        "max_bytes_mb": round(settings.log_max_bytes / (1024 * 1024), 2),
        "total_files": len(log_files),
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "files": log_files
    }
