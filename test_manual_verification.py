#!/usr/bin/env python3
"""
手动验证安全修复的脚本
"""

import urllib.request
import urllib.parse
import urllib.error
import json

BASE_URL = "http://localhost:8000"

def get_user_token():
    """获取用户token"""
    try:
        data = json.dumps({"username": "testuser", "password": "testpass"}).encode('utf-8')
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/auth/login",
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
            return result.get("access_token")
    except Exception as e:
        print(f"获取token失败: {e}")
        return None

def test_unauthorized_access():
    """测试未授权访问"""
    print("🔒 测试未授权访问是否被正确拒绝...")
    
    try:
        req = urllib.request.Request(f"{BASE_URL}/api/v1/webrtc/devices")
        with urllib.request.urlopen(req) as response:
            print(f"❌ 未授权请求未被拒绝: {response.status}")
            return False
    except urllib.error.HTTPError as e:
        if e.code == 403:
            print("✅ 未授权请求被正确拒绝 (403 Forbidden)")
            return True
        elif e.code == 401:
            print("✅ 未授权请求被正确拒绝 (401 Unauthorized)")
            return True
        else:
            print(f"❌ 意外的状态码: {e.code}")
            return False

def test_authorized_access(token):
    """测试授权访问"""
    print("🔑 测试授权访问...")
    
    if not token:
        print("❌ 没有有效token")
        return False
        
    try:
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/webrtc/devices",
            headers={'Authorization': f'Bearer {token}'}
        )
        
        with urllib.request.urlopen(req) as response:
            print(f"✅ 授权请求成功: {response.status}")
            return True
    except urllib.error.HTTPError as e:
        if e.code == 404:
            print("✅ 授权请求成功 (404 - 正常，因为没有设备)")
            return True
        else:
            print(f"❌ 授权请求失败: {e.code}")
            return False

def test_invalid_token():
    """测试无效token"""
    print("🚫 测试无效token...")
    
    try:
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/webrtc/devices",
            headers={'Authorization': 'Bearer invalid_token_12345'}
        )
        
        with urllib.request.urlopen(req) as response:
            print(f"❌ 无效token未被拒绝: {response.status}")
            return False
    except urllib.error.HTTPError as e:
        if e.code in [401, 403]:
            print(f"✅ 无效token被正确拒绝: {e.code}")
            return True
        else:
            print(f"❌ 意外的状态码: {e.code}")
            return False

def main():
    print("🔐 安全修复手动验证")
    print("=" * 50)
    
    # 获取有效token
    print("📋 步骤1: 获取用户token")
    token = get_user_token()
    if token:
        print(f"✅ 成功获取token: {token[:20]}...")
    else:
        print("❌ 获取token失败")
        return
    
    print("\n📋 步骤2: 测试安全修复效果")
    
    # 测试各种情况
    results = []
    results.append(test_unauthorized_access())
    results.append(test_authorized_access(token))
    results.append(test_invalid_token())
    
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有安全测试通过！")
        print("\n✅ 安全修复验证成功:")
        print("  - HTTP接口正确使用Authorization头认证")
        print("  - 未授权请求被正确拒绝")
        print("  - 无效token被正确拒绝")
        print("  - 有效token可以正常访问")
        
        print("\n📱 客户端测试建议:")
        print("  1. 打开 clients/mobile_app_client.html")
        print("  2. 使用 testuser/testpass 登录")
        print("  3. 检查WebSocket连接是否正常")
        print("  4. 检查浏览器开发者工具，确认URL中没有token")
        
    else:
        print("⚠️ 部分测试失败")

if __name__ == "__main__":
    main()
