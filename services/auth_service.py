from sqlmodel import Session, select
from typing import Optional
from models.user import User
from utils.security import verify_password, get_password_hash, create_access_token
from utils.logger import get_logger

logger = get_logger(__name__)


class AuthService:
    def __init__(self, session: Session):
        self.session = session
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password"""
        try:
            statement = select(User).where(User.username == username, User.is_active == True)
            user = self.session.exec(statement).first()
            
            if not user:
                logger.warning(f"Authentication failed: user {username} not found")
                return None
            
            if not verify_password(password, user.password_hash):
                logger.warning(f"Authentication failed: invalid password for user {username}")
                return None
            
            logger.info(f"User {username} authenticated successfully")
            return user
        except Exception as e:
            logger.error(f"Error authenticating user {username}: {str(e)}")
            return None
    
    def create_user(self, username: str, password: str, role: str = "webrtc_app_user") -> Optional[User]:
        """Create a new user"""
        try:
            # Check if user already exists
            statement = select(User).where(User.username == username)
            existing_user = self.session.exec(statement).first()
            
            if existing_user:
                logger.warning(f"User creation failed: username {username} already exists")
                return None
            
            # Create new user
            user = User(
                username=username,
                password_hash=get_password_hash(password),
                role=role
            )
            
            self.session.add(user)
            self.session.commit()
            self.session.refresh(user)
            
            logger.info(f"User {username} created successfully with role {role}")
            return user
        except Exception as e:
            logger.error(f"Error creating user {username}: {str(e)}")
            self.session.rollback()
            return None
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            statement = select(User).where(User.id == user_id, User.is_active == True)
            return self.session.exec(statement).first()
        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {str(e)}")
            return None
