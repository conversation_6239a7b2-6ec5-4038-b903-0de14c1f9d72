from typing import Dict, Type
from .payment_adapter import PaymentAdapter
from .alipay_adapter import AlipayAdapter
from .payment_models import PaymentMethod
from utils.logger import get_logger

logger = get_logger(__name__)


class PaymentFactory:
    """支付适配器工厂类"""
    
    # 注册的支付适配器
    _adapters: Dict[PaymentMethod, Type[PaymentAdapter]] = {
        PaymentMethod.ALIPAY: AlipayAdapter,
        # PaymentMethod.WECHAT: WechatAdapter,  # 待实现
        # PaymentMethod.UNIONPAY: UnionpayAdapter,  # 待实现
    }
    
    @classmethod
    def create_adapter(cls, payment_method: PaymentMethod, config: dict) -> PaymentAdapter:
        """
        创建支付适配器实例
        
        Args:
            payment_method: 支付方式
            config: 支付配置
            
        Returns:
            PaymentAdapter: 支付适配器实例
            
        Raises:
            ValueError: 不支持的支付方式
        """
        if payment_method not in cls._adapters:
            raise ValueError(f"Unsupported payment method: {payment_method}")
        
        adapter_class = cls._adapters[payment_method]
        adapter = adapter_class(config)
        
        logger.info(f"Created payment adapter for {payment_method}")
        return adapter
    
    @classmethod
    def register_adapter(cls, payment_method: PaymentMethod, adapter_class: Type[PaymentAdapter]):
        """
        注册新的支付适配器
        
        Args:
            payment_method: 支付方式
            adapter_class: 适配器类
        """
        cls._adapters[payment_method] = adapter_class
        logger.info(f"Registered payment adapter for {payment_method}")
    
    @classmethod
    def get_supported_methods(cls) -> list:
        """获取支持的支付方式列表"""
        return list(cls._adapters.keys())


# 示例：微信支付适配器（待实现）
class WechatAdapter(PaymentAdapter):
    """微信支付适配器（示例）"""
    
    def create_payment(self, request):
        # TODO: 实现微信支付创建逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def query_payment(self, request):
        # TODO: 实现微信支付查询逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def close_payment(self, out_trade_no, trade_no=None):
        # TODO: 实现微信支付关闭逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def refund_payment(self, request):
        # TODO: 实现微信退款逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def query_refund(self, request):
        # TODO: 实现微信退款查询逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def verify_callback(self, callback_data):
        # TODO: 实现微信回调验证逻辑
        raise NotImplementedError("Wechat payment not implemented yet")
    
    def parse_callback(self, callback_data):
        # TODO: 实现微信回调解析逻辑
        raise NotImplementedError("Wechat payment not implemented yet")


# 可以在这里注册微信支付适配器（当实现完成后）
# PaymentFactory.register_adapter(PaymentMethod.WECHAT, WechatAdapter)
