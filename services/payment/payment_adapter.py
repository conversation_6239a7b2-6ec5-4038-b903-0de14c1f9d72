from abc import ABC, abstractmethod
from typing import Optional
from .payment_models import (
    PaymentRequest, PaymentResponse, PaymentQueryRequest, PaymentQueryResponse,
    RefundRequest, RefundResponse, RefundQueryRequest, RefundQueryResponse
)


class PaymentAdapter(ABC):
    """支付适配器抽象基类"""
    
    def __init__(self, config: dict):
        """
        初始化支付适配器
        
        Args:
            config: 支付配置字典，包含各种支付平台的配置信息
        """
        self.config = config
    
    @abstractmethod
    def create_payment(self, request: PaymentRequest) -> PaymentResponse:
        """
        创建支付订单
        
        Args:
            request: 支付请求
            
        Returns:
            PaymentResponse: 支付响应，包含支付链接或二维码
        """
        pass
    
    @abstractmethod
    def query_payment(self, request: PaymentQueryRequest) -> PaymentQueryResponse:
        """
        查询支付状态
        
        Args:
            request: 查询请求
            
        Returns:
            PaymentQueryResponse: 查询响应
        """
        pass
    
    @abstractmethod
    def close_payment(self, out_trade_no: str, trade_no: Optional[str] = None) -> bool:
        """
        关闭支付订单
        
        Args:
            out_trade_no: 商户订单号
            trade_no: 支付平台交易号
            
        Returns:
            bool: 是否成功关闭
        """
        pass
    
    @abstractmethod
    def refund_payment(self, request: RefundRequest) -> RefundResponse:
        """
        申请退款
        
        Args:
            request: 退款请求
            
        Returns:
            RefundResponse: 退款响应
        """
        pass
    
    @abstractmethod
    def query_refund(self, request: RefundQueryRequest) -> RefundQueryResponse:
        """
        查询退款状态
        
        Args:
            request: 退款查询请求
            
        Returns:
            RefundQueryResponse: 退款查询响应
        """
        pass
    
    @abstractmethod
    def verify_callback(self, callback_data: dict) -> bool:
        """
        验证支付回调签名
        
        Args:
            callback_data: 回调数据
            
        Returns:
            bool: 签名是否有效
        """
        pass
    
    @abstractmethod
    def parse_callback(self, callback_data: dict) -> PaymentQueryResponse:
        """
        解析支付回调数据
        
        Args:
            callback_data: 回调数据
            
        Returns:
            PaymentQueryResponse: 解析后的支付状态
        """
        pass
