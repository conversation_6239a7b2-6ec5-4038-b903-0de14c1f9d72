from pydantic import BaseModel
from typing import Optional, Dict, Any
from enum import Enum
from datetime import datetime


class PaymentStatus(str, Enum):
    """支付状态枚举"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIAL_REFUNDED = "partial_refunded"


class PaymentMethod(str, Enum):
    """支付方式枚举"""
    ALIPAY = "alipay"
    WECHAT = "wechat"
    UNIONPAY = "unionpay"


class PaymentRequest(BaseModel):
    """支付请求模型"""
    out_trade_no: str  # 商户订单号
    total_amount: float  # 支付金额
    subject: str  # 订单标题
    body: Optional[str] = None  # 订单描述
    timeout_express: Optional[str] = "30m"  # 超时时间
    return_url: Optional[str] = None  # 同步回调地址
    notify_url: Optional[str] = None  # 异步回调地址
    extra_params: Optional[Dict[str, Any]] = None  # 额外参数


class PaymentResponse(BaseModel):
    """支付响应模型"""
    success: bool
    payment_url: Optional[str] = None  # 支付链接
    qr_code: Optional[str] = None  # 二维码内容
    trade_no: Optional[str] = None  # 支付平台交易号
    out_trade_no: str  # 商户订单号
    message: Optional[str] = None
    error_code: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


class PaymentQueryRequest(BaseModel):
    """支付查询请求"""
    out_trade_no: Optional[str] = None
    trade_no: Optional[str] = None


class PaymentQueryResponse(BaseModel):
    """支付查询响应"""
    success: bool
    trade_status: PaymentStatus
    out_trade_no: str
    trade_no: Optional[str] = None
    total_amount: Optional[float] = None
    buyer_id: Optional[str] = None
    buyer_logon_id: Optional[str] = None
    send_pay_date: Optional[datetime] = None
    message: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


class RefundRequest(BaseModel):
    """退款请求模型"""
    out_trade_no: Optional[str] = None
    trade_no: Optional[str] = None
    refund_amount: float
    refund_reason: Optional[str] = None
    out_request_no: Optional[str] = None  # 退款请求号


class RefundResponse(BaseModel):
    """退款响应模型"""
    success: bool
    out_trade_no: str
    trade_no: Optional[str] = None
    refund_fee: Optional[float] = None
    gmt_refund_pay: Optional[datetime] = None
    fund_change: Optional[str] = None
    message: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


class RefundQueryRequest(BaseModel):
    """退款查询请求"""
    out_trade_no: Optional[str] = None
    trade_no: Optional[str] = None
    out_request_no: Optional[str] = None


class RefundQueryResponse(BaseModel):
    """退款查询响应"""
    success: bool
    out_trade_no: str
    refund_status: Optional[str] = None
    refund_amount: Optional[float] = None
    message: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None
