"""
依赖注入容器
管理应用程序的所有依赖项，支持多进程环境
"""

import os
import uuid
from typing import Optional, Dict, Any
from fastapi import WebSocket
from services.webrtc_redis_service import WebRTCRedisService, get_redis_service
from utils.logger import get_logger

logger = get_logger(__name__)

# 进程ID，用于多进程环境下的连接管理
PROCESS_ID = str(uuid.uuid4())[:8]


class WebRTCConnectionManager:
    """
    WebRTC连接管理器
    负责管理WebSocket连接和会话状态，支持多进程环境
    """
    
    def __init__(self, redis_service: Optional[WebRTCRedisService] = None):
        """
        初始化连接管理器
        
        Args:
            redis_service: Redis服务实例，如果为None则自动创建
        """
        self.redis_service = redis_service or get_redis_service()
        self.process_id = PROCESS_ID
        
        # 本地WebSocket连接存储（不能序列化到Redis）
        self.local_websockets: Dict[str, WebSocket] = {}
        
        # 本地连接和会话存储（用于快速访问）
        self.local_connections: Dict[str, Dict[str, Any]] = {}
        self.local_sessions: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"WebRTC连接管理器初始化完成，进程ID: {self.process_id}")
    
    async def add_websocket_connection(self, connection_id: str, websocket: WebSocket):
        """
        添加WebSocket连接到本地存储
        
        Args:
            connection_id: 连接ID
            websocket: WebSocket实例
        """
        self.local_websockets[connection_id] = websocket
        logger.debug(f"添加WebSocket连接: {connection_id}")
    
    async def remove_websocket_connection(self, connection_id: str):
        """
        移除WebSocket连接
        
        Args:
            connection_id: 连接ID
        """
        if connection_id in self.local_websockets:
            del self.local_websockets[connection_id]
            logger.debug(f"移除WebSocket连接: {connection_id}")
    
    async def get_websocket_connection(self, connection_id: str) -> Optional[WebSocket]:
        """
        获取WebSocket连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            WebSocket实例或None
        """
        return self.local_websockets.get(connection_id)
    
    async def add_connection_info(self, connection_id: str, connection_info: dict):
        """
        添加连接信息到Redis和本地缓存
        
        Args:
            connection_id: 连接ID
            connection_info: 连接信息
        """
        # 添加进程ID到连接信息
        connection_info["process_id"] = self.process_id
        connection_info["is_active"] = True
        
        # 存储到Redis
        await self.redis_service.add_connection(connection_id, connection_info)
        
        # 存储到本地缓存
        self.local_connections[connection_id] = connection_info
        
        logger.info(f"添加连接信息: {connection_id} (进程: {self.process_id})")
    
    async def remove_connection_info(self, connection_id: str):
        """
        移除连接信息
        
        Args:
            connection_id: 连接ID
        """
        # 从Redis移除
        await self.redis_service.remove_connection(connection_id)
        
        # 从本地缓存移除
        self.local_connections.pop(connection_id, None)
        
        logger.info(f"移除连接信息: {connection_id}")
    
    async def get_connection_info(self, connection_id: str) -> Optional[dict]:
        """
        获取连接信息
        
        Args:
            connection_id: 连接ID
            
        Returns:
            连接信息字典或None
        """
        # 先从本地缓存获取
        if connection_id in self.local_connections:
            return self.local_connections[connection_id]
        
        # 从Redis获取
        connection_info = await self.redis_service.get_connection(connection_id)
        if connection_info:
            # 更新本地缓存
            self.local_connections[connection_id] = connection_info
        
        return connection_info
    
    async def get_all_connections(self) -> Dict[str, dict]:
        """
        获取所有连接信息
        
        Returns:
            所有连接信息的字典
        """
        return await self.redis_service.get_all_connections()
    
    async def get_local_connections_count(self) -> int:
        """
        获取本地WebSocket连接数量
        
        Returns:
            本地连接数量
        """
        return len(self.local_websockets)
    
    async def cleanup_connection(self, connection_id: str):
        """
        完全清理连接（WebSocket + 信息）
        
        Args:
            connection_id: 连接ID
        """
        # 移除WebSocket连接
        await self.remove_websocket_connection(connection_id)
        
        # 移除连接信息
        await self.remove_connection_info(connection_id)
        
        logger.info(f"完全清理连接: {connection_id}")
    
    async def add_session(self, session_id: str, session_info: dict):
        """
        添加会话信息
        
        Args:
            session_id: 会话ID
            session_info: 会话信息
        """
        session_info["process_id"] = self.process_id
        
        # 存储到Redis
        await self.redis_service.add_session(session_id, session_info)
        
        # 存储到本地缓存
        self.local_sessions[session_id] = session_info
        
        logger.info(f"添加会话: {session_id}")
    
    async def remove_session(self, session_id: str):
        """
        移除会话信息
        
        Args:
            session_id: 会话ID
        """
        # 从Redis移除
        await self.redis_service.remove_session(session_id)
        
        # 从本地缓存移除
        self.local_sessions.pop(session_id, None)
        
        logger.info(f"移除会话: {session_id}")
    
    async def get_session(self, session_id: str) -> Optional[dict]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息字典或None
        """
        # 先从本地缓存获取
        if session_id in self.local_sessions:
            return self.local_sessions[session_id]
        
        # 从Redis获取
        session_info = await self.redis_service.get_session(session_id)
        if session_info:
            # 更新本地缓存
            self.local_sessions[session_id] = session_info
        
        return session_info
    
    async def get_all_sessions(self) -> Dict[str, dict]:
        """
        获取所有会话信息
        
        Returns:
            所有会话信息的字典
        """
        return await self.redis_service.get_all_sessions()


# 全局连接管理器实例
_connection_manager: Optional[WebRTCConnectionManager] = None


def get_connection_manager() -> WebRTCConnectionManager:
    """
    获取连接管理器实例（单例模式）
    
    Returns:
        WebRTC连接管理器实例
    """
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = WebRTCConnectionManager()
    return _connection_manager


def reset_connection_manager():
    """
    重置连接管理器（主要用于测试）
    """
    global _connection_manager
    _connection_manager = None
