"""
WebRTC Redis 服务
用于在多进程环境下管理WebRTC连接和会话状态
"""

import json
import redis
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import uuid
from fastapi import WebSocket
from config import settings

logger = logging.getLogger(__name__)

class WebRTCRedisService:
    """WebRTC Redis状态管理服务"""
    
    def __init__(self, redis_url: Optional[str] = None):
        """初始化Redis连接"""
        self.redis_available = False
        self.redis_client = None
        self.pubsub = None

        # 内存回退存储
        self.memory_connections = {}
        self.memory_sessions = {}

        # 消息中继功能
        self.websocket_connections: Dict[str, WebSocket] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self.message_queue = asyncio.Queue()
        self.relay_task: Optional[asyncio.Task] = None
        self.relay_running = False

        try:
            if redis_url is None:
                redis_url = settings.redis_url

            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            # 测试连接
            self.redis_client.ping()
            self.pubsub = self.redis_client.pubsub()
            self.redis_available = True

            # Redis键前缀
            prefix = settings.webrtc_key_prefix
            self.CONNECTION_PREFIX = f"{prefix}:connection:"
            self.SESSION_PREFIX = f"{prefix}:session:"
            self.CHANNEL_PREFIX = f"{prefix}:channel:"

            # 过期时间设置
            self.CONNECTION_TTL = settings.webrtc_connection_ttl
            self.SESSION_TTL = settings.webrtc_session_ttl

            logger.info("成功 WebRTC Redis服务初始化完成")

        except Exception as e:
            logger.warning(f"警告 Redis不可用，使用内存存储回退: {e}")
            self.redis_available = False
    
    def _connection_key(self, connection_id: str) -> str:
        """生成连接键"""
        return f"{self.CONNECTION_PREFIX}{connection_id}"
    
    def _session_key(self, session_id: str) -> str:
        """生成会话键"""
        return f"{self.SESSION_PREFIX}{session_id}"
    
    def _channel_key(self, session_id: str) -> str:
        """生成频道键"""
        return f"{self.CHANNEL_PREFIX}{session_id}"
    
    async def add_connection(self, connection_id: str, connection_info: Dict[str, Any]) -> bool:
        """添加连接"""
        try:
            # 序列化连接信息（排除websocket对象）
            serializable_info = {
                "participant_type": connection_info["participant_type"],
                "target_id": connection_info["target_id"],
                "connection_type": connection_info["connection_type"],
                "session_id": connection_info["session_id"],
                "user_info": connection_info["user_info"],
                "connected_at": connection_info["connected_at"].isoformat(),
                "features": connection_info["features"],
                "is_active": connection_info.get("is_active", True),
                "process_id": connection_info.get("process_id", "unknown")
            }

            if self.redis_available:
                key = self._connection_key(connection_id)
                self.redis_client.setex(key, self.CONNECTION_TTL, json.dumps(serializable_info))
                logger.info(f"记录 Redis: 添加连接 {connection_id}")
            else:
                self.memory_connections[connection_id] = serializable_info
                logger.info(f"记录 Memory: 添加连接 {connection_id}")

            return True

        except Exception as e:
            logger.error(f"失败 添加连接失败 {connection_id}: {e}")
            return False
    
    async def get_connection(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """获取连接信息"""
        try:
            if self.redis_available:
                key = self._connection_key(connection_id)
                data = self.redis_client.get(key)

                if data:
                    connection_info = json.loads(data)
                    # 转换时间格式
                    connection_info["connected_at"] = datetime.fromisoformat(connection_info["connected_at"])
                    return connection_info
            else:
                connection_info = self.memory_connections.get(connection_id)
                if connection_info:
                    # 转换时间格式
                    if isinstance(connection_info["connected_at"], str):
                        connection_info["connected_at"] = datetime.fromisoformat(connection_info["connected_at"])
                    return connection_info

            return None

        except Exception as e:
            logger.error(f"失败 获取连接失败 {connection_id}: {e}")
            return None
    
    async def remove_connection(self, connection_id: str) -> bool:
        """移除连接"""
        try:
            if self.redis_available:
                key = self._connection_key(connection_id)
                result = self.redis_client.delete(key)

                if result:
                    logger.info(f"删除 Redis: 移除连接 {connection_id}")

                return bool(result)
            else:
                if connection_id in self.memory_connections:
                    del self.memory_connections[connection_id]
                    logger.info(f"删除 Memory: 移除连接 {connection_id}")
                    return True

            return False

        except Exception as e:
            logger.error(f"失败 移除连接失败 {connection_id}: {e}")
            return False
    
    async def get_all_connections(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接"""
        try:
            if self.redis_available:
                pattern = f"{self.CONNECTION_PREFIX}*"
                keys = self.redis_client.keys(pattern)

                connections = {}
                for key in keys:
                    connection_id = key.replace(self.CONNECTION_PREFIX, "")
                    connection_info = await self.get_connection(connection_id)
                    if connection_info:
                        connections[connection_id] = connection_info

                return connections
            else:
                # 返回内存中的连接，转换时间格式
                connections = {}
                for conn_id, conn_info in self.memory_connections.items():
                    conn_copy = conn_info.copy()
                    if isinstance(conn_copy["connected_at"], str):
                        conn_copy["connected_at"] = datetime.fromisoformat(conn_copy["connected_at"])
                    connections[conn_id] = conn_copy
                return connections

        except Exception as e:
            logger.error(f"失败 获取所有连接失败: {e}")
            return {}
    
    async def add_session(self, session_id: str, session_info: Dict[str, Any]) -> bool:
        """添加会话"""
        try:
            serializable_info = {
                "session_id": session_id,
                "connection_type": session_info["connection_type"],
                "participants": session_info["participants"],
                "created_at": session_info["created_at"].isoformat(),
                "state": session_info.get("state", "active")
            }

            if self.redis_available:
                key = self._session_key(session_id)
                self.redis_client.setex(key, self.SESSION_TTL, json.dumps(serializable_info))
                logger.info(f"记录 Redis: 添加会话 {session_id}")
            else:
                self.memory_sessions[session_id] = serializable_info
                logger.info(f"记录 Memory: 添加会话 {session_id}")

            return True

        except Exception as e:
            logger.error(f"失败 添加会话失败 {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        try:
            if self.redis_available:
                key = self._session_key(session_id)
                data = self.redis_client.get(key)

                if data:
                    session_info = json.loads(data)
                    session_info["created_at"] = datetime.fromisoformat(session_info["created_at"])
                    return session_info
            else:
                session_info = self.memory_sessions.get(session_id)
                if session_info:
                    session_copy = session_info.copy()
                    if isinstance(session_copy["created_at"], str):
                        session_copy["created_at"] = datetime.fromisoformat(session_copy["created_at"])
                    return session_copy

            return None

        except Exception as e:
            logger.error(f"失败 获取会话失败 {session_id}: {e}")
            return None
    
    async def update_session_participants(self, session_id: str, participants: Dict[str, str]) -> bool:
        """更新会话参与者"""
        try:
            session_info = await self.get_session(session_id)
            if session_info:
                session_info["participants"] = participants
                return await self.add_session(session_id, session_info)

            return False

        except Exception as e:
            logger.error(f"失败 更新会话参与者失败 {session_id}: {e}")
            return False
    
    async def remove_session(self, session_id: str) -> bool:
        """移除会话"""
        try:
            if self.redis_available:
                key = self._session_key(session_id)
                result = self.redis_client.delete(key)

                if result:
                    logger.info(f"删除 Redis: 移除会话 {session_id}")

                return bool(result)
            else:
                if session_id in self.memory_sessions:
                    del self.memory_sessions[session_id]
                    logger.info(f"删除 Memory: 移除会话 {session_id}")
                    return True

            return False

        except Exception as e:
            logger.error(f"失败 移除会话失败 {session_id}: {e}")
            return False
    
    async def get_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """获取所有会话"""
        try:
            if self.redis_available:
                pattern = f"{self.SESSION_PREFIX}*"
                keys = self.redis_client.keys(pattern)

                sessions = {}
                for key in keys:
                    session_id = key.replace(self.SESSION_PREFIX, "")
                    session_info = await self.get_session(session_id)
                    if session_info:
                        sessions[session_id] = session_info

                return sessions
            else:
                # 返回内存中的会话，转换时间格式
                sessions = {}
                for session_id, session_info in self.memory_sessions.items():
                    session_copy = session_info.copy()
                    if isinstance(session_copy["created_at"], str):
                        session_copy["created_at"] = datetime.fromisoformat(session_copy["created_at"])
                    sessions[session_id] = session_copy
                return sessions

        except Exception as e:
            logger.error(f"失败 获取所有会话失败: {e}")
            return {}
    
    async def publish_message(self, session_id: str, message: Dict[str, Any]) -> bool:
        """发布消息到会话频道"""
        try:
            channel = self._channel_key(session_id)
            message_data = {
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id,
                "message": message
            }
            
            result = self.redis_client.publish(channel, json.dumps(message_data))
            logger.debug(f"消息 Redis: 发布消息到 {channel}, 订阅者数量: {result}")
            
            return True
            
        except Exception as e:
            logger.error(f"失败 Redis: 发布消息失败 {session_id}: {e}")
            return False
    
    async def subscribe_session(self, session_id: str):
        """订阅会话频道"""
        try:
            channel = self._channel_key(session_id)
            self.pubsub.subscribe(channel)
            logger.info(f"消息 Redis: 订阅频道 {channel}")
            
        except Exception as e:
            logger.error(f"失败 Redis: 订阅频道失败 {session_id}: {e}")
    
    async def unsubscribe_session(self, session_id: str):
        """取消订阅会话频道"""
        try:
            channel = self._channel_key(session_id)
            self.pubsub.unsubscribe(channel)
            logger.info(f"消息 Redis: 取消订阅频道 {channel}")
            
        except Exception as e:
            logger.error(f"失败 Redis: 取消订阅失败 {session_id}: {e}")
    
    async def get_messages(self) -> List[Dict[str, Any]]:
        """获取订阅的消息"""
        try:
            messages = []
            message = self.pubsub.get_message(timeout=0.1)
            
            while message:
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        messages.append(data)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析Redis消息: {message['data']}")
                
                message = self.pubsub.get_message(timeout=0.1)
            
            return messages
            
        except Exception as e:
            logger.error(f"失败 Redis: 获取消息失败: {e}")
            return []
    
    async def cleanup_expired(self) -> int:
        """清理过期的连接和会话"""
        try:
            cleaned_count = 0
            
            # 清理过期连接
            connection_keys = self.redis_client.keys(f"{self.CONNECTION_PREFIX}*")
            for key in connection_keys:
                ttl = self.redis_client.ttl(key)
                if ttl == -1:  # 没有过期时间的键
                    self.redis_client.expire(key, self.CONNECTION_TTL)
                elif ttl == -2:  # 已过期的键
                    self.redis_client.delete(key)
                    cleaned_count += 1
            
            # 清理过期会话
            session_keys = self.redis_client.keys(f"{self.SESSION_PREFIX}*")
            for key in session_keys:
                ttl = self.redis_client.ttl(key)
                if ttl == -1:
                    self.redis_client.expire(key, self.SESSION_TTL)
                elif ttl == -2:
                    self.redis_client.delete(key)
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理 Redis: 清理了 {cleaned_count} 个过期项")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"失败 Redis: 清理过期项失败: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        try:
            if self.redis_available:
                connection_count = len(self.redis_client.keys(f"{self.CONNECTION_PREFIX}*"))
                session_count = len(self.redis_client.keys(f"{self.SESSION_PREFIX}*"))
                memory_usage = self.redis_client.memory_usage("webrtc:*") if hasattr(self.redis_client, 'memory_usage') else 0
            else:
                connection_count = len(self.memory_connections)
                session_count = len(self.memory_sessions)
                memory_usage = 0

            return {
                "active_connections": connection_count,
                "active_sessions": session_count,
                "redis_memory_usage": memory_usage
            }

        except Exception as e:
            logger.error(f"失败 获取统计信息失败: {e}")
            return {"active_connections": 0, "active_sessions": 0, "redis_memory_usage": 0}
    
    # ==================== 消息中继功能 ====================

    async def start_message_relay(self):
        """启动消息中继服务"""
        if self.relay_running:
            return

        self.relay_running = True
        self.relay_task = asyncio.create_task(self._message_relay_loop())
        logger.info("Redis message relay started")

    async def stop_message_relay(self):
        """停止消息中继服务"""
        self.relay_running = False
        if self.relay_task:
            self.relay_task.cancel()
            try:
                await self.relay_task
            except asyncio.CancelledError:
                pass
        logger.info("Redis message relay stopped")

    def register_websocket(self, connection_id: str, websocket: WebSocket):
        """注册WebSocket连接"""
        self.websocket_connections[connection_id] = websocket
        logger.debug(f"Registered WebSocket connection: {connection_id}")

    def unregister_websocket(self, connection_id: str):
        """注销WebSocket连接"""
        if connection_id in self.websocket_connections:
            del self.websocket_connections[connection_id]
            logger.debug(f"Unregistered WebSocket connection: {connection_id}")

    def register_redis_handler(self, channel: str, connection_id: str):
        """注册Redis消息处理器"""
        def handler(message_data):
            """Redis消息处理器"""
            try:
                # 将消息放入异步队列
                asyncio.create_task(self.message_queue.put({
                    'connection_id': connection_id,
                    'channel': channel,
                    'message': message_data
                }))
            except Exception as e:
                logger.error(f"Error queuing Redis message: {str(e)}")

        self.message_handlers[channel] = handler
        logger.debug(f"Registered Redis handler for channel: {channel}")
        return handler

    def unregister_redis_handler(self, channel: str):
        """注销Redis消息处理器"""
        if channel in self.message_handlers:
            del self.message_handlers[channel]
            logger.debug(f"Unregistered Redis handler for channel: {channel}")

    async def _message_relay_loop(self):
        """消息中继循环"""
        logger.info("Message relay loop started")

        while self.relay_running:
            try:
                # 等待消息
                message_data = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )

                connection_id = message_data.get('connection_id')
                channel = message_data.get('channel')
                message = message_data.get('message', {})

                # 查找对应的WebSocket连接
                if connection_id in self.websocket_connections:
                    websocket = self.websocket_connections[connection_id]

                    try:
                        # 检查WebSocket连接状态
                        if websocket.client_state.CONNECTED:
                            # 转发消息到WebSocket
                            await websocket.send_text(json.dumps(message.get('message', {})))
                            logger.debug(f"Relayed Redis message to WebSocket: {connection_id}")
                        else:
                            logger.warning(f"WebSocket {connection_id} is not connected")
                            # 清理断开的连接
                            self.unregister_websocket(connection_id)

                    except Exception as e:
                        logger.error(f"Error sending message to WebSocket {connection_id}: {str(e)}")
                        # 清理出错的连接
                        self.unregister_websocket(connection_id)
                else:
                    logger.warning(f"WebSocket connection {connection_id} not found")

                # 标记任务完成
                self.message_queue.task_done()

            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"Error in message relay loop: {str(e)}")

    async def send_direct_message(self, connection_id: str, message: Dict[str, Any]):
        """直接发送消息到WebSocket"""
        if connection_id in self.websocket_connections:
            websocket = self.websocket_connections[connection_id]
            try:
                if websocket.client_state.CONNECTED:
                    await websocket.send_text(json.dumps(message))
                    logger.debug(f"Sent direct message to WebSocket: {connection_id}")
                    return True
                else:
                    logger.warning(f"WebSocket {connection_id} is not connected")
                    self.unregister_websocket(connection_id)
            except Exception as e:
                logger.error(f"Error sending direct message to {connection_id}: {str(e)}")
                self.unregister_websocket(connection_id)
        else:
            logger.warning(f"WebSocket connection {connection_id} not found")

        return False

    def get_websocket_connection_count(self) -> int:
        """获取WebSocket连接数"""
        return len(self.websocket_connections)

    def get_message_handler_count(self) -> int:
        """获取消息处理器数量"""
        return len(self.message_handlers)

    async def get_message_queue_size(self) -> int:
        """获取消息队列大小"""
        return self.message_queue.qsize()

    def get_relay_status(self) -> Dict[str, Any]:
        """获取中继器状态"""
        return {
            "relay_running": self.relay_running,
            "websocket_connections": self.get_websocket_connection_count(),
            "message_handlers": self.get_message_handler_count(),
            "queue_size": self.message_queue.qsize() if hasattr(self.message_queue, 'qsize') else 0
        }

    def close(self):
        """关闭Redis连接"""
        try:
            # 停止消息中继
            if self.relay_running:
                asyncio.create_task(self.stop_message_relay())

            if self.redis_available and self.pubsub:
                self.pubsub.close()
            if self.redis_available and self.redis_client:
                self.redis_client.close()
                logger.info("Redis连接已关闭")

            # 清理内存存储
            self.memory_connections.clear()
            self.memory_sessions.clear()
            self.websocket_connections.clear()
            self.message_handlers.clear()

        except Exception as e:
            logger.error(f"关闭连接失败: {e}")


# 全局Redis服务实例
redis_service: Optional[WebRTCRedisService] = None

def get_redis_service() -> WebRTCRedisService:
    """获取Redis服务实例"""
    global redis_service
    if redis_service is None:
        redis_service = WebRTCRedisService()
    return redis_service

def close_redis_service():
    """关闭Redis服务"""
    global redis_service
    if redis_service:
        redis_service.close()
        redis_service = None
