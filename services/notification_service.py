"""
统一通知服务
支持多种通知方式：WebSocket、日志、数据库记录、第三方推送等
符合PRD要求的通知体系
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum
from abc import ABC, abstractmethod

from utils.logger import get_logger
from config import settings

logger = get_logger(__name__)


class NotificationType(str, Enum):
    """通知类型枚举"""
    NEW_ORDER = "new_order"                    # 新订单通知
    PICKUP_READY = "pickup_ready"              # 备料完成通知
    STATUS_CHANGE = "status_change"            # 状态变更通知
    ORDER_TIMEOUT = "order_timeout"            # 订单超时通知
    SYSTEM_ALERT = "system_alert"              # 系统警报
    INVENTORY_WARNING = "inventory_warning"     # 库存警告


class NotificationPriority(str, Enum):
    """通知优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class NotificationChannel(str, Enum):
    """通知渠道"""
    WEBSOCKET = "websocket"
    LOG = "log"
    DATABASE = "database"
    PUSH = "push"
    EMAIL = "email"
    SMS = "sms"


class NotificationMessage:
    """通知消息模型"""
    
    def __init__(
        self,
        notification_type: NotificationType,
        title: str,
        message: str,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        target_audience: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        channels: Optional[List[NotificationChannel]] = None
    ):
        self.id = self._generate_id()
        self.type = notification_type
        self.title = title
        self.message = message
        self.priority = priority
        self.target_audience = target_audience or "all"
        self.data = data or {}
        self.channels = channels or [NotificationChannel.LOG]
        self.created_at = datetime.utcnow()
        self.sent_channels = []
        self.failed_channels = []
    
    def _generate_id(self) -> str:
        """生成通知ID"""
        import uuid
        return str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type.value,
            "title": self.title,
            "message": self.message,
            "priority": self.priority.value,
            "target_audience": self.target_audience,
            "data": self.data,
            "channels": [ch.value for ch in self.channels],
            "created_at": self.created_at.isoformat(),
            "sent_channels": [ch.value for ch in self.sent_channels],
            "failed_channels": [ch.value for ch in self.failed_channels]
        }


class NotificationAdapter(ABC):
    """通知适配器抽象基类"""
    
    @abstractmethod
    async def send(self, message: NotificationMessage) -> bool:
        """发送通知"""
        pass
    
    @abstractmethod
    def get_channel(self) -> NotificationChannel:
        """获取渠道类型"""
        pass


class LogNotificationAdapter(NotificationAdapter):
    """日志通知适配器"""
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.LOG
    
    async def send(self, message: NotificationMessage) -> bool:
        """发送日志通知"""
        try:
            log_level = self._get_log_level(message.priority)
            log_message = f"📢 {message.title}: {message.message}"
            
            if message.data:
                log_message += f" | 数据: {json.dumps(message.data, ensure_ascii=False)}"
            
            # 根据优先级选择日志级别
            if log_level == "ERROR":
                logger.error(log_message)
            elif log_level == "WARNING":
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
            return True
            
        except Exception as e:
            logger.error(f"日志通知发送失败: {str(e)}")
            return False
    
    def _get_log_level(self, priority: NotificationPriority) -> str:
        """根据优先级获取日志级别"""
        level_map = {
            NotificationPriority.LOW: "INFO",
            NotificationPriority.NORMAL: "INFO",
            NotificationPriority.HIGH: "WARNING",
            NotificationPriority.URGENT: "ERROR"
        }
        return level_map.get(priority, "INFO")


class WebSocketNotificationAdapter(NotificationAdapter):
    """WebSocket通知适配器"""
    
    def __init__(self):
        self.connections = {}  # 存储WebSocket连接
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.WEBSOCKET
    
    async def send(self, message: NotificationMessage) -> bool:
        """发送WebSocket通知"""
        try:
            if not settings.enable_websocket_notifications:
                logger.debug("WebSocket通知已禁用")
                return False
            
            # 构建WebSocket消息
            ws_message = {
                "type": "notification",
                "notification": message.to_dict()
            }
            
            # 发送给目标受众
            sent_count = await self._broadcast_to_audience(
                message.target_audience, 
                ws_message
            )
            
            logger.info(f"WebSocket通知发送完成: {message.id}, 发送给 {sent_count} 个连接")
            return sent_count > 0
            
        except Exception as e:
            logger.error(f"WebSocket通知发送失败: {str(e)}")
            return False
    
    async def _broadcast_to_audience(self, audience: str, message: Dict[str, Any]) -> int:
        """向目标受众广播消息"""
        sent_count = 0
        
        # 这里应该实现实际的WebSocket广播逻辑
        # 根据audience筛选连接并发送消息
        logger.info(f"模拟WebSocket广播: 受众={audience}, 消息={message['notification']['title']}")
        sent_count = 1  # 模拟发送成功
        
        return sent_count


class DatabaseNotificationAdapter(NotificationAdapter):
    """数据库通知适配器"""
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.DATABASE
    
    async def send(self, message: NotificationMessage) -> bool:
        """保存通知到数据库"""
        try:
            # 这里应该实现数据库保存逻辑
            notification_record = {
                "id": message.id,
                "type": message.type.value,
                "title": message.title,
                "message": message.message,
                "priority": message.priority.value,
                "target_audience": message.target_audience,
                "data": json.dumps(message.data),
                "created_at": message.created_at,
                "status": "sent"
            }
            
            logger.info(f"模拟保存通知记录到数据库: {message.id}")
            # 实际实现：await database.save_notification(notification_record)
            
            return True
            
        except Exception as e:
            logger.error(f"数据库通知保存失败: {str(e)}")
            return False


class PushNotificationAdapter(NotificationAdapter):
    """推送通知适配器（预留第三方推送服务）"""
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.PUSH
    
    async def send(self, message: NotificationMessage) -> bool:
        """发送推送通知"""
        try:
            if not settings.enable_push_notifications:
                logger.debug("推送通知已禁用")
                return False
            
            # 这里可以集成第三方推送服务
            # 如极光推送、个推、Firebase等
            push_data = {
                "title": message.title,
                "body": message.message,
                "data": message.data,
                "priority": message.priority.value
            }
            
            logger.info(f"模拟推送通知: {message.id}, 数据: {push_data}")
            # 实际实现：await push_service.send(push_data)
            
            return True
            
        except Exception as e:
            logger.error(f"推送通知发送失败: {str(e)}")
            return False


class NotificationService:
    """统一通知服务"""
    
    def __init__(self):
        self.adapters: Dict[NotificationChannel, NotificationAdapter] = {}
        self._register_adapters()
    
    def _register_adapters(self):
        """注册通知适配器"""
        adapters = [
            LogNotificationAdapter(),
            WebSocketNotificationAdapter(),
            DatabaseNotificationAdapter(),
            PushNotificationAdapter()
        ]
        
        for adapter in adapters:
            self.adapters[adapter.get_channel()] = adapter
        
        logger.info(f"通知适配器注册完成: {list(self.adapters.keys())}")
    
    async def send_notification(self, message: NotificationMessage) -> Dict[str, Any]:
        """发送通知"""
        logger.info(f"开始发送通知: {message.id}, 类型: {message.type.value}, 渠道: {[ch.value for ch in message.channels]}")
        
        results = {}
        
        for channel in message.channels:
            adapter = self.adapters.get(channel)
            if not adapter:
                logger.warning(f"未找到通知适配器: {channel.value}")
                results[channel.value] = {"success": False, "error": "适配器未找到"}
                message.failed_channels.append(channel)
                continue
            
            try:
                success = await adapter.send(message)
                results[channel.value] = {"success": success}
                
                if success:
                    message.sent_channels.append(channel)
                else:
                    message.failed_channels.append(channel)
                    
            except Exception as e:
                logger.error(f"通知发送异常: {channel.value}, 错误: {str(e)}")
                results[channel.value] = {"success": False, "error": str(e)}
                message.failed_channels.append(channel)
        
        # 记录发送结果
        success_count = len(message.sent_channels)
        total_count = len(message.channels)
        
        logger.info(f"通知发送完成: {message.id}, 成功: {success_count}/{total_count}")
        
        return {
            "notification_id": message.id,
            "success_count": success_count,
            "total_count": total_count,
            "results": results,
            "sent_channels": [ch.value for ch in message.sent_channels],
            "failed_channels": [ch.value for ch in message.failed_channels]
        }
    
    async def send_new_order_notification(self, order_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送新订单通知"""
        message = NotificationMessage(
            notification_type=NotificationType.NEW_ORDER,
            title="新订单通知",
            message=f"新的备料任务已到达：订单 {order_id}",
            priority=NotificationPriority.HIGH,
            target_audience="kitchen_staff",
            data=order_data,
            channels=[
                NotificationChannel.WEBSOCKET,
                NotificationChannel.LOG,
                NotificationChannel.DATABASE
            ]
        )
        
        return await self.send_notification(message)
    
    async def send_pickup_ready_notification(self, order_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送备料完成通知"""
        message = NotificationMessage(
            notification_type=NotificationType.PICKUP_READY,
            title="备料完成通知",
            message=f"订单 {order_id} 的原料已备好，可以领取",
            priority=NotificationPriority.NORMAL,
            target_audience="downstream_staff",
            data=order_data,
            channels=[
                NotificationChannel.WEBSOCKET,
                NotificationChannel.LOG,
                NotificationChannel.DATABASE
            ]
        )
        
        return await self.send_notification(message)


# 全局通知服务实例
_notification_service = None

def get_notification_service() -> NotificationService:
    """获取通知服务实例（单例模式）"""
    global _notification_service
    if _notification_service is None:
        _notification_service = NotificationService()
    return _notification_service
