"""
订单状态机验证器
实现严格的状态转换验证逻辑，符合PRD要求
"""

from typing import Dict, List, Optional
from models.order import OrderStatus
from utils.logger import get_logger

logger = get_logger(__name__)


class OrderStateMachine:
    """订单状态机 - 管理订单状态转换逻辑"""
    
    # 定义有效的状态转换规则
    VALID_TRANSITIONS: Dict[OrderStatus, List[OrderStatus]] = {
        OrderStatus.PENDING_PAYMENT: [
            OrderStatus.PAID, 
            OrderStatus.CANCELLED
        ],
        OrderStatus.PAID: [
            OrderStatus.PROCESSING, 
            OrderStatus.CANCELLED
        ],
        OrderStatus.PROCESSING: [
            OrderStatus.READY_FOR_PICKUP, 
            OrderStatus.CANCELLED
        ],
        OrderStatus.READY_FOR_PICKUP: [
            OrderStatus.COMPLETED
        ],
        OrderStatus.COMPLETED: [],  # 终态，不能再转换
        OrderStatus.CANCELLED: []   # 终态，不能再转换
    }
    
    @classmethod
    def is_valid_transition(cls, from_status: str, to_status: str) -> bool:
        """
        验证状态转换是否有效
        
        Args:
            from_status: 当前状态
            to_status: 目标状态
            
        Returns:
            bool: 转换是否有效
        """
        try:
            # 转换为枚举类型
            from_enum = OrderStatus(from_status)
            to_enum = OrderStatus(to_status)
            
            # 检查是否在有效转换列表中
            valid_targets = cls.VALID_TRANSITIONS.get(from_enum, [])
            is_valid = to_enum in valid_targets
            
            logger.info(f"状态转换验证: {from_status} -> {to_status}, 结果: {is_valid}")
            return is_valid
            
        except ValueError as e:
            logger.error(f"无效的状态值: {e}")
            return False
    
    @classmethod
    def get_valid_next_statuses(cls, current_status: str) -> List[str]:
        """
        获取当前状态的所有有效下一状态
        
        Args:
            current_status: 当前状态
            
        Returns:
            List[str]: 有效的下一状态列表
        """
        try:
            current_enum = OrderStatus(current_status)
            valid_next = cls.VALID_TRANSITIONS.get(current_enum, [])
            return [status.value for status in valid_next]
        except ValueError:
            return []
    
    @classmethod
    def get_transition_error_message(cls, from_status: str, to_status: str) -> str:
        """
        获取状态转换错误信息
        
        Args:
            from_status: 当前状态
            to_status: 目标状态
            
        Returns:
            str: 错误信息
        """
        valid_next = cls.get_valid_next_statuses(from_status)
        
        if not valid_next:
            return f"订单状态 '{from_status}' 已是终态，无法进行任何状态转换"
        
        return (
            f"无效的状态转换：无法从 '{from_status}' 直接变为 '{to_status}'。"
            f"有效的下一状态为: {', '.join(valid_next)}"
        )
    
    @classmethod
    def validate_status_update_for_role(cls, current_status: str, target_status: str, user_role: str) -> tuple[bool, str]:
        """
        根据用户角色验证状态更新权限
        
        Args:
            current_status: 当前状态
            target_status: 目标状态
            user_role: 用户角色
            
        Returns:
            tuple[bool, str]: (是否有权限, 错误信息)
        """
        # 首先验证状态转换是否有效
        if not cls.is_valid_transition(current_status, target_status):
            return False, cls.get_transition_error_message(current_status, target_status)
        
        # 根据角色验证权限
        if user_role == "staff":
            # 员工只能进行备料相关的状态转换
            allowed_transitions = [
                (OrderStatus.PAID, OrderStatus.PROCESSING),
                (OrderStatus.PROCESSING, OrderStatus.READY_FOR_PICKUP),
                (OrderStatus.READY_FOR_PICKUP, OrderStatus.COMPLETED)
            ]
            
            try:
                current_enum = OrderStatus(current_status)
                target_enum = OrderStatus(target_status)
                
                if (current_enum, target_enum) not in allowed_transitions:
                    return False, f"员工角色无权限进行此状态转换: {current_status} -> {target_status}"
                    
            except ValueError:
                return False, "无效的状态值"
        
        elif user_role == "admin":
            # 管理员可以进行所有有效的状态转换
            pass
        
        else:
            return False, f"角色 '{user_role}' 无权限更新订单状态"
        
        return True, ""


class OrderWorkflowHelper:
    """订单工作流助手 - 提供工作流相关的辅助方法"""
    
    @staticmethod
    def get_status_display_name(status: str) -> str:
        """获取状态的中文显示名称"""
        status_names = {
            OrderStatus.PENDING_PAYMENT: "待支付",
            OrderStatus.PAID: "已支付",
            OrderStatus.PROCESSING: "备料中",
            OrderStatus.READY_FOR_PICKUP: "备料完成",
            OrderStatus.COMPLETED: "已完成",
            OrderStatus.CANCELLED: "已取消"
        }
        
        try:
            status_enum = OrderStatus(status)
            return status_names.get(status_enum, status)
        except ValueError:
            return status
    
    @staticmethod
    def is_active_for_staff(status: str) -> bool:
        """判断订单是否为员工的活动任务"""
        try:
            status_enum = OrderStatus(status)
            return status_enum in [OrderStatus.PAID, OrderStatus.PROCESSING]
        except ValueError:
            return False
    
    @staticmethod
    def requires_video_recording(from_status: str, to_status: str) -> bool:
        """判断状态转换是否需要视频录制"""
        try:
            from_enum = OrderStatus(from_status)
            to_enum = OrderStatus(to_status)
            
            # 开始备料时需要开始录制
            if from_enum == OrderStatus.PAID and to_enum == OrderStatus.PROCESSING:
                return True
                
            return False
        except ValueError:
            return False
