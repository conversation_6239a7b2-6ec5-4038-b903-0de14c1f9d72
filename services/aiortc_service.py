import asyncio
import json
import logging
import uuid
from typing import Dict, Optional, Set
from datetime import datetime

from aiortc import RTCPeerConnection, RTCSessionDescription, RTCIceCandidate
from aiortc.contrib.media import MediaPlayer, MediaRecorder
from aiortc.contrib.signaling import BYE

from utils.logger import get_logger

logger = get_logger(__name__)

# 设置aiortc日志级别
logging.getLogger("aiortc").setLevel(logging.WARNING)
logging.getLogger("aioice").setLevel(logging.WARNING)


class MediaSource:
    """媒体源管理"""
    
    def __init__(self, video_path: str = None, audio_path: str = None):
        self.video_path = video_path or r"C:\Users\<USER>\Downloads\Video\test.mp4"
        self.audio_path = audio_path or r"C:\Users\<USER>\Downloads\Video\test.wav"
        self.player = None
    
    def create_player(self, media_type: str = "both") -> Optional[MediaPlayer]:
        """创建媒体播放器"""
        try:
            logger.info(f"Creating media player for type: {media_type}")
            logger.info(f"Video path: {self.video_path}")
            logger.info(f"Audio path: {self.audio_path}")

            if media_type == "video":
                logger.info("Creating video-only player")
                self.player = MediaPlayer(self.video_path)
            elif media_type == "audio":
                logger.info("Creating audio-only player")
                self.player = MediaPlayer(self.audio_path)
            elif media_type == "both":
                # 尝试创建包含音视频的播放器
                logger.info("Creating combined audio/video player")
                try:
                    self.player = MediaPlayer(self.video_path)
                    logger.info("Successfully created player from video file")
                except Exception as e:
                    logger.warning(f"Failed to create player from video file: {str(e)}")
                    # 如果视频文件没有音频，分别处理
                    self.player = MediaPlayer(self.video_path)
                    logger.info("Created fallback video player")
            else:
                logger.warning(f"Unknown media type: {media_type}")
                return None

            # 检查播放器的轨道
            if self.player:
                has_video = hasattr(self.player, 'video') and self.player.video is not None
                has_audio = hasattr(self.player, 'audio') and self.player.audio is not None
                logger.info(f"Player created - Video: {has_video}, Audio: {has_audio}")

            logger.info(f"Successfully created media player for {media_type}")
            return self.player

        except Exception as e:
            logger.error(f"Failed to create media player: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def stop(self):
        """停止媒体播放器"""
        if self.player:
            try:
                # aiortc的MediaPlayer没有公共的stop方法
                # 直接设置为None，让垃圾回收处理
                logger.info("Media player cleaned up by setting to None")
                self.player = None
                logger.info("Media player stopped and cleaned up")

            except Exception as e:
                logger.error(f"Error stopping media player: {str(e)}")
                # 即使出错也要清理引用
                self.player = None


class WebRTCConnection:
    """WebRTC连接管理"""
    
    def __init__(self, connection_id: str, is_caller: bool = False):
        try:
            self.connection_id = connection_id
            self.is_caller = is_caller

            logger.info(f"Initializing WebRTC connection: {connection_id}")

            # 创建RTCPeerConnection
            self.pc = RTCPeerConnection()
            logger.info(f"RTCPeerConnection created for {connection_id}")

            # 创建媒体源
            self.media_source = MediaSource()
            logger.info(f"MediaSource created for {connection_id}")

            self.recorder = None
            self.data_channel = None
            self.connected = False
            self.created_at = datetime.now()

            # 设置事件处理器
            self.setup_event_handlers()
            logger.info(f"WebRTC connection {connection_id} initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize WebRTC connection {connection_id}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def setup_event_handlers(self):
        """设置WebRTC事件处理器"""
        
        @self.pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"Connection {self.connection_id} state: {self.pc.connectionState}")
            if self.pc.connectionState == "connected":
                self.connected = True
            elif self.pc.connectionState in ["failed", "closed"]:
                self.connected = False
        
        @self.pc.on("datachannel")
        def on_datachannel(channel):
            logger.info(f"Data channel {channel.label} created")
            
            @channel.on("message")
            def on_message(message):
                logger.info(f"Received data channel message: {message}")
        
        @self.pc.on("track")
        def on_track(track):
            logger.info(f"Received track: {track.kind}")
            
            if track.kind == "video":
                # 可以在这里处理接收到的视频流
                logger.info("Video track received")
            elif track.kind == "audio":
                # 可以在这里处理接收到的音频流
                logger.info("Audio track received")
    
    async def add_media_tracks(self, media_type: str = "both"):
        """添加媒体轨道"""
        try:
            logger.info(f"Adding media tracks for {self.connection_id}, type: {media_type}")

            player = self.media_source.create_player(media_type)
            if not player:
                logger.warning(f"Failed to create media player for {self.connection_id}")
                return False

            logger.info(f"Media player created for {self.connection_id}")

            tracks_added = 0

            if media_type in ["video", "both"] and player.video:
                self.pc.addTrack(player.video)
                tracks_added += 1
                logger.info(f"Video track added for {self.connection_id}")
            else:
                logger.warning(f"No video track available for {self.connection_id}")

            if media_type in ["audio", "both"] and player.audio:
                self.pc.addTrack(player.audio)
                tracks_added += 1
                logger.info(f"Audio track added for {self.connection_id}")
            else:
                logger.warning(f"No audio track available for {self.connection_id}")

            logger.info(f"Total {tracks_added} tracks added for {self.connection_id}")
            return tracks_added > 0

        except Exception as e:
            logger.error(f"Error adding media tracks for {self.connection_id}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    async def create_offer(self) -> Optional[RTCSessionDescription]:
        """创建Offer"""
        try:
            # 添加数据通道
            self.data_channel = self.pc.createDataChannel("chat")
            
            # 添加媒体轨道
            await self.add_media_tracks()
            
            # 创建offer
            offer = await self.pc.createOffer()
            await self.pc.setLocalDescription(offer)
            
            logger.info(f"Created offer for connection {self.connection_id}")
            return offer
            
        except Exception as e:
            logger.error(f"Error creating offer: {str(e)}")
            return None
    
    async def create_answer(self, offer: RTCSessionDescription) -> Optional[RTCSessionDescription]:
        """创建Answer"""
        try:
            logger.error(f"严重 CRITICAL: create_answer called for {self.connection_id} - THIS SHOULD NOT HAPPEN IN PURE RELAY MODE!")
            import traceback
            logger.error(f"严重 Call stack: {traceback.format_stack()}")
            logger.info(f"Creating answer for connection {self.connection_id}")

            # 设置远程描述
            logger.info(f"Setting remote description for {self.connection_id}")
            await self.pc.setRemoteDescription(offer)
            logger.info(f"Remote description set successfully for {self.connection_id}")

            # 添加媒体轨道
            logger.info(f"Adding media tracks for {self.connection_id}")
            media_added = await self.add_media_tracks()
            if media_added:
                logger.info(f"Media tracks added successfully for {self.connection_id}")
            else:
                logger.warning(f"Failed to add media tracks for {self.connection_id}")

            # 创建answer
            logger.info(f"Creating answer SDP for {self.connection_id}")
            answer = await self.pc.createAnswer()
            logger.info(f"Answer SDP created for {self.connection_id}")

            logger.info(f"Setting local description for {self.connection_id}")
            await self.pc.setLocalDescription(answer)
            logger.info(f"Local description set successfully for {self.connection_id}")

            logger.info(f"Answer creation completed for connection {self.connection_id}")
            return answer

        except Exception as e:
            logger.error(f"Error creating answer for {self.connection_id}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    async def set_remote_description(self, description: RTCSessionDescription):
        """设置远程描述"""
        try:
            await self.pc.setRemoteDescription(description)
            logger.info(f"Set remote description for connection {self.connection_id}")
        except Exception as e:
            logger.error(f"Error setting remote description: {str(e)}")
    
    async def add_ice_candidate(self, candidate: RTCIceCandidate):
        """添加ICE候选"""
        try:
            await self.pc.addIceCandidate(candidate)
            logger.info(f"Added ICE candidate for connection {self.connection_id}")
        except Exception as e:
            logger.error(f"Error adding ICE candidate: {str(e)}")
    
    async def send_data_message(self, message: str):
        """发送数据通道消息"""
        try:
            if self.data_channel and self.data_channel.readyState == "open":
                self.data_channel.send(message)
                logger.info(f"Sent data message: {message}")
                return True
            else:
                logger.warning("Data channel not ready")
                return False
        except Exception as e:
            logger.error(f"Error sending data message: {str(e)}")
            return False
    
    async def close(self):
        """关闭连接"""
        try:
            # 停止媒体源
            self.media_source.stop()
            
            # 关闭数据通道
            if self.data_channel:
                self.data_channel.close()
            
            # 关闭录制器
            if self.recorder:
                await self.recorder.stop()
            
            # 关闭PeerConnection
            await self.pc.close()
            
            self.connected = False
            logger.info(f"Connection {self.connection_id} closed")
            
        except Exception as e:
            logger.error(f"Error closing connection: {str(e)}")


class AiortcWebRTCService:
    """基于aiortc的WebRTC服务"""
    
    def __init__(self):
        self.connections: Dict[str, WebRTCConnection] = {}
        self.sessions: Dict[str, Dict] = {}
    
    def create_connection(self, session_id: str, participant_type: str, is_caller: bool = False) -> str:
        """创建WebRTC连接"""
        try:
            connection_id = f"{session_id}_{participant_type}"
            logger.info(f"Creating WebRTC connection: {connection_id} (caller: {is_caller})")

            if connection_id in self.connections:
                logger.warning(f"Connection {connection_id} already exists")
                return connection_id

            # 创建WebRTC连接
            logger.info(f"Initializing WebRTCConnection for {connection_id}")
            connection = WebRTCConnection(connection_id, is_caller)
            self.connections[connection_id] = connection
            logger.info(f"WebRTCConnection {connection_id} added to connections")

            # 更新会话信息
            if session_id not in self.sessions:
                logger.info(f"Creating new session: {session_id}")
                self.sessions[session_id] = {
                    "session_id": session_id,
                    "participants": {},
                    "created_at": datetime.now()
                }

            self.sessions[session_id]["participants"][participant_type] = {
                "connection_id": connection_id,
                "connected": False,
                "joined_at": datetime.now()
            }

            logger.info(f"Successfully created WebRTC connection: {connection_id}")
            return connection_id

        except Exception as e:
            logger.error(f"Failed to create WebRTC connection for session {session_id}, participant {participant_type}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    async def handle_offer(self, connection_id: str, offer_data: dict) -> Optional[dict]:
        """处理Offer - 在纯中继模式下禁用"""
        logger.error(f"严重 CRITICAL: handle_offer called for {connection_id} - THIS SHOULD NOT HAPPEN IN PURE RELAY MODE!")
        logger.error(f"严重 Offer data: {offer_data}")
        import traceback
        logger.error(f"严重 Call stack: {traceback.format_stack()}")
        return None  # 在纯中继模式下不自动处理Offer
    
    async def handle_answer(self, connection_id: str, answer_data: dict) -> bool:
        """处理Answer"""
        try:
            if connection_id not in self.connections:
                logger.error(f"Connection {connection_id} not found")
                return False
            
            connection = self.connections[connection_id]
            
            # 创建RTCSessionDescription
            answer = RTCSessionDescription(
                sdp=answer_data["sdp"],
                type=answer_data["type"]
            )
            
            # 设置远程描述
            await connection.set_remote_description(answer)
            return True
            
        except Exception as e:
            logger.error(f"Error handling answer: {str(e)}")
            return False
    
    async def handle_ice_candidate(self, connection_id: str, candidate_data: dict) -> bool:
        """处理ICE候选"""
        try:
            if connection_id not in self.connections:
                logger.error(f"Connection {connection_id} not found")
                return False

            connection = self.connections[connection_id]

            # 解析candidate字符串
            candidate_str = candidate_data.get("candidate", "")
            if not candidate_str:
                logger.warning("Empty candidate string")
                return False

            # 创建RTCIceCandidate
            candidate = RTCIceCandidate.from_sdp(candidate_str)
            candidate.sdpMid = candidate_data.get("sdpMid")
            candidate.sdpMLineIndex = candidate_data.get("sdpMLineIndex")

            # 添加ICE候选
            await connection.add_ice_candidate(candidate)
            return True

        except Exception as e:
            logger.error(f"Error handling ICE candidate: {str(e)}")
            return False
    
    async def create_offer(self, connection_id: str) -> Optional[dict]:
        """创建Offer - 在纯中继模式下禁用"""
        logger.info(f"DEBUG: create_offer called for {connection_id} - DISABLED in pure relay mode")
        return None  # 在纯中继模式下不自动创建Offer
    
    async def send_data_message(self, connection_id: str, message: str) -> bool:
        """发送数据消息"""
        try:
            if connection_id not in self.connections:
                logger.error(f"Connection {connection_id} not found")
                return False
            
            connection = self.connections[connection_id]
            return await connection.send_data_message(message)
            
        except Exception as e:
            logger.error(f"Error sending data message: {str(e)}")
            return False
    
    def get_connection_status(self, connection_id: str) -> dict:
        """获取连接状态"""
        if connection_id not in self.connections:
            return {"connected": False, "error": "Connection not found"}
        
        connection = self.connections[connection_id]
        return {
            "connected": connection.connected,
            "connection_state": connection.pc.connectionState,
            "ice_connection_state": connection.pc.iceConnectionState,
            "ice_gathering_state": connection.pc.iceGatheringState,
            "created_at": connection.created_at.isoformat()
        }
    
    def get_session_info(self, session_id: str) -> Optional[dict]:
        """获取会话信息"""
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id].copy()
        
        # 添加连接状态信息
        for participant_type, participant_info in session["participants"].items():
            connection_id = participant_info["connection_id"]
            if connection_id in self.connections:
                connection = self.connections[connection_id]
                participant_info["connected"] = connection.connected
                participant_info["connection_state"] = connection.pc.connectionState
        
        return session
    
    async def close_connection(self, connection_id: str):
        """关闭连接"""
        if connection_id in self.connections:
            connection = self.connections[connection_id]
            await connection.close()
            del self.connections[connection_id]
            logger.info(f"Closed connection: {connection_id}")
    
    async def close_session(self, session_id: str):
        """关闭会话"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            
            # 关闭所有连接
            for participant_info in session["participants"].values():
                connection_id = participant_info["connection_id"]
                await self.close_connection(connection_id)
            
            del self.sessions[session_id]
            logger.info(f"Closed session: {session_id}")


# 全局aiortc WebRTC服务实例
aiortc_webrtc_service = AiortcWebRTCService()
