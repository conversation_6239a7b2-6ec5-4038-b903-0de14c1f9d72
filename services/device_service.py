from sqlmodel import Session, select, and_, or_
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import uuid
import json
import secrets
import string
import qrcode
import io
import base64

from models.user import User
from models.user_device_binding import Device, UserDeviceBinding, DeviceBindingRequest, PermissionLevel
from utils.security import create_access_token
from utils.logger import get_logger

logger = get_logger(__name__)


class DeviceService:
    def __init__(self, session: Session):
        self.session = session
    
    def register_device(self, device_id: str, device_name: str, device_type: str) -> Optional[Device]:
        """Register a new device"""
        try:
            # Check if device already exists
            statement = select(Device).where(Device.device_id == device_id)
            existing_device = self.session.exec(statement).first()
            
            if existing_device:
                logger.warning(f"Device registration failed: device_id {device_id} already exists")
                return existing_device
            
            # Generate QR code data
            qr_data = json.dumps({
                "device_id": device_id,
                "timestamp": datetime.utcnow().isoformat(),
                "type": "device_binding"
            })
            
            # Create new device
            device = Device(
                device_id=device_id,
                device_name=device_name,
                device_type=device_type,
                binding_qr_code_data=qr_data,
                status="online"
            )
            
            self.session.add(device)
            self.session.commit()
            self.session.refresh(device)
            
            logger.info(f"Device {device_id} registered successfully")
            return device
        except Exception as e:
            logger.error(f"Error registering device {device_id}: {str(e)}")
            self.session.rollback()
            return None
    
    def update_heartbeat(self, device_id: str, status: str, **kwargs) -> bool:
        """Update device heartbeat"""
        try:
            statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(statement).first()
            
            if not device:
                logger.warning(f"Heartbeat update failed: device {device_id} not found")
                return False
            
            device.status = status
            device.last_heartbeat_at = datetime.utcnow()
            device.updated_at = datetime.utcnow()
            
            self.session.add(device)
            self.session.commit()
            
            logger.debug(f"Heartbeat updated for device {device_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating heartbeat for device {device_id}: {str(e)}")
            self.session.rollback()
            return False
    
    def bind_user_to_device(self, user_id: str, qr_code_data: str) -> Optional[Device]:
        """Bind user to device using QR code data"""
        try:
            # Parse QR code data
            qr_data = json.loads(qr_code_data)
            device_id = qr_data.get("device_id")

            if not device_id:
                logger.warning("Invalid QR code data: missing device_id")
                return None

            # Find device
            statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(statement).first()

            if not device:
                logger.warning(f"Device binding failed: device {device_id} not found")
                return None

            # Check if binding already exists
            binding_statement = select(UserDeviceBinding).where(
                and_(
                    UserDeviceBinding.user_id == int(user_id),
                    UserDeviceBinding.device_id == device_id,
                    UserDeviceBinding.is_active == True
                )
            )
            existing_binding = self.session.exec(binding_statement).first()

            if existing_binding:
                logger.info(f"User {user_id} already has access to device {device_id}")
                return device

            # Create new binding record
            binding = UserDeviceBinding(
                user_id=int(user_id),
                device_id=device_id,
                device_name=device.device_name,
                device_type=device.device_type,
                permission_level=PermissionLevel.CONTROL,
                is_active=True,
                is_approved=True
            )

            self.session.add(binding)
            self.session.commit()

            logger.info(f"User {user_id} bound to device {device_id}")
            return device
        except Exception as e:
            logger.error(f"Error binding user {user_id} to device: {str(e)}")
            self.session.rollback()
            return None
    
    def get_user_devices_legacy(self, user_id: str) -> List[Device]:
        """Get all devices accessible by user (legacy method for compatibility)"""
        try:
            statement = select(Device).join(UserDeviceBinding).where(
                and_(
                    UserDeviceBinding.user_id == int(user_id),
                    UserDeviceBinding.is_active == True,
                    UserDeviceBinding.is_approved == True
                )
            )
            devices = self.session.exec(statement).all()
            return list(devices)
        except Exception as e:
            logger.error(f"Error getting devices for user {user_id}: {str(e)}")
            return []

    def get_device_bound_users(self, device_id: str):
        """Get all users bound to a device"""
        try:
            from models.user import User

            statement = select(User, UserDeviceBinding).join(
                UserDeviceBinding, User.id == UserDeviceBinding.user_id
            ).where(
                and_(
                    UserDeviceBinding.device_id == device_id,
                    UserDeviceBinding.is_active == True,
                    UserDeviceBinding.is_approved == True
                )
            )

            results = self.session.exec(statement).all()
            return list(results)
        except Exception as e:
            logger.error(f"Error getting bound users for device {device_id}: {str(e)}")
            return []

    # ==================== 设备绑定功能 ====================

    def get_user_devices(self, user_id: int, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """获取用户绑定的设备列表"""
        try:
            statement = select(UserDeviceBinding).where(
                UserDeviceBinding.user_id == user_id
            )

            if not include_inactive:
                statement = statement.where(
                    UserDeviceBinding.is_active == True,
                    UserDeviceBinding.is_approved == True
                )

            bindings = self.session.exec(statement).all()

            devices = []
            for binding in bindings:
                device_info = {
                    "device_id": binding.device_id,
                    "device_name": binding.device_name,
                    "device_type": binding.device_type,
                    "device_location": binding.device_location,
                    "device_description": binding.device_description,
                    "permission_level": binding.permission_level,
                    "bound_at": binding.bound_at.isoformat(),
                    "last_accessed": binding.last_accessed.isoformat() if binding.last_accessed else None,
                    "is_expired": binding.is_expired,
                    "is_valid": binding.is_valid
                }
                devices.append(device_info)

            logger.info(f"获取用户 {user_id} 的设备列表: {len(devices)} 个设备")
            return devices

        except Exception as e:
            logger.error(f"获取用户设备列表失败: {str(e)}")
            return []

    def bind_device_to_user(self, user_id: int, device_id: str, binding_code: str,
                           permission_level: PermissionLevel = PermissionLevel.READ) -> Dict[str, Any]:
        """绑定设备到用户"""
        try:
            # 检查用户是否存在
            user_statement = select(User).where(User.id == user_id)
            user = self.session.exec(user_statement).first()
            if not user:
                return {"success": False, "message": "用户不存在"}

            # 检查设备是否存在
            device_statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(device_statement).first()
            if not device:
                return {"success": False, "message": "设备不存在"}

            # 检查是否已经绑定
            existing_binding = self.session.exec(
                select(UserDeviceBinding).where(
                    and_(
                        UserDeviceBinding.user_id == user_id,
                        UserDeviceBinding.device_id == device_id,
                        UserDeviceBinding.is_active == True
                    )
                )
            ).first()

            if existing_binding:
                return {"success": False, "message": "设备已经绑定到该用户"}

            # 创建绑定记录
            binding = UserDeviceBinding(
                user_id=user_id,
                device_id=device_id,
                device_name=device.device_name,
                device_type=device.device_type,
                permission_level=permission_level,
                bound_at=datetime.utcnow(),
                is_active=True,
                is_approved=True  # 简化流程，直接批准
            )

            self.session.add(binding)
            self.session.commit()
            self.session.refresh(binding)

            logger.info(f"用户 {user_id} 成功绑定设备 {device_id}")
            return {
                "success": True,
                "message": "设备绑定成功",
                "binding_id": binding.id
            }

        except Exception as e:
            logger.error(f"设备绑定失败: {str(e)}")
            self.session.rollback()
            return {"success": False, "message": f"绑定失败: {str(e)}"}

    def unbind_device_from_user(self, user_id: int, device_id: str) -> Dict[str, Any]:
        """解绑用户的设备"""
        try:
            binding = self.session.exec(
                select(UserDeviceBinding).where(
                    and_(
                        UserDeviceBinding.user_id == user_id,
                        UserDeviceBinding.device_id == device_id,
                        UserDeviceBinding.is_active == True
                    )
                )
            ).first()

            if not binding:
                return {"success": False, "message": "绑定关系不存在"}

            binding.is_active = False
            binding.unbound_at = datetime.utcnow()

            self.session.commit()

            logger.info(f"用户 {user_id} 成功解绑设备 {device_id}")
            return {"success": True, "message": "设备解绑成功"}

        except Exception as e:
            logger.error(f"设备解绑失败: {str(e)}")
            self.session.rollback()
            return {"success": False, "message": f"解绑失败: {str(e)}"}

    def check_user_device_permission(self, user_id: int, device_id: str,
                                   required_permission: PermissionLevel = PermissionLevel.READ) -> bool:
        """检查用户对设备的权限"""
        try:
            binding = self.session.exec(
                select(UserDeviceBinding).where(
                    and_(
                        UserDeviceBinding.user_id == user_id,
                        UserDeviceBinding.device_id == device_id,
                        UserDeviceBinding.is_active == True,
                        UserDeviceBinding.is_approved == True
                    )
                )
            ).first()

            if not binding or binding.is_expired:
                return False

            # 检查权限级别
            permission_levels = {
                PermissionLevel.READ: 1,
                PermissionLevel.CONTROL: 2,
                PermissionLevel.ADMIN: 3
            }

            user_level = permission_levels.get(binding.permission_level, 0)
            required_level = permission_levels.get(required_permission, 0)

            return user_level >= required_level

        except Exception as e:
            logger.error(f"权限检查失败: {str(e)}")
            return False

    # ==================== 设备绑定码功能 ====================

    def generate_binding_code(self, device_id: str, expires_minutes: int = 30) -> Optional[Dict[str, Any]]:
        """为设备生成绑定码"""
        try:
            # 检查设备是否存在
            device_statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(device_statement).first()

            if not device:
                logger.error(f"设备不存在: {device_id}")
                return None

            # 生成6位数字绑定码
            binding_code = ''.join(secrets.choice(string.digits) for _ in range(6))

            # 生成绑定链接
            binding_url = f"webrtc://bind?device_id={device_id}&code={binding_code}"

            # 生成二维码
            qr_code_data = self.generate_qr_code(binding_url)

            # 计算过期时间
            expires_at = datetime.utcnow() + timedelta(minutes=expires_minutes)

            # 更新设备的绑定码信息
            device.binding_code = binding_code
            device.binding_code_expires_at = expires_at
            device.binding_qr_code_data = qr_code_data

            self.session.commit()
            self.session.refresh(device)

            logger.info(f"为设备 {device_id} 生成绑定码: {binding_code}")

            return {
                "device_id": device_id,
                "device_name": device.device_name,
                "binding_code": binding_code,
                "binding_url": binding_url,
                "qr_code_data": qr_code_data,
                "expires_at": expires_at.isoformat(),
                "expires_minutes": expires_minutes
            }

        except Exception as e:
            logger.error(f"生成绑定码失败: {str(e)}")
            return None

    def generate_qr_code(self, data: str) -> str:
        """生成二维码图片的Base64编码"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)

            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为Base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{img_str}"

        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            return ""

    def verify_binding_code(self, device_id: str, binding_code: str) -> Dict[str, Any]:
        """验证设备绑定码"""
        try:
            device_statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(device_statement).first()

            if not device:
                return {"valid": False, "message": "设备不存在"}

            if not device.binding_code:
                return {"valid": False, "message": "设备未生成绑定码"}

            if device.binding_code != binding_code:
                return {"valid": False, "message": "绑定码错误"}

            # 检查是否过期
            if device.binding_code_expires_at and device.binding_code_expires_at < datetime.utcnow():
                return {"valid": False, "message": "绑定码已过期"}

            return {
                "valid": True,
                "message": "绑定码验证成功",
                "device_id": device_id,
                "device_name": device.device_name,
                "device_type": device.device_type
            }

        except Exception as e:
            logger.error(f"验证绑定码失败: {str(e)}")
            return {"valid": False, "message": f"验证失败: {str(e)}"}

    def bind_device_with_code(self, user_id: int, device_id: str, binding_code: str,
                             permission_level: PermissionLevel = PermissionLevel.READ) -> Dict[str, Any]:
        """使用绑定码绑定设备"""
        try:
            # 先验证绑定码
            verification_result = self.verify_binding_code(device_id, binding_code)
            if not verification_result["valid"]:
                return {"success": False, "message": verification_result["message"]}

            # 执行绑定
            binding_result = self.bind_device_to_user(user_id, device_id, binding_code, permission_level)

            if binding_result["success"]:
                # 清除绑定码（一次性使用）
                device_statement = select(Device).where(Device.device_id == device_id)
                device = self.session.exec(device_statement).first()
                if device:
                    device.binding_code = None
                    device.binding_code_expires_at = None
                    self.session.commit()

                logger.info(f"用户 {user_id} 使用绑定码成功绑定设备 {device_id}")

            return binding_result

        except Exception as e:
            logger.error(f"使用绑定码绑定设备失败: {str(e)}")
            return {"success": False, "message": f"绑定失败: {str(e)}"}

    def get_device_binding_info(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备绑定信息"""
        try:
            device_statement = select(Device).where(Device.device_id == device_id)
            device = self.session.exec(device_statement).first()

            if not device:
                return None

            return {
                "device_id": device_id,
                "device_name": device.device_name,
                "device_type": device.device_type,
                "has_binding_code": bool(device.binding_code),
                "binding_code_expires_at": device.binding_code_expires_at.isoformat() if device.binding_code_expires_at else None,
                "qr_code_data": device.binding_qr_code_data
            }

        except Exception as e:
            logger.error(f"获取设备绑定信息失败: {str(e)}")
            return None
