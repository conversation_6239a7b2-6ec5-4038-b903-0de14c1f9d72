"""
订单相关的异步任务
处理订单履约流程中的业务逻辑任务
"""

from celery import shared_task
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from utils.logger import get_logger

logger = get_logger(__name__)


@shared_task(bind=True, max_retries=3)
def process_payment_confirmation(self, order_id: str, payment_data: Dict[str, Any]):
    """
    处理支付确认的后续业务逻辑
    
    Args:
        order_id: 订单ID
        payment_data: 支付数据
    """
    try:
        logger.info(f"开始处理支付确认后续任务: {order_id}")
        
        # 1. 验证支付数据的完整性
        if not _validate_payment_data(payment_data):
            raise ValueError("支付数据验证失败")
        
        # 2. 更新支付记录
        if not _update_payment_record(order_id, payment_data):
            raise Exception("更新支付记录失败")
        
        # 3. 触发库存检查（如果需要）
        inventory_check_result = _check_inventory_for_order(order_id)
        if not inventory_check_result["success"]:
            logger.warning(f"库存检查警告: {order_id}, {inventory_check_result['message']}")
        
        # 4. 生成备料清单
        preparation_list = _generate_preparation_list(order_id)
        if preparation_list:
            logger.info(f"备料清单生成成功: {order_id}, 项目数: {len(preparation_list)}")
        
        # 5. 预估备料时间
        estimated_time = _estimate_preparation_time(preparation_list)
        logger.info(f"预估备料时间: {order_id}, {estimated_time} 分钟")
        
        logger.info(f"支付确认后续处理完成: {order_id}")
        
        return {
            "success": True,
            "order_id": order_id,
            "preparation_items": len(preparation_list) if preparation_list else 0,
            "estimated_time_minutes": estimated_time,
            "message": "支付确认处理完成"
        }
        
    except Exception as e:
        logger.error(f"支付确认处理失败: {order_id}, 错误: {str(e)}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(exc=e, countdown=60)
        
        return {
            "success": False,
            "order_id": order_id,
            "error": str(e)
        }


@shared_task
def monitor_order_timeout(order_id: str, status: str, timeout_minutes: int = 30):
    """
    监控订单超时
    
    如果订单在指定时间内没有状态变更，发送提醒
    
    Args:
        order_id: 订单ID
        status: 当前状态
        timeout_minutes: 超时时间（分钟）
    """
    try:
        logger.info(f"开始监控订单超时: {order_id}, 状态: {status}, 超时: {timeout_minutes}分钟")
        
        # 检查订单当前状态
        current_order_info = _get_current_order_status(order_id)
        
        if not current_order_info:
            logger.error(f"无法获取订单状态: {order_id}")
            return {"success": False, "error": "订单不存在"}
        
        current_status = current_order_info.get("status")
        
        # 如果状态已经变更，说明没有超时
        if current_status != status:
            logger.info(f"订单状态已变更: {order_id}, 从 {status} 到 {current_status}, 取消超时监控")
            return {
                "success": True,
                "message": "订单状态已变更，无需超时提醒",
                "old_status": status,
                "current_status": current_status
            }
        
        # 检查是否真的超时了
        last_updated = current_order_info.get("updated_at")
        if last_updated:
            time_diff = datetime.utcnow() - datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            if time_diff.total_seconds() < timeout_minutes * 60:
                logger.info(f"订单未超时: {order_id}, 已过时间: {time_diff.total_seconds()/60:.1f}分钟")
                return {"success": True, "message": "订单未超时"}
        
        # 发送超时提醒
        timeout_notification = {
            "type": "order_timeout",
            "order_id": order_id,
            "status": status,
            "timeout_minutes": timeout_minutes,
            "message": f"订单 {order_id} 在状态 {status} 已超时 {timeout_minutes} 分钟，请检查处理进度",
            "timestamp": datetime.utcnow().isoformat(),
            "priority": "high"
        }
        
        # 发送超时通知
        _send_timeout_notification(timeout_notification)
        
        logger.warning(f"订单超时提醒: {order_id}, 状态: {status}, 超时: {timeout_minutes}分钟")
        
        return {
            "success": True,
            "message": "超时提醒已发送",
            "order_id": order_id,
            "timeout_status": status
        }
        
    except Exception as e:
        logger.error(f"订单超时监控失败: {order_id}, 错误: {str(e)}")
        return {"success": False, "error": str(e)}


@shared_task
def cleanup_completed_orders():
    """
    清理已完成的订单数据
    
    定期任务，清理超过一定时间的已完成订单的临时数据
    """
    try:
        logger.info("开始清理已完成订单数据")
        
        # 获取需要清理的订单列表（例如：7天前完成的订单）
        cutoff_date = datetime.utcnow() - timedelta(days=7)
        orders_to_cleanup = _get_orders_for_cleanup(cutoff_date)
        
        cleaned_count = 0
        for order_id in orders_to_cleanup:
            if _cleanup_order_temp_data(order_id):
                cleaned_count += 1
        
        logger.info(f"订单数据清理完成: 处理 {len(orders_to_cleanup)} 个订单, 成功清理 {cleaned_count} 个")
        
        return {
            "success": True,
            "total_orders": len(orders_to_cleanup),
            "cleaned_orders": cleaned_count,
            "message": "订单数据清理完成"
        }
        
    except Exception as e:
        logger.error(f"订单数据清理失败: {str(e)}")
        return {"success": False, "error": str(e)}


# 辅助函数
def _validate_payment_data(payment_data: Dict[str, Any]) -> bool:
    """验证支付数据"""
    required_fields = ["payment_transaction_id", "payment_method"]
    return all(field in payment_data for field in required_fields)


def _update_payment_record(order_id: str, payment_data: Dict[str, Any]) -> bool:
    """更新支付记录"""
    try:
        logger.info(f"更新支付记录: {order_id}")
        # 这里应该实现数据库更新逻辑
        return True
    except Exception as e:
        logger.error(f"更新支付记录失败: {order_id}, 错误: {str(e)}")
        return False


def _check_inventory_for_order(order_id: str) -> Dict[str, Any]:
    """检查订单库存"""
    try:
        # 这里应该实现库存检查逻辑
        logger.info(f"检查订单库存: {order_id}")
        return {"success": True, "message": "库存充足"}
    except Exception as e:
        logger.error(f"库存检查失败: {order_id}, 错误: {str(e)}")
        return {"success": False, "message": str(e)}


def _generate_preparation_list(order_id: str) -> Optional[list]:
    """生成备料清单"""
    try:
        # 这里应该根据订单内容生成备料清单
        logger.info(f"生成备料清单: {order_id}")
        return [
            {"item": "示例原料1", "quantity": 2, "unit": "份"},
            {"item": "示例原料2", "quantity": 1, "unit": "个"}
        ]
    except Exception as e:
        logger.error(f"生成备料清单失败: {order_id}, 错误: {str(e)}")
        return None


def _estimate_preparation_time(preparation_list: list) -> int:
    """预估备料时间（分钟）"""
    if not preparation_list:
        return 0
    
    # 简单的时间估算逻辑
    base_time = 5  # 基础时间5分钟
    item_time = len(preparation_list) * 3  # 每个项目3分钟
    
    return base_time + item_time


def _get_current_order_status(order_id: str) -> Optional[Dict[str, Any]]:
    """获取当前订单状态"""
    try:
        # 这里应该从数据库获取订单状态
        logger.info(f"获取订单状态: {order_id}")
        return {
            "status": "processing",
            "updated_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"获取订单状态失败: {order_id}, 错误: {str(e)}")
        return None


def _send_timeout_notification(notification_data: Dict[str, Any]):
    """发送超时通知"""
    try:
        logger.warning(f"⏰ 超时提醒: {notification_data['message']}")
        # 这里可以集成实际的通知系统
    except Exception as e:
        logger.error(f"发送超时通知失败: {str(e)}")


def _get_orders_for_cleanup(cutoff_date: datetime) -> list:
    """获取需要清理的订单列表"""
    try:
        # 这里应该从数据库查询需要清理的订单
        logger.info(f"查询需要清理的订单，截止日期: {cutoff_date}")
        return []  # 返回空列表作为示例
    except Exception as e:
        logger.error(f"查询清理订单失败: {str(e)}")
        return []


def _cleanup_order_temp_data(order_id: str) -> bool:
    """清理订单临时数据"""
    try:
        logger.info(f"清理订单临时数据: {order_id}")
        # 这里应该实现清理逻辑，如删除临时文件、缓存等
        return True
    except Exception as e:
        logger.error(f"清理订单数据失败: {order_id}, 错误: {str(e)}")
        return False
