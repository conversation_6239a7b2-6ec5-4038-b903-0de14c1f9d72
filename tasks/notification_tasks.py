"""
通知任务模块
处理订单履约流程中的各种异步通知
符合PRD要求的备料工作流通知
"""

from celery import shared_task
from typing import Optional, Dict, Any
import json
from datetime import datetime

from utils.logger import get_logger
from config import settings
from services.notification_service import get_notification_service

logger = get_logger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def send_kitchen_new_order_notification(self, order_id: str, order_data: Optional[Dict[str, Any]] = None):
    """
    发送新订单通知给备料员工
    
    当订单支付确认后触发，通知备料区有新的任务到达
    符合PRD要求：支付确认后触发备料通知
    
    Args:
        order_id: 订单ID
        order_data: 订单详细信息（可选）
    """
    try:
        logger.info(f"开始处理新订单通知任务: {order_id}")
        
        # 获取订单信息（如果没有提供）
        if not order_data:
            order_data = _get_order_info(order_id)
        
        if not order_data:
            logger.error(f"无法获取订单信息: {order_id}")
            raise Exception(f"Order {order_id} not found")
        
        # 构建通知消息
        notification_data = {
            "type": "new_order",
            "order_id": order_id,
            "message": f"新的备料任务已到达：订单 {order_id}",
            "order_info": {
                "total_amount": order_data.get("total_amount"),
                "device_id": order_data.get("device_id"),
                "created_at": order_data.get("created_at"),
                "status": order_data.get("status")
            },
            "timestamp": datetime.utcnow().isoformat(),
            "priority": "high"
        }
        
        # 使用统一通知服务发送通知
        try:
            notification_service = get_notification_service()
            # 在Celery任务中需要使用同步方式调用异步方法
            import asyncio
            result = asyncio.run(notification_service.send_new_order_notification(order_id, order_data))
            success_channels = result.get("sent_channels", [])
        except Exception as e:
            logger.error(f"通知服务调用失败: {str(e)}")
            success_channels = ["log"]  # 至少保证日志记录
        
        logger.info(f"新订单通知发送成功: {order_id}, 渠道: {success_channels}")
        
        return {
            "success": True,
            "order_id": order_id,
            "channels": success_channels,
            "message": "新订单通知发送成功"
        }
        
    except Exception as e:
        logger.error(f"发送新订单通知失败: {order_id}, 错误: {str(e)}")
        
        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"重试发送通知: {order_id}, 重试次数: {self.request.retries + 1}")
            raise self.retry(exc=e, countdown=60 * (self.request.retries + 1))
        
        # 最终失败处理
        logger.error(f"新订单通知最终失败: {order_id}, 已达到最大重试次数")
        return {
            "success": False,
            "order_id": order_id,
            "error": str(e),
            "message": "新订单通知发送失败"
        }


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def notify_downstream_on_pickup_ready(self, order_id: str, order_data: Optional[Dict[str, Any]] = None):
    """
    通知下游环节原料已备好可以领取
    
    当备料完成（状态变为READY_FOR_PICKUP）时触发
    符合PRD要求：备料完成后通知下游领取
    
    Args:
        order_id: 订单ID
        order_data: 订单详细信息（可选）
    """
    try:
        logger.info(f"开始处理备料完成通知任务: {order_id}")
        
        # 获取订单信息
        if not order_data:
            order_data = _get_order_info(order_id)
        
        if not order_data:
            logger.error(f"无法获取订单信息: {order_id}")
            raise Exception(f"Order {order_id} not found")
        
        # 构建通知消息
        notification_data = {
            "type": "pickup_ready",
            "order_id": order_id,
            "message": f"订单 {order_id} 的原料已备好，可以领取",
            "order_info": {
                "total_amount": order_data.get("total_amount"),
                "device_id": order_data.get("device_id"),
                "video_stream_id": order_data.get("video_stream_id"),
                "processing_completed_at": datetime.utcnow().isoformat()
            },
            "timestamp": datetime.utcnow().isoformat(),
            "priority": "normal"
        }
        
        # 使用统一通知服务发送通知
        try:
            notification_service = get_notification_service()
            # 在Celery任务中需要使用同步方式调用异步方法
            import asyncio
            result = asyncio.run(notification_service.send_pickup_ready_notification(order_id, order_data))
            success_channels = result.get("sent_channels", [])
        except Exception as e:
            logger.error(f"通知服务调用失败: {str(e)}")
            success_channels = ["log"]  # 至少保证日志记录
        
        logger.info(f"备料完成通知发送成功: {order_id}, 渠道: {success_channels}")
        
        return {
            "success": True,
            "order_id": order_id,
            "channels": success_channels,
            "message": "备料完成通知发送成功"
        }
        
    except Exception as e:
        logger.error(f"发送备料完成通知失败: {order_id}, 错误: {str(e)}")
        
        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"重试发送通知: {order_id}, 重试次数: {self.request.retries + 1}")
            raise self.retry(exc=e, countdown=60 * (self.request.retries + 1))
        
        return {
            "success": False,
            "order_id": order_id,
            "error": str(e),
            "message": "备料完成通知发送失败"
        }


@shared_task
def send_order_status_change_notification(order_id: str, old_status: str, new_status: str, user_info: Optional[Dict] = None):
    """
    发送订单状态变更通知
    
    通用的状态变更通知，可用于各种状态转换
    
    Args:
        order_id: 订单ID
        old_status: 原状态
        new_status: 新状态
        user_info: 操作用户信息
    """
    try:
        from services.order_state_machine import OrderWorkflowHelper
        
        old_display = OrderWorkflowHelper.get_status_display_name(old_status)
        new_display = OrderWorkflowHelper.get_status_display_name(new_status)
        
        notification_data = {
            "type": "status_change",
            "order_id": order_id,
            "message": f"订单 {order_id} 状态已从 {old_display} 变更为 {new_display}",
            "status_change": {
                "old_status": old_status,
                "new_status": new_status,
                "old_display": old_display,
                "new_display": new_display
            },
            "operator": user_info.get("username") if user_info else "系统",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 记录状态变更日志
        logger.info(
            f"📋 状态变更: 订单 {order_id} 由 {old_display} 变更为 {new_display}, "
            f"操作者: {notification_data['operator']}"
        )
        
        # 保存通知记录
        _save_notification_record(notification_data)
        
        return {
            "success": True,
            "order_id": order_id,
            "message": "状态变更通知发送成功"
        }
        
    except Exception as e:
        logger.error(f"发送状态变更通知失败: {order_id}, 错误: {str(e)}")
        return {
            "success": False,
            "order_id": order_id,
            "error": str(e)
        }


# 辅助函数
def _get_order_info(order_id: str) -> Optional[Dict[str, Any]]:
    """获取订单信息"""
    try:
        # 这里应该从数据库获取订单信息
        # 为了避免循环导入，使用简单的实现
        logger.info(f"获取订单信息: {order_id}")
        return {
            "id": order_id,
            "total_amount": 0.0,
            "device_id": "unknown",
            "status": "unknown",
            "created_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"获取订单信息失败: {order_id}, 错误: {str(e)}")
        return None


async def _send_websocket_notification(target_group: str, data: Dict[str, Any]) -> bool:
    """发送WebSocket通知"""
    try:
        # 这里应该实现WebSocket通知逻辑
        logger.info(f"发送WebSocket通知到 {target_group}: {data['message']}")
        return True
    except Exception as e:
        logger.error(f"WebSocket通知发送失败: {str(e)}")
        return False


async def _send_downstream_notification(data: Dict[str, Any]) -> bool:
    """发送下游通知"""
    try:
        # 这里可以实现HTTP POST到下游系统的逻辑
        # 例如：requests.post('https://kitchen-display.internal/api/update', json=data)
        logger.info(f"发送下游通知: {data['message']}")
        return True
    except Exception as e:
        logger.error(f"下游通知发送失败: {str(e)}")
        return False


async def _save_notification_record(data: Dict[str, Any]) -> bool:
    """保存通知记录到数据库"""
    try:
        # 这里应该实现数据库保存逻辑
        logger.info(f"保存通知记录: {data['type']} - {data['message']}")
        return True
    except Exception as e:
        logger.error(f"保存通知记录失败: {str(e)}")
        return False
