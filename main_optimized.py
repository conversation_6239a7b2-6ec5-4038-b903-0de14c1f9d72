"""
优化的主应用文件
使用模块化路由管理器和性能优化
"""

import asyncio
import uvicorn
from contextlib import asynccontextmanager

from core.router_manager import create_app
from config import settings
from utils.logger import get_logger
from utils.performance import periodic_cache_cleanup, get_performance_report

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app):
    """应用生命周期管理"""
    
    # 启动时执行
    logger.info(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"数据库: {settings.database_url}")
    logger.info(f"Redis: {settings.redis_url}")
    
    # 启动后台任务
    cleanup_task = None
    if settings.is_feature_enabled("redis_cache"):
        cleanup_task = asyncio.create_task(periodic_cache_cleanup())
        logger.info("✅ 缓存清理任务已启动")
    
    # 打印功能开关状态
    logger.info("📋 功能开关状态:")
    feature_flags = {
        "order_system": settings.enable_order_system,
        "payment_system": settings.enable_payment_system,
        "notification_system": settings.enable_notification_system,
        "webrtc": settings.enable_webrtc,
        "llm_integration": settings.enable_llm_integration,
        "admin_panel": settings.enable_admin_panel,
        "video_recording": settings.enable_video_recording,
        "redis_cache": settings.enable_redis_cache
    }
    for feature, enabled in feature_flags.items():
        status = "✅" if enabled else "❌"
        logger.info(f"  {status} {feature}: {enabled}")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 正在关闭应用...")
    
    if cleanup_task:
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
        logger.info("✅ 缓存清理任务已停止")
    
    # 打印性能报告
    performance_report = get_performance_report()
    logger.info(f"📊 性能报告: 运行时间 {performance_report['uptime_formatted']}")
    logger.info(f"📊 缓存统计: 大小 {performance_report['cache_stats']['size']}, 命中率 {performance_report['cache_stats']['hit_rate']:.1f}%")
    
    logger.info("👋 应用已关闭")


# 创建应用实例
app = create_app()
app.router.lifespan_context = lifespan


# 添加性能监控端点
@app.get("/api/v1/system/performance", tags=["system"])
async def get_performance_metrics():
    """获取性能指标"""
    return get_performance_report()


@app.get("/api/v1/system/config", tags=["system"])
async def get_system_config():
    """获取系统配置（脱敏）"""
    # 手动构建配置字典，避免复杂的模块化结构
    config = {
        "app_name": settings.app_name,
        "app_version": settings.app_version,
        "debug": settings.debug,
        "database_url": "***MASKED***",  # 敏感信息脱敏
        "redis_host": settings.redis_host,
        "redis_port": settings.redis_port,
        "features": {
            "order_system": settings.enable_order_system,
            "payment_system": settings.enable_payment_system,
            "notification_system": settings.enable_notification_system,
            "webrtc": settings.enable_webrtc,
            "llm_integration": settings.enable_llm_integration,
            "admin_panel": settings.enable_admin_panel,
            "video_recording": settings.enable_video_recording,
            "redis_cache": settings.enable_redis_cache
        }
    }

    return config


if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "main_optimized:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )
