# Robot WebRTC Video Call System

基于FastAPI的机器人WebRTC视频通话系统，支持设备管理、用户认证和实时视频通话。

## 核心功能

- 🤖 **设备管理**: Orange Pi设备注册与管理
- 👥 **用户认证**: 安全的用户登录与授权
- 📹 **视频通话**: 基于WebRTC的实时视频通话
- 🔒 **绑定验证**: 只允许绑定用户与设备通信
- 📱 **客户端应用**: PyQt6图形界面客户端

## 技术栈

- **后端**: FastAPI, SQLAlchemy, Redis
- **数据库**: SQLite
- **WebRTC**: aiortc
- **客户端**: PyQt6
- **实时通信**: WebSocket

## 快速开始

### 1. 环境要求

- Python 3.8+
- Redis Server
- FFmpeg

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
# 启动Redis
redis-server

# 启动API服务器
python main.py
```

### 4. 启动客户端

```bash
# Orange Pi设备端
python clients/orange_pi_client.py

# WebRTC App用户端
python clients/webrtc_app_client.py
```

## 使用流程

1. **设备注册**: Orange Pi客户端自动注册设备
2. **用户登录**: WebRTC App客户端登录用户账号
3. **设备绑定**: 用户扫描设备二维码进行绑定
4. **发起通话**: 用户选择设备发起视频通话
5. **接听通话**: 设备端接听并开始视频通话

## 项目结构

```
robot/
├── main.py                    # 应用入口
├── config.py                  # 配置文件
├── clients/                   # 客户端
│   ├── orange_pi_client.py    # 设备端客户端
│   └── webrtc_app_client.py   # 用户端客户端
├── routers/                   # API路由
│   ├── auth.py               # 用户认证
│   ├── device.py             # 设备管理
│   └── webrtc_unified.py     # WebRTC统一路由
├── services/                  # 核心服务
│   ├── aiortc_service.py     # WebRTC服务
│   ├── redis_webrtc_service.py # Redis会话管理
│   └── device_service.py     # 设备服务
├── models/                    # 数据模型
└── schemas/                   # API模式
```

## API文档

启动服务后访问: http://localhost:8000/docs

## 许可证

MIT License
