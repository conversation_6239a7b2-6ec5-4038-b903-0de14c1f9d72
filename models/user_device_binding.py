"""
设备和用户设备绑定关系模型
用于WebRTC设备访问权限控制
"""

from sqlmodel import SQLModel, Field
from datetime import datetime, timedelta
from enum import Enum
from typing import Optional
import uuid


class Device(SQLModel, table=True):
    """设备模型"""
    __tablename__ = "devices"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    device_id: str = Field(unique=True, index=True)
    device_name: str
    device_type: str
    status: str = Field(default="offline")  # online, offline, maintenance
    last_heartbeat_at: Optional[datetime] = None

    # 绑定相关字段
    binding_code: Optional[str] = Field(default=None, max_length=10)  # 绑定码
    binding_code_expires_at: Optional[datetime] = Field(default=None)  # 绑定码过期时间
    binding_qr_code_data: Optional[str] = Field(default=None)  # 二维码数据

    # WebRTC 相关字段
    active_webrtc_user_id: Optional[str] = Field(default=None, foreign_key="users.id")
    webrtc_session_id: Optional[str] = None  # 当前WebRTC会话ID
    webrtc_connected_at: Optional[datetime] = None  # WebRTC连接建立时间

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    def __repr__(self):
        return f"<Device(device_id='{self.device_id}', name='{self.device_name}', status='{self.status}')>"


class PermissionLevel(str, Enum):
    """权限级别枚举"""
    READ = "read"           # 只读权限：只能查看设备状态
    CONTROL = "control"     # 控制权限：可以发起通话、控制设备
    ADMIN = "admin"         # 管理员权限：可以管理设备设置


class UserDeviceBinding(SQLModel, table=True):
    """用户设备绑定关系表"""
    __tablename__ = "user_device_bindings"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id", index=True)
    device_id: str = Field(max_length=255, index=True)
    device_name: Optional[str] = Field(default=None, max_length=255)  # 设备显示名称

    # 权限控制
    permission_level: str = Field(default=PermissionLevel.CONTROL, max_length=50)

    # 时间管理
    bound_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = Field(default=None)  # 可选的过期时间
    last_accessed: Optional[datetime] = Field(default=None)  # 最后访问时间

    # 状态管理
    is_active: bool = Field(default=True)
    is_approved: bool = Field(default=False)  # 是否已批准

    # 绑定信息
    bound_by_user_id: Optional[int] = Field(default=None, foreign_key="users.id")  # 谁绑定的
    binding_reason: Optional[str] = Field(default=None)  # 绑定原因

    # 设备信息缓存
    device_type: Optional[str] = Field(default=None, max_length=100)  # 设备类型
    device_location: Optional[str] = Field(default=None, max_length=255)  # 设备位置
    device_description: Optional[str] = Field(default=None)  # 设备描述

    # 创建和更新时间
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    def __repr__(self):
        return f"<UserDeviceBinding(user_id={self.user_id}, device_id='{self.device_id}', permission='{self.permission_level}')>"

    @property
    def is_expired(self) -> bool:
        """检查绑定是否已过期"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self) -> bool:
        """检查绑定是否有效"""
        return self.is_active and self.is_approved and not self.is_expired

    def has_permission(self, required_permission: PermissionLevel) -> bool:
        """检查是否具有指定权限"""
        if not self.is_valid:
            return False
        
        permission_hierarchy = {
            PermissionLevel.READ: 1,
            PermissionLevel.CONTROL: 2,
            PermissionLevel.ADMIN: 3
        }
        
        current_level = permission_hierarchy.get(self.permission_level, 0)
        required_level = permission_hierarchy.get(required_permission, 0)
        
        return current_level >= required_level

    def update_last_accessed(self):
        """更新最后访问时间"""
        self.last_accessed = datetime.utcnow()

    def extend_expiry(self, days: int = 30):
        """延长过期时间"""
        if self.expires_at:
            self.expires_at = max(self.expires_at, datetime.utcnow()) + timedelta(days=days)
        else:
            self.expires_at = datetime.utcnow() + timedelta(days=days)

    @classmethod
    def create_binding(cls, user_id: int, device_id: str, device_name: str = None, 
                      permission_level: PermissionLevel = PermissionLevel.CONTROL,
                      bound_by_user_id: int = None, expires_days: int = None):
        """创建新的设备绑定"""
        binding = cls(
            user_id=user_id,
            device_id=device_id,
            device_name=device_name or device_id,
            permission_level=permission_level,
            bound_by_user_id=bound_by_user_id or user_id,
            is_approved=True  # 默认自动批准
        )
        
        if expires_days:
            binding.expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        return binding


class DeviceBindingRequest(SQLModel, table=True):
    """设备绑定请求表（用于需要审批的场景）"""
    __tablename__ = "device_binding_requests"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    device_id: str = Field(max_length=255)
    device_name: Optional[str] = Field(default=None, max_length=255)

    # 请求信息
    requested_permission: str = Field(default=PermissionLevel.CONTROL, max_length=50)
    request_reason: Optional[str] = Field(default=None)

    # 审批信息
    status: str = Field(default="pending", max_length=50)  # pending, approved, rejected
    approved_by_user_id: Optional[int] = Field(default=None, foreign_key="users.id")
    approved_at: Optional[datetime] = Field(default=None)
    rejection_reason: Optional[str] = Field(default=None)

    # 时间管理
    requested_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = Field(default=None)

    def approve(self, approved_by_user_id: int):
        """批准绑定请求"""
        self.status = "approved"
        self.approved_by_user_id = approved_by_user_id
        self.approved_at = datetime.utcnow()

    def reject(self, approved_by_user_id: int, reason: str = None):
        """拒绝绑定请求"""
        self.status = "rejected"
        self.approved_by_user_id = approved_by_user_id
        self.approved_at = datetime.utcnow()
        self.rejection_reason = reason
