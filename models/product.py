from sqlmodel import SQLModel, <PERSON>
from typing import Optional
from datetime import datetime
import uuid


class Product(SQLModel, table=True):
    __tablename__ = "products"
    
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    name: str = Field(index=True)
    description: Optional[str] = None
    price: float
    stock: int = Field(default=0)
    image_url: Optional[str] = None
    category: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
