from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime
from enum import Enum
import uuid


class OrderStatus(str, Enum):
    """订单状态枚举 - 符合PRD要求的备料工作流"""
    PENDING_PAYMENT = "pending_payment"  # 待支付
    PAID = "paid"                        # 已支付 (对备料员工来说，这是"待处理"的任务)
    PROCESSING = "processing"            # 原料处理中
    READY_FOR_PICKUP = "ready_for_pickup" # 备料完成，待（下游）领取
    COMPLETED = "completed"              # 已被下游领取，流程完成
    CANCELLED = "cancelled"              # 已取消


class Order(SQLModel, table=True):
    __tablename__ = "orders"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    device_id: str = Field(foreign_key="devices.id")
    total_amount: float
    status: str = Field(default=OrderStatus.PENDING_PAYMENT)  # 使用枚举默认值
    payment_transaction_id: Optional[str] = Field(default=None, foreign_key="payment_transactions.id")

    # 新增视频录制相关字段（为任务3做准备）
    video_stream_id: Optional[str] = Field(default=None)
    video_start_time: Optional[datetime] = Field(default=None)
    video_end_time: Optional[datetime] = Field(default=None)

    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class OrderItem(SQLModel, table=True):
    __tablename__ = "order_items"
    
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    order_id: str = Field(foreign_key="orders.id")
    product_id: str = Field(foreign_key="products.id")
    quantity: int
    price_at_purchase: float
