from sqlmodel import SQLModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
import uuid


class PaymentTransaction(SQLModel, table=True):
    __tablename__ = "payment_transactions"
    
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    order_id: str = Field(foreign_key="orders.id")
    payment_method: str  # alipay, wechatpay
    transaction_id: Optional[str] = None
    amount: float
    status: str = Field(default="pending")  # pending, success, failed
    callback_data: Optional[str] = None  # JSON string for callback data
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
