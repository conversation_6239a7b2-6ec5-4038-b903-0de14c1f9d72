#!/usr/bin/env python3
"""
数据库迁移便捷脚本
简化 Alembic 常用操作
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def get_alembic_cmd():
    """获取 alembic 命令路径"""
    # 尝试不同的路径
    possible_paths = [
        "alembic",  # 系统PATH中
        "venv/Scripts/alembic.exe",  # Windows虚拟环境
        "venv/bin/alembic",  # Linux/Mac虚拟环境
        os.path.join(os.path.dirname(sys.executable), "alembic.exe"),  # 当前Python环境
        os.path.join(os.path.dirname(sys.executable), "alembic"),
    ]

    for path in possible_paths:
        try:
            result = subprocess.run([path, "--help"], capture_output=True, text=True)
            if result.returncode == 0:
                return path
        except (FileNotFoundError, subprocess.CalledProcessError):
            continue

    return "alembic"  # 默认返回

def run_command(cmd, description=""):
    """执行命令并处理结果"""
    if description:
        print(f"🔄 {description}")

    # 如果是 alembic 命令，使用正确的路径
    if cmd[0] == "alembic":
        cmd[0] = get_alembic_cmd()

    print(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False
    except FileNotFoundError as e:
        print(f"❌ 找不到命令: {e}")
        print("请确保 alembic 已正确安装")
        return False

def backup_database():
    """备份数据库"""
    from config import settings
    
    if settings.database_url.startswith("sqlite:///"):
        db_path = settings.database_url.replace("sqlite:///", "")
        if os.path.exists(db_path):
            backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"✅ 数据库已备份到: {backup_path}")
            return backup_path
        else:
            print("⚠️ 数据库文件不存在，跳过备份")
            return None
    else:
        print("⚠️ PostgreSQL 数据库请手动备份")
        return None

def create_migration(message):
    """创建新的迁移"""
    if not message:
        message = input("请输入迁移描述: ")
    
    cmd = ["alembic", "revision", "--autogenerate", "-m", message]
    if run_command(cmd, f"创建迁移: {message}"):
        print("✅ 迁移文件创建成功")
        print("📝 请检查生成的迁移文件是否正确")
        return True
    return False

def upgrade_database(target="head"):
    """升级数据库"""
    # 备份数据库
    backup_path = backup_database()
    
    cmd = ["alembic", "upgrade", target]
    if run_command(cmd, f"升级数据库到: {target}"):
        print("✅ 数据库升级成功")
        return True
    else:
        if backup_path:
            print(f"💾 如需恢复，备份文件位于: {backup_path}")
        return False

def downgrade_database(target):
    """降级数据库"""
    if not target:
        target = input("请输入目标版本 (如: -1, base, 或具体版本号): ")
    
    # 备份数据库
    backup_path = backup_database()
    
    print("⚠️ 警告: 数据库降级可能导致数据丢失!")
    confirm = input("确认继续? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return False
    
    cmd = ["alembic", "downgrade", target]
    if run_command(cmd, f"降级数据库到: {target}"):
        print("✅ 数据库降级成功")
        return True
    else:
        if backup_path:
            print(f"💾 如需恢复，备份文件位于: {backup_path}")
        return False

def show_status():
    """显示迁移状态"""
    print("📊 当前迁移状态:")
    print("-" * 50)
    
    # 当前版本
    cmd = ["alembic", "current"]
    run_command(cmd, "当前版本")
    
    print()
    
    # 迁移历史
    cmd = ["alembic", "history", "--verbose"]
    run_command(cmd, "迁移历史")

def init_database():
    """初始化数据库（全新安装）"""
    print("🚀 初始化数据库")
    print("这将创建所有表结构...")
    
    confirm = input("确认继续? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return False
    
    # 升级到最新版本
    return upgrade_database("head")

def reset_database():
    """重置数据库（危险操作）"""
    print("⚠️ 危险操作: 重置数据库")
    print("这将删除所有数据并重新创建表结构!")
    
    confirm = input("确认继续? 请输入 'RESET' 确认: ")
    if confirm != 'RESET':
        print("❌ 操作已取消")
        return False
    
    # 备份数据库
    backup_path = backup_database()
    
    # 降级到 base
    cmd = ["alembic", "downgrade", "base"]
    if run_command(cmd, "降级到初始状态"):
        # 升级到最新版本
        if upgrade_database("head"):
            print("✅ 数据库重置成功")
            return True
    
    if backup_path:
        print(f"💾 如需恢复，备份文件位于: {backup_path}")
    return False

def main():
    parser = argparse.ArgumentParser(description="数据库迁移便捷工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建迁移
    create_parser = subparsers.add_parser("create", help="创建新迁移")
    create_parser.add_argument("-m", "--message", help="迁移描述")
    
    # 升级数据库
    upgrade_parser = subparsers.add_parser("upgrade", help="升级数据库")
    upgrade_parser.add_argument("target", nargs="?", default="head", help="目标版本 (默认: head)")
    
    # 降级数据库
    downgrade_parser = subparsers.add_parser("downgrade", help="降级数据库")
    downgrade_parser.add_argument("target", nargs="?", help="目标版本")
    
    # 显示状态
    subparsers.add_parser("status", help="显示迁移状态")
    
    # 初始化数据库
    subparsers.add_parser("init", help="初始化数据库")
    
    # 重置数据库
    subparsers.add_parser("reset", help="重置数据库 (危险操作)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 确保在正确的目录
    if not os.path.exists("alembic.ini"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 执行对应命令
    if args.command == "create":
        create_migration(args.message)
    elif args.command == "upgrade":
        upgrade_database(args.target)
    elif args.command == "downgrade":
        downgrade_database(args.target)
    elif args.command == "status":
        show_status()
    elif args.command == "init":
        init_database()
    elif args.command == "reset":
        reset_database()

if __name__ == "__main__":
    main()
