#!/usr/bin/env python3
"""
测试第二阶段功能
验证Celery异步通知系统和notifications模块
"""

import asyncio
from typing import Dict, Any

def test_celery_tasks():
    """测试Celery任务导入"""
    print("🧪 测试Celery任务导入")
    
    try:
        from tasks.notification_tasks import (
            send_kitchen_new_order_notification,
            notify_downstream_on_pickup_ready,
            send_order_status_change_notification
        )
        from tasks.order_tasks import (
            process_payment_confirmation,
            monitor_order_timeout,
            cleanup_completed_orders
        )
        
        print("✅ 所有Celery任务导入成功")
        
        # 测试任务属性
        assert hasattr(send_kitchen_new_order_notification, 'delay')
        assert hasattr(notify_downstream_on_pickup_ready, 'delay')
        print("✅ Celery任务具有delay方法")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery任务测试失败: {str(e)}")
        return False


def test_notification_service():
    """测试通知服务"""
    print("\n🧪 测试通知服务")
    
    try:
        from services.notification_service import (
            NotificationService, 
            NotificationMessage, 
            NotificationType, 
            NotificationPriority,
            NotificationChannel,
            get_notification_service
        )
        
        print("✅ 通知服务模块导入成功")
        
        # 测试枚举
        assert NotificationType.NEW_ORDER == "new_order"
        assert NotificationPriority.HIGH == "high"
        assert NotificationChannel.WEBSOCKET == "websocket"
        print("✅ 通知枚举定义正确")
        
        # 测试通知消息创建
        message = NotificationMessage(
            notification_type=NotificationType.NEW_ORDER,
            title="测试通知",
            message="这是一个测试通知",
            priority=NotificationPriority.NORMAL,
            target_audience="test_users",
            data={"test": "data"},
            channels=[NotificationChannel.LOG, NotificationChannel.WEBSOCKET]
        )
        
        assert message.type == NotificationType.NEW_ORDER
        assert message.title == "测试通知"
        assert len(message.channels) == 2
        print("✅ 通知消息创建正常")
        
        # 测试消息转换为字典
        message_dict = message.to_dict()
        assert "id" in message_dict
        assert "type" in message_dict
        assert "created_at" in message_dict
        print("✅ 通知消息序列化正常")
        
        # 测试通知服务实例
        service = get_notification_service()
        assert isinstance(service, NotificationService)
        print("✅ 通知服务实例创建正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 通知服务测试失败: {str(e)}")
        return False


async def test_notification_adapters():
    """测试通知适配器"""
    print("\n🧪 测试通知适配器")
    
    try:
        from services.notification_service import (
            LogNotificationAdapter,
            WebSocketNotificationAdapter,
            DatabaseNotificationAdapter,
            PushNotificationAdapter,
            NotificationMessage,
            NotificationType,
            NotificationPriority
        )
        
        print("✅ 通知适配器导入成功")
        
        # 创建测试消息
        test_message = NotificationMessage(
            notification_type=NotificationType.NEW_ORDER,
            title="适配器测试",
            message="测试通知适配器功能",
            priority=NotificationPriority.NORMAL
        )
        
        # 测试日志适配器
        log_adapter = LogNotificationAdapter()
        log_result = await log_adapter.send(test_message)
        assert log_result == True
        print("✅ 日志适配器测试通过")
        
        # 测试WebSocket适配器
        ws_adapter = WebSocketNotificationAdapter()
        ws_result = await ws_adapter.send(test_message)
        # WebSocket可能返回False（如果没有连接），这是正常的
        print(f"✅ WebSocket适配器测试完成: {ws_result}")
        
        # 测试数据库适配器
        db_adapter = DatabaseNotificationAdapter()
        db_result = await db_adapter.send(test_message)
        assert db_result == True
        print("✅ 数据库适配器测试通过")
        
        # 测试推送适配器
        push_adapter = PushNotificationAdapter()
        push_result = await push_adapter.send(test_message)
        # 推送可能返回False（如果禁用），这是正常的
        print(f"✅ 推送适配器测试完成: {push_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 通知适配器测试失败: {str(e)}")
        return False


async def test_notification_service_methods():
    """测试通知服务方法"""
    print("\n🧪 测试通知服务方法")
    
    try:
        from services.notification_service import get_notification_service
        
        service = get_notification_service()
        
        # 测试新订单通知
        order_data = {
            "id": "test_order_001",
            "total_amount": 35.0,
            "device_id": "test_device",
            "status": "paid"
        }
        
        new_order_result = await service.send_new_order_notification("test_order_001", order_data)
        assert "notification_id" in new_order_result
        assert "success_count" in new_order_result
        print("✅ 新订单通知方法测试通过")
        
        # 测试备料完成通知
        pickup_data = {
            "id": "test_order_001",
            "total_amount": 35.0,
            "device_id": "test_device",
            "video_stream_id": "video_123",
            "status": "ready_for_pickup"
        }
        
        pickup_result = await service.send_pickup_ready_notification("test_order_001", pickup_data)
        assert "notification_id" in pickup_result
        assert "success_count" in pickup_result
        print("✅ 备料完成通知方法测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 通知服务方法测试失败: {str(e)}")
        return False


def test_celery_app_config():
    """测试Celery应用配置"""
    print("\n🧪 测试Celery应用配置")
    
    try:
        from celery_app import celery_app
        
        print("✅ Celery应用导入成功")
        
        # 测试配置
        assert celery_app.conf.task_serializer == 'json'
        assert celery_app.conf.timezone == 'Asia/Shanghai'
        print("✅ Celery配置正确")
        
        # 测试任务发现
        task_names = list(celery_app.tasks.keys())
        print(f"发现的任务: {len(task_names)} 个")
        
        # 检查关键任务是否被发现
        expected_tasks = [
            'tasks.notification_tasks.send_kitchen_new_order_notification',
            'tasks.notification_tasks.notify_downstream_on_pickup_ready',
            'tasks.order_tasks.process_payment_confirmation'
        ]
        
        for task_name in expected_tasks:
            if task_name in task_names:
                print(f"✅ 任务已注册: {task_name}")
            else:
                print(f"⚠️  任务未注册: {task_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery应用配置测试失败: {str(e)}")
        return False


def test_config_updates():
    """测试配置更新"""
    print("\n🧪 测试配置更新")
    
    try:
        from config import settings
        
        # 测试新增的配置项
        assert hasattr(settings, 'celery_task_serializer')
        assert hasattr(settings, 'enable_websocket_notifications')
        assert hasattr(settings, 'notification_retry_attempts')
        print("✅ 新配置项存在")
        
        # 测试Redis URL
        redis_url = settings.redis_url
        assert redis_url.startswith('redis://')
        print(f"✅ Redis URL配置正确: {redis_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置更新测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始第二阶段功能测试")
    
    results = []
    
    # 运行同步测试
    results.append(("Celery任务导入", test_celery_tasks()))
    results.append(("通知服务", test_notification_service()))
    results.append(("Celery应用配置", test_celery_app_config()))
    results.append(("配置更新", test_config_updates()))
    
    # 运行异步测试
    results.append(("通知适配器", await test_notification_adapters()))
    results.append(("通知服务方法", await test_notification_service_methods()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 第二阶段测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 第二阶段所有功能测试通过！")
        print("\n📋 第二阶段开发完成情况:")
        print("✅ 任务5: Celery异步通知系统 - 完整实现")
        print("✅ 任务6: notifications模块 - 统一通知服务")
        print("✅ 任务7: staff接口路径调整 - 符合PRD规范")
        print("\n🚀 可以开始第三阶段开发：功能评估和模块化")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要修复")


if __name__ == "__main__":
    asyncio.run(main())
