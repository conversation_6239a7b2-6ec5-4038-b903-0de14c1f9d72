#!/usr/bin/env python3
"""
测试认证功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1/auth"

def test_register():
    """测试注册功能"""
    print("🧪 测试用户注册")
    
    register_data = {
        "username": "testuser",
        "password": "testpass",
        "role": "user"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/register", json=register_data)
        print(f"注册请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 注册成功: {result}")
            return True
        else:
            print(f"❌ 注册失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 注册请求失败: {str(e)}")
        return False

def test_login():
    """测试登录功能"""
    print("\n🧪 测试用户登录")
    
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/login", json=login_data)
        print(f"登录请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: {result}")
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None

def test_login_with_wrong_password():
    """测试错误密码登录"""
    print("\n🧪 测试错误密码登录")
    
    login_data = {
        "username": "testuser",
        "password": "wrongpassword"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/login", json=login_data)
        print(f"错误密码登录状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 正确返回401错误")
            return True
        else:
            print(f"❌ 应该返回401，但返回了: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始认证功能测试")
    print("="*50)
    
    # 先测试注册
    register_success = test_register()
    
    if register_success:
        # 测试正确登录
        token = test_login()
        
        if token:
            print(f"\n🎉 获得访问令牌: {token[:20]}...")
        
        # 测试错误密码
        test_login_with_wrong_password()
    
    print("\n✅ 认证测试完成")

if __name__ == "__main__":
    main()
