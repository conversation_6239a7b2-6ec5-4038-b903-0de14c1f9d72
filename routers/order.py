from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from typing import Optional

from database import get_session
from services.shop_service import ShopService
from services.payment import PaymentFactory, PaymentMethod, PaymentRequest
from services.order_state_machine import OrderStateMachine
from schemas.order import (
    OrderCreate, OrderResponse, PaymentInitRequest, PaymentResponse, PaymentConfirmRequest
)
from models.order import OrderStatus
from tasks.notification_tasks import send_kitchen_new_order_notification
from tasks.order_tasks import process_payment_confirmation
from utils.logger import get_logger
from config import settings

logger = get_logger(__name__)
router = APIRouter(tags=["Orders"])


@router.post('/', response_model=OrderResponse)
async def create_order(
    order_data: OrderCreate,
    session: Session = Depends(get_session)
):
    """
    创建订单

    根据序列图步骤1: 设备创建订单，返回订单ID
    """
    try:
        shop_service = ShopService(session, settings.payment_config)

        # 创建订单
        order = shop_service.create_order_from_items(
            device_id=order_data.device_id,
            items=order_data.items,
            total_amount=order_data.total_amount
        )

        if not order:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建订单失败"
            )

        logger.info(f"Order {order.id} created for device {order_data.device_id}")

        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at,
            items=[]  # 可以根据需要填充订单项
        )

    except Exception as e:
        logger.error(f"Error creating order: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建订单时发生错误: {str(e)}"
        )


@router.get('/{order_id}', response_model=OrderResponse)
async def get_order_details(
    order_id: str,
    session: Session = Depends(get_session)
):
    """
    获取订单详情
    """
    try:
        shop_service = ShopService(session)
        order = shop_service.get_order_by_id(order_id)

        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            created_at=order.created_at,
            items=[]  # 可以根据需要填充订单项
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting order details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订单详情时发生错误: {str(e)}"
        )


@router.post('/{order_id}/pay', response_model=PaymentResponse)
async def initiate_payment(
    order_id: str,
    payment_request: PaymentInitRequest,
    session: Session = Depends(get_session)
):
    """
    发起支付

    根据序列图步骤2-5: 设备发起支付，调用支付平台API，返回二维码URL
    """
    try:
        shop_service = ShopService(session, settings.payment_config)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 检查订单状态
        if order.status != OrderStatus.PENDING_PAYMENT:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"订单状态不允许支付，当前状态: {order.status}"
            )

        # 验证支付方式
        try:
            payment_method = PaymentMethod(payment_request.payment_method)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的支付方式: {payment_request.payment_method}"
            )

        # 获取支付配置
        method_config = settings.payment_config.get(payment_request.payment_method, {})
        if not method_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"支付方式 {payment_request.payment_method} 未配置"
            )

        # 创建支付适配器
        payment_adapter = PaymentFactory.create_adapter(payment_method, method_config)

        # 创建支付请求
        pay_request = PaymentRequest(
            out_trade_no=order.id,
            total_amount=order.total_amount,
            subject=f"订单支付-{order.id}",
            body=f"机器人订单支付，订单号：{order.id}",
            notify_url=method_config.get('notify_url'),
            return_url=method_config.get('return_url')
        )

        # 调用支付平台API
        payment_result = payment_adapter.create_payment(pay_request)

        if not payment_result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"创建支付订单失败: {payment_result.message}"
            )

        logger.info(f"Payment initiated for order {order_id} with method {payment_request.payment_method}")

        return PaymentResponse(
            success=True,
            payment_url=payment_result.payment_url,
            qr_code=payment_result.qr_code,
            order_id=order_id,
            payment_method=payment_request.payment_method,
            message="支付订单创建成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initiating payment for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发起支付时发生错误: {str(e)}"
        )


@router.get('/{order_id}/payment-status')
async def get_payment_status(
    order_id: str,
    session: Session = Depends(get_session)
):
    """
    查询支付状态

    根据序列图步骤11: 设备轮询查询支付状态
    """
    try:
        shop_service = ShopService(session)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        return {
            "order_id": order_id,
            "status": order.status,
            "total_amount": order.total_amount,
            "created_at": order.created_at,
            "updated_at": order.updated_at
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting payment status for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询支付状态时发生错误: {str(e)}"
        )


@router.post('/{order_id}/confirm_payment')
async def confirm_payment(
    order_id: str,
    payment_data: PaymentConfirmRequest,
    session: Session = Depends(get_session)
):
    """
    确认支付成功

    根据PRD要求：支付确认后将订单状态从PENDING_PAYMENT转为PAID，
    并触发备料通知（为后续Celery任务做准备）
    """
    try:
        shop_service = ShopService(session, settings.payment_config)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证当前状态
        if order.status != OrderStatus.PENDING_PAYMENT:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"订单状态不允许确认支付，当前状态: {order.status}"
            )

        # 验证状态转换
        if not OrderStateMachine.is_valid_transition(order.status, OrderStatus.PAID):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=OrderStateMachine.get_transition_error_message(order.status, OrderStatus.PAID)
            )

        # TODO: 这里应该验证支付凭证的真实性
        # 可以调用支付平台API验证payment_transaction_id
        logger.info(f"验证支付凭证: {payment_data.payment_transaction_id}")

        # 更新订单状态
        success = shop_service.update_order_status(order_id, OrderStatus.PAID)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新订单状态失败"
            )

        # 触发备料通知（Celery异步任务）
        try:
            # 准备订单数据
            order_data = {
                "id": order.id,
                "total_amount": order.total_amount,
                "device_id": order.device_id,
                "status": OrderStatus.PAID,
                "created_at": order.created_at.isoformat()
            }

            # 异步发送新订单通知给备料员工
            send_kitchen_new_order_notification.delay(order_id, order_data)
            logger.info(f"已触发备料通知任务: {order_id}")

            # 异步处理支付确认的后续业务逻辑
            payment_task_data = {
                "payment_transaction_id": payment_data.payment_transaction_id,
                "payment_method": payment_data.payment_method,
                "verification_data": payment_data.verification_data
            }
            process_payment_confirmation.delay(order_id, payment_task_data)
            logger.info(f"已触发支付确认处理任务: {order_id}")

        except Exception as e:
            logger.error(f"触发异步任务失败: {order_id}, 错误: {str(e)}")
            # 不影响主流程，只记录错误

        logger.info(f"Payment confirmed for order {order_id}")

        return {
            "success": True,
            "message": "支付确认成功",
            "order_id": order_id,
            "new_status": OrderStatus.PAID,
            "next_step": "等待备料员工开始处理"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming payment for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认支付时发生错误: {str(e)}"
        )
