from fastapi import APIRouter, WebSocket, Query, HTTPException, Depends, WebSocketDisconnect
from fastapi.responses import JSONResponse
import json
import asyncio
import uuid
import os
from datetime import datetime
from typing import Optional, Dict, Any
from sqlmodel import select
from enum import Enum
from utils.logger import get_logger
from utils.security import verify_token
from database import get_session
from sqlalchemy.orm import Session
from services.dependency_container import WebRTCConnectionManager, get_connection_manager
from services.device_service import DeviceService
from models.user_device_binding import PermissionLevel
from models.user import User
from dependencies import get_current_user

# 添加WebSocket异常处理
try:
    from websockets.exceptions import ConnectionClosedError
except ImportError:
    # 如果websockets库不可用，定义一个替代异常
    class ConnectionClosedError(Exception):
        pass

logger = get_logger(__name__)
router = APIRouter(tags=["WebRTC"])

# 连接类型枚举 - 定义支持的WebRTC连接类型
class ConnectionType(str, Enum):
    SIGNALING = "signaling"          # 基础信令连接
    VIDEO_CALL = "video"            # 视频通话连接
    AUDIO_CALL = "audio"            # 音频通话连接
    SCREEN_SHARE = "screen"         # 屏幕共享连接
    DATA_CHANNEL = "data"           # 数据通道连接

# 功能模块枚举 - 定义WebRTC支持的功能模块
class FeatureModule(str, Enum):
    BASIC_MESSAGING = "messaging"    # 基础消息传输
    MEDIA_STREAMING = "media"        # 音视频媒体流
    DATA_CHANNEL = "data"           # 二进制数据通道
    RECORDING = "recording"         # 通话录制功能
    ANALYTICS = "analytics"         # 通话质量分析

# 保留向后兼容的Redis服务获取函数
def get_webrtc_redis():
    """获取WebRTC Redis服务实例（向后兼容）"""
    from services.webrtc_redis_service import get_redis_service
    return get_redis_service()


async def authenticate_websocket(websocket: WebSocket, token: str) -> Optional[dict]:
    """WebSocket连接认证 - 增强版本"""
    try:
        payload = verify_token(token)
        if not payload:
            await websocket.close(code=1008, reason="Invalid token")
            return None

        # 验证token中的必要字段
        if payload.get("type") == "device":
            if not payload.get("device_id"):
                await websocket.close(code=1008, reason="Invalid device token: missing device_id")
                return None
        elif payload.get("sub"):  # 用户token
            if not payload.get("sub"):
                await websocket.close(code=1008, reason="Invalid user token: missing sub")
                return None
        else:
            await websocket.close(code=1008, reason="Invalid token: unknown type")
            return None

        return payload
    except Exception as e:
        logger.error(f"WebSocket authentication failed: {e}")
        await websocket.close(code=1008, reason="Authentication failed")
        return None

# 进程ID用于多进程环境标识
PROCESS_ID = f"webrtc_process_{os.getpid()}"

# 本地WebSocket连接存储（不能序列化到Redis）
local_websockets: Dict[str, WebSocket] = {}

# 全局连接和会话存储
test_connections: Dict[str, Dict[str, Any]] = {}
webrtc_connections: Dict[str, Dict[str, Any]] = {}
webrtc_sessions: Dict[str, Dict[str, Any]] = {}


async def verify_device_access_permission(user_info: dict, target_id: str, participant_type: str,
                                        db_session: Session) -> bool:
    """验证用户是否有权限访问目标设备"""
    try:
        device_service = DeviceService(db_session)

        if participant_type == "user":
            # 用户尝试连接设备，检查用户是否绑定了该设备
            user_id = user_info.get("user_id") or user_info.get("id") or user_info.get('sub')
            if not user_id:
                logger.error("用户信息中缺少user_id")
                return False

            # 检查用户是否有控制权限
            has_permission = device_service.check_user_device_permission(
                user_id=user_id,
                device_id=target_id,
                required_permission=PermissionLevel.CONTROL
            )

            if not has_permission:
                logger.warning(f"用户 {user_id} 没有权限访问设备 {target_id}")
                return False

            logger.info(f"用户 {user_id} 有权限访问设备 {target_id}")
            return True

        elif participant_type == "device":
            # 设备尝试连接，检查设备是否允许目标用户访问
            device_id = user_info.get("device_id")
            if not device_id:
                logger.error("设备信息中缺少device_id")
                return False

            # 获取有权限访问该设备的用户列表
            device_service = DeviceService(db_session)
            authorized_users = device_service.get_device_bound_users(device_id)
            authorized_user_ids = [user.id for user, binding in authorized_users]

            # 检查目标用户是否在授权列表中
            # 如果target_id是用户名，需要转换为用户ID
            target_user_id = None
            try:
                target_user_id = int(target_id)
            except ValueError:
                # target_id可能是用户名，需要查询用户ID
                user_statement = select(User).where(User.username == target_id)
                user = db_session.exec(user_statement).first()
                if user:
                    target_user_id = user.id
                else:
                    logger.error(f"未找到用户: {target_id}")
                    return False

            if target_user_id not in authorized_user_ids:
                logger.warning(f"设备 {device_id} 不允许用户 {target_user_id} 访问")
                return False

            logger.info(f"设备 {device_id} 允许用户 {target_user_id} 访问")
            return True

        else:
            logger.error(f"未知的参与者类型: {participant_type}")
            return False

    except Exception as e:
        logger.error(f"验证设备访问权限失败: {e}")
        return False

@router.websocket("/connect")
async def webrtc_connect(
    websocket: WebSocket,
    participant_type: str = Query(...),  # "user" or "device"
    target_id: str = Query(...),
    connection_type: str = Query("video"),  # "video", "audio", "signaling"
    features: Optional[str] = Query(None),
    connection_manager: WebRTCConnectionManager = Depends(get_connection_manager),
):
    """WebRTC V2连接端点 - 安全的WebSocket认证"""

    # 从WebSocket子协议中提取token (更安全的方式)
    token = None
    if websocket.headers.get("sec-websocket-protocol"):
        protocols = websocket.headers.get("sec-websocket-protocol").split(", ")
        for protocol in protocols:
            if protocol.startswith("Bearer."):
                token = protocol[7:]  # 移除 "Bearer." 前缀
                break

    # 如果没有在子协议中找到token，尝试从查询参数获取（向后兼容）
    if not token:
        token = websocket.query_params.get("token")
        if token:
            logger.warning("Token passed via query parameter - this is deprecated and insecure")

    if not token:
        await websocket.close(code=1008, reason="Missing authentication token")
        return

    # 验证token - 使用增强版认证
    user_info = await authenticate_websocket(websocket, token)
    if not user_info:
        return  # authenticate_websocket已经关闭了连接

    logger.info(f'WebRTC连接认证成功: {user_info.get("type", "user")} - {user_info.get("device_id") or user_info.get("sub")}')

    # 验证设备访问权限
    db_session = next(get_session())
    try:
        # 对于设备类型的连接，如果没有绑定用户，允许连接但标记为未绑定状态
        if participant_type == "device":
            # 设备连接时，检查是否有绑定的用户
            device_service = DeviceService(db_session)
            device_id = user_info.get("device_id")

            if device_id:
                # 获取设备的绑定用户列表
                authorized_users = device_service.get_device_bound_users(device_id)

                if not authorized_users:
                    # 设备没有绑定用户，允许连接但标记为未绑定状态
                    logger.info(f"设备 {device_id} 未绑定用户，允许连接以生成绑定码")
                    # 继续连接，但在连接信息中标记未绑定状态
                else:
                    # 设备有绑定用户，进行正常的权限验证
                    has_permission = await verify_device_access_permission(
                        user_info=user_info,
                        target_id=target_id,
                        participant_type=participant_type,
                        db_session=db_session
                    )

                    if not has_permission:
                        await websocket.close(code=1008, reason="Access denied: Device not bound to user")
                        return
        else:
            # 用户连接设备，必须进行权限验证
            has_permission = await verify_device_access_permission(
                user_info=user_info,
                target_id=target_id,
                participant_type=participant_type,
                db_session=db_session
            )

            # if not has_permission:
            #     await websocket.close(code=1008, reason="Access denied: Device not bound to user")
            #     return

    except Exception as e:
        logger.error(f"权限验证过程中发生错误: {e}")
        await websocket.close(code=1008, reason="Permission verification failed")
        return
    finally:
        db_session.close()

    await websocket.accept()

    # 生成会话ID和连接ID - 使用固定的会话ID进行配对
    session_id = f"{connection_type}_session"  # 简化：所有相同类型的连接使用同一个会话
    connection_id = f"{connection_type}_{session_id}_{participant_type}"

    # 获取Redis服务
    redis = get_webrtc_redis()

    # 检查是否已有相同类型的连接，如果有则先清理
    all_connections = await redis.get_all_connections()
    existing_connections = [conn_id for conn_id, conn_info in all_connections.items()
                          if conn_info.get("participant_type") == participant_type and
                             conn_info.get("session_id") == session_id]

    for existing_conn_id in existing_connections:
        logger.warning(f"WebRTC V2: 清理现有{participant_type}连接: {existing_conn_id}")
        await cleanup_connection(existing_conn_id)

    logger.info(f"WebRTC V2: {participant_type}连接到{target_id} (类型: {connection_type})")

    # 存储连接信息
    connection_info = {
        "participant_type": participant_type,
        "target_id": target_id,
        "connection_type": connection_type,
        "session_id": session_id,
        "user_info": user_info,
        "connected_at": datetime.now(),
        "features": features.split(",") if features else [],
        "is_active": True,
        "process_id": PROCESS_ID  # 添加进程ID标识
    }

    # 使用连接管理器存储连接信息和WebSocket
    await connection_manager.add_connection_info(connection_id, connection_info)
    await connection_manager.add_websocket_connection(connection_id, websocket)

    # 同时存储到全局字典以保持兼容性
    local_websockets[connection_id] = websocket

    # 创建或加入会话
    session_info = await redis.get_session(session_id)
    if not session_info:
        session_info = {
            "session_id": session_id,
            "connection_type": connection_type,
            "participants": {},
            "created_at": datetime.now(),
            "state": "waiting"
        }
        await redis.add_session(session_id, session_info)

    # 更新会话参与者
    session_info["participants"][participant_type] = connection_id
    await redis.update_session_participants(session_id, session_info["participants"])

    # 添加调试信息
    logger.info(f"DEBUG: Updated session {session_id} participants: {session_info['participants']}")
    all_connections = await redis.get_all_connections()
    logger.info(f"DEBUG: All active connections: {list(all_connections.keys())}")

    try:
        # 发送连接确认
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "payload": {
                "connection_id": connection_id,
                "session_id": session_id,
                "participant_type": participant_type,
                "connection_type": connection_type,
                "session_state": session_info["state"],
                "features": connection_info["features"]
            }
        }))

        logger.info(f"WebRTC V2: 连接已建立 {connection_id}")

        # 检查是否有对方连接，如果有则通知双方
        await check_and_notify_peer_connection(session_id, connection_id)

        # 消息处理循环
        while True:
            try:
                # 检查连接是否仍然活跃
                connection_info = await redis.get_connection(connection_id)
                if not connection_info or not connection_info.get("is_active", True):
                    logger.info(f"WebRTC V2: 连接{connection_id}不再活跃，正在关闭")
                    break

                data = await websocket.receive_text()
                message = json.loads(data)

                logger.info(f"WebRTC V2: 收到{message.get('type')}来自{connection_id}")

                await handle_webrtc_message(connection_id, message)

            except WebSocketDisconnect:
                logger.info(f"🔌 WebRTC V2: WebSocket disconnected for {connection_id}")
                break
            except ConnectionClosedError:
                logger.info(f"🔌 WebRTC V2: Connection closed for {connection_id}")
                break
            except Exception as e:
                logger.warning(f"🚀 WebRTC V2: Error receiving message from {connection_id}: {e}")
                break

    except WebSocketDisconnect:
        logger.info(f"🔌 WebRTC V2: WebSocket disconnected during setup for {connection_id}")
    except Exception as e:
        logger.warning(f"🚀 WebRTC V2: Connection error for {connection_id}: {e}")
    finally:
        # 清理连接
        await cleanup_connection(connection_id)


async def get_or_create_session(participant_type: str, target_id: str, connection_type: str) -> str:
    """获取或创建会话 - 智能匹配逻辑"""

    # 查找现有会话，看是否有匹配的对等方在等待
    for session_id, session in webrtc_sessions.items():
        if session["connection_type"] != connection_type:
            continue

        # 如果会话只有一个参与者，检查是否可以匹配
        if len(session["participants"]) == 1:
            existing_participant_type = list(session["participants"].keys())[0]
            existing_connection_id = list(session["participants"].values())[0]

            # 如果参与者类型不同，可能是匹配的对等方
            if existing_participant_type != participant_type:
                existing_connection = webrtc_connections.get(existing_connection_id)
                if existing_connection:
                    existing_target = existing_connection["target_id"]

                    # 检查是否互相匹配
                    # 用户的target_id应该是设备ID，设备的target_id应该是用户ID
                    if (participant_type == "user" and existing_participant_type == "device") or \
                       (participant_type == "device" and existing_participant_type == "user"):
                        logger.info(f"🔗 Found matching session {session_id} for {participant_type}")
                        return session_id

    # 没有找到匹配的会话，创建新会话
    new_session_id = str(uuid.uuid4())
    logger.info(f"🆕 Created new session {new_session_id} for {participant_type}")
    return new_session_id


async def check_and_notify_peer_connection(session_id: str, connection_id: str):
    """检查并通知对等连接"""
    redis = get_webrtc_redis()
    session = await redis.get_session(session_id)
    if not session:
        return

    # 如果有两个参与者，更新会话状态
    if len(session["participants"]) >= 2:
        session["state"] = "connecting"
        await redis.add_session(session_id, session)

        # 通知所有参与者对方已连接
        current_connection = await redis.get_connection(connection_id)
        for participant_type, conn_id in session["participants"].items():
            if conn_id != connection_id:
                websocket = local_websockets.get(conn_id)
                if websocket:
                    try:
                        await websocket.send_text(json.dumps({
                            "type": "peer_connected",
                            "payload": {
                                "session_id": session_id,
                                "peer_type": current_connection["participant_type"] if current_connection else "unknown",
                                "session_state": "connecting"
                            }
                        }))
                    except Exception as e:
                        logger.error(f"Failed to notify peer connection: {e}")


async def handle_webrtc_message(sender_connection_id: str, message: dict):
    """处理WebRTC消息 - 纯中继模式"""
    message_type = message.get("type")
    payload = message.get("payload", {})

    redis = get_webrtc_redis()
    sender_info = await redis.get_connection(sender_connection_id)
    if not sender_info or not sender_info.get("is_active", True):
        logger.warning(f"Sender connection {sender_connection_id} not found or inactive")
        return

    session_id = sender_info["session_id"]
    sender_type = sender_info["participant_type"]

    logger.info(f"[PROCESS] WebRTC V2: Processing {message_type} from {sender_type} in session {session_id}")

    # 特殊处理挂断消息
    if message_type == "hangup":
        await handle_hangup(sender_connection_id, session_id)
        return

    # 查找目标连接（对等方）
    target_connection_id = await find_peer_connection(session_id, sender_connection_id)

    if not target_connection_id:
        logger.warning(f"No peer found for {sender_connection_id} in session {session_id}")
        # 添加调试信息：显示当前会话状态
        redis = get_webrtc_redis()
        session = await redis.get_session(session_id)
        all_connections = await redis.get_all_connections()
        logger.warning(f"DEBUG: Session info: {session}")
        logger.warning(f"DEBUG: All connections: {list(all_connections.keys())}")
        return

    # 中继消息给对等方
    await relay_message_to_peer(target_connection_id, message)

    # 发送确认给发送方
    await send_confirmation(sender_connection_id, message_type)


async def handle_hangup(sender_connection_id: str, session_id: str):
    """处理挂断操作 - 主动断开所有相关连接"""
    logger.info(f"📞 WebRTC V2: Processing hangup from {sender_connection_id} in session {session_id}")

    redis = get_webrtc_redis()

    # 获取会话信息
    session = await redis.get_session(session_id)
    if not session:
        logger.warning(f"Session {session_id} not found for hangup")
        return

    # 获取所有参与者连接
    all_connections = []
    for participant_type, connection_id in session["participants"].items():
        connection_info = await redis.get_connection(connection_id)
        if connection_info:
            all_connections.append(connection_id)

    # 先通知所有参与者挂断消息
    hangup_message = {
        "type": "hangup",
        "payload": {
            "session_id": session_id,
            "reason": "peer_hangup"
        }
    }

    for connection_id in all_connections:
        if connection_id != sender_connection_id:  # 不给发送者发送挂断消息
            websocket = local_websockets.get(connection_id)
            if websocket:
                try:
                    await websocket.send_text(json.dumps(hangup_message))
                    logger.info(f"📞 WebRTC V2: Sent hangup notification to {connection_id}")
                except Exception as e:
                    logger.warning(f"Failed to send hangup to {connection_id}: {e}")

    # 给发送者发送确认
    await send_confirmation(sender_connection_id, "hangup")

    # 延迟一下让消息发送完成，然后主动关闭所有连接
    await asyncio.sleep(0.1)

    # 主动关闭所有WebSocket连接
    for connection_id in all_connections:
        websocket = local_websockets.get(connection_id)
        if websocket:
            try:
                await websocket.close(code=1000, reason="Call ended")
                logger.info(f"📞 WebRTC V2: Closed WebSocket for {connection_id}")
            except Exception as e:
                logger.warning(f"Failed to close WebSocket for {connection_id}: {e}")

    # 清理会话
    await redis.remove_session(session_id)
    logger.info(f"📞 WebRTC V2: Deleted session {session_id} after hangup")

    # 清理所有连接
    for connection_id in all_connections:
        await redis.remove_connection(connection_id)
        if connection_id in local_websockets:
            del local_websockets[connection_id]
        logger.info(f"📞 WebRTC V2: Cleaned up connection {connection_id}")


async def find_peer_connection(session_id: str, sender_connection_id: str) -> Optional[str]:
    """查找对等连接"""
    redis = get_webrtc_redis()
    session = await redis.get_session(session_id)
    if not session:
        return None

    # 查找不同参与者类型的连接
    for participant_type, connection_id in session["participants"].items():
        if connection_id != sender_connection_id:
            # 检查连接是否存在且活跃
            connection_info = await redis.get_connection(connection_id)
            if connection_info and connection_info.get("is_active", True):
                return connection_id

    return None


async def relay_message_to_peer(target_connection_id: str, message: dict):
    """中继消息给对等方"""
    redis = get_webrtc_redis()
    target_info = await redis.get_connection(target_connection_id)
    if not target_info or not target_info.get("is_active", True):
        logger.warning(f"Target connection {target_connection_id} not found or inactive")
        return

    # 获取本地WebSocket连接
    websocket = local_websockets.get(target_connection_id)
    if not websocket:
        logger.warning(f"Local WebSocket not found for {target_connection_id}")
        await cleanup_connection(target_connection_id)
        return

    try:
        await websocket.send_text(json.dumps(message))
        logger.info(f"✅ WebRTC V2: Relayed {message.get('type')} to {target_connection_id}")
    except Exception as e:
        logger.warning(f"Failed to relay message to {target_connection_id}: {e}")
        # 如果发送失败，清理这个连接
        await cleanup_connection(target_connection_id)


async def send_confirmation(connection_id: str, message_type: str):
    """发送确认消息"""
    redis = get_webrtc_redis()
    connection_info = await redis.get_connection(connection_id)
    if not connection_info or not connection_info.get("is_active", True):
        return

    # 获取本地WebSocket连接
    websocket = local_websockets.get(connection_id)
    if not websocket:
        logger.warning(f"Local WebSocket not found for confirmation {connection_id}")
        return

    try:
        await websocket.send_text(json.dumps({
            "type": "message_sent",
            "payload": {
                "original_type": message_type,
                "timestamp": datetime.now().isoformat()
            }
        }))
    except Exception as e:
        logger.warning(f"Failed to send confirmation to {connection_id}: {e}")
        # 如果发送失败，清理这个连接
        await cleanup_connection(connection_id)


async def cleanup_connection(connection_id: str):
    """清理连接"""
    redis = get_webrtc_redis()
    connection_info = await redis.get_connection(connection_id)
    if not connection_info:
        return

    # 检查连接是否已经被清理
    if not connection_info.get("is_active", True):
        return

    session_id = connection_info["session_id"]
    participant_type = connection_info["participant_type"]

    # 从Redis中移除连接
    await redis.remove_connection(connection_id)

    # 从本地WebSocket存储中移除
    if connection_id in local_websockets:
        del local_websockets[connection_id]

    # 从会话中移除
    session = await redis.get_session(session_id)
    if session and participant_type in session["participants"]:
        del session["participants"][participant_type]

        # 如果会话中没有参与者了，删除会话
        if not session["participants"]:
            await redis.remove_session(session_id)
            logger.info(f"🗑️ WebRTC V2: Deleted empty session {session_id}")
        else:
            # 更新会话参与者
            await redis.update_session_participants(session_id, session["participants"])

            # 通知其他参与者对方已断开
            for remaining_participant_type, remaining_connection_id in session["participants"].items():
                remaining_connection = await redis.get_connection(remaining_connection_id)
                if remaining_connection and remaining_connection.get("is_active", True):
                    websocket = local_websockets.get(remaining_connection_id)
                    if websocket:
                        try:
                            await websocket.send_text(json.dumps({
                                "type": "peer_disconnected",
                                "payload": {
                                    "session_id": session_id,
                                    "disconnected_peer_type": participant_type
                                }
                            }))
                        except Exception as e:
                            logger.warning(f"Failed to notify peer disconnection: {e}")
                            # 如果通知失败，也清理这个连接
                            await cleanup_connection(remaining_connection_id)

    logger.info(f"WebRTC: 已清理连接 {connection_id}")


# ==================== 管理API端点 ====================

@router.get("/connections")
async def get_active_connections():
    """获取所有活跃连接"""
    try:
        redis = get_webrtc_redis()
        all_connections = await redis.get_all_connections()

        connections_summary = {}
        for conn_id, conn_info in all_connections.items():
            # 过滤敏感信息，只返回必要的连接信息
            connections_summary[conn_id] = {
                "participant_type": conn_info.get("participant_type"),
                "target_id": conn_info.get("target_id"),
                "connection_type": conn_info.get("connection_type"),
                "session_id": conn_info.get("session_id"),
                "connected_at": conn_info.get("connected_at"),
                "is_active": conn_info.get("is_active", True),
                "process_id": conn_info.get("process_id")
            }

        return {
            "success": True,
            "total_connections": len(connections_summary),
            "connections": connections_summary
        }
    except Exception as e:
        logger.error(f"Error getting active connections: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connections/{connection_id}")
async def get_connection_info(connection_id: str):
    """获取特定连接信息"""
    try:
        redis = get_webrtc_redis()
        connection_info = await redis.get_connection(connection_id)

        if not connection_info:
            raise HTTPException(status_code=404, detail="Connection not found")

        # 过滤敏感信息
        safe_info = {
            "connection_id": connection_id,
            "participant_type": connection_info.get("participant_type"),
            "target_id": connection_info.get("target_id"),
            "connection_type": connection_info.get("connection_type"),
            "session_id": connection_info.get("session_id"),
            "connected_at": connection_info.get("connected_at"),
            "is_active": connection_info.get("is_active", True),
            "features": connection_info.get("features", []),
            "process_id": connection_info.get("process_id")
        }

        return {
            "success": True,
            "connection": safe_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting connection info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/connections/{connection_id}/terminate")
async def terminate_connection(connection_id: str):
    """终止特定连接"""
    try:
        redis = get_webrtc_redis()
        connection_info = await redis.get_connection(connection_id)

        if not connection_info:
            raise HTTPException(status_code=404, detail="Connection not found")

        # 标记连接为非活跃状态
        connection_info["is_active"] = False
        await redis.add_connection(connection_id, connection_info)

        # 如果有本地WebSocket连接，关闭它
        if connection_id in local_websockets:
            try:
                websocket = local_websockets[connection_id]
                await websocket.close(code=1000, reason="Connection terminated by admin")
            except Exception as ws_error:
                logger.warning(f"Error closing WebSocket for {connection_id}: {ws_error}")

        # 清理连接
        await cleanup_connection(connection_id)

        return {
            "success": True,
            "message": f"Connection {connection_id} terminated successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error terminating connection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/features")
async def get_available_features():
    """获取可用功能列表"""
    return {
        "connection_types": [
            {
                "type": "video",
                "name": "视频通话",
                "description": "完整的音视频通话功能",
                "features": ["video_streaming", "audio_streaming", "data_channel"],
                "status": "available"
            },
            {
                "type": "audio",
                "name": "音频通话",
                "description": "纯音频通话功能",
                "features": ["audio_streaming", "data_channel"],
                "status": "available"
            },
            {
                "type": "signaling",
                "name": "基础信令",
                "description": "基础WebRTC信令交换",
                "features": ["basic_messaging"],
                "status": "available"
            },
            {
                "type": "screen",
                "name": "屏幕共享",
                "description": "屏幕共享功能",
                "features": ["screen_streaming", "data_channel"],
                "status": "planned"
            },
            {
                "type": "data",
                "name": "数据通道",
                "description": "纯数据传输通道",
                "features": ["data_channel", "file_transfer"],
                "status": "planned"
            }
        ],
        "feature_modules": [
            {
                "module": "messaging",
                "name": "基础消息",
                "description": "文本消息传输",
                "status": "available"
            },
            {
                "module": "media",
                "name": "媒体流",
                "description": "音视频流传输",
                "status": "available"
            },
            {
                "module": "data",
                "name": "数据通道",
                "description": "二进制数据传输",
                "status": "available"
            },
            {
                "module": "recording",
                "name": "录制功能",
                "description": "通话录制和回放",
                "status": "planned"
            },
            {
                "module": "analytics",
                "name": "分析统计",
                "description": "通话质量分析和统计",
                "status": "planned"
            }
        ]
    }


@router.get("/stats")
async def get_webrtc_stats():
    """获取WebRTC统计信息"""
    try:
        redis = get_webrtc_redis()
        all_connections = await redis.get_all_connections()
        all_sessions = await redis.get_all_sessions()

        # 统计不同连接类型的数量
        connection_type_stats = {}
        participant_type_stats = {}
        feature_stats = {}

        for conn_info in all_connections.values():
            # 连接类型统计
            conn_type = conn_info.get("connection_type", "unknown")
            connection_type_stats[conn_type] = connection_type_stats.get(conn_type, 0) + 1

            # 参与者类型统计
            participant_type = conn_info.get("participant_type", "unknown")
            participant_type_stats[participant_type] = participant_type_stats.get(participant_type, 0) + 1

            # 功能使用统计
            features = conn_info.get("features", [])
            for feature in features:
                feature_stats[feature] = feature_stats.get(feature, 0) + 1

        # 会话状态统计
        session_state_stats = {}
        for session_info in all_sessions.values():
            state = session_info.get("state", "unknown")
            session_state_stats[state] = session_state_stats.get(state, 0) + 1

        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "total_connections": len(all_connections),
            "total_sessions": len(all_sessions),
            "active_local_websockets": len(local_websockets),
            "connection_types": connection_type_stats,
            "participant_types": participant_type_stats,
            "feature_usage": feature_stats,
            "session_states": session_state_stats,
            "redis_health": await redis.health_check() if hasattr(redis, 'health_check') else True
        }

    except Exception as e:
        logger.error(f"Error getting WebRTC stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def webrtc_health_check():
    """WebRTC健康检查"""
    try:
        redis = get_webrtc_redis()

        # 基础健康检查
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "active_connections": len(local_websockets),
            "redis_connected": True,
            "process_id": PROCESS_ID
        }

        # 检查Redis连接
        try:
            await redis.get_all_connections()
            health_status["redis_connected"] = True
        except Exception as redis_error:
            health_status["redis_connected"] = False
            health_status["status"] = "degraded"
            health_status["issues"] = [f"Redis connection failed: {str(redis_error)}"]
            logger.warning(f"Redis health check failed: {redis_error}")

        # 检查WebSocket连接状态
        broken_connections = 0
        for conn_id, websocket in local_websockets.items():
            try:
                # 简单的连接状态检查
                if websocket.client_state.name != "CONNECTED":
                    broken_connections += 1
            except Exception:
                broken_connections += 1

        if broken_connections > 0:
            health_status["broken_websockets"] = broken_connections
            if broken_connections > len(local_websockets) * 0.5:  # 超过50%连接异常
                health_status["status"] = "degraded"
                if "issues" not in health_status:
                    health_status["issues"] = []
                health_status["issues"].append(f"{broken_connections} broken WebSocket connections")

        return health_status

    except Exception as e:
        logger.error(f"WebRTC health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "process_id": PROCESS_ID
        }


# 保留原有的测试函数以便兼容
async def handle_test_message(sender_id: str, message: dict):
    """处理测试消息"""
    message_type = message.get("type")
    payload = message.get("payload", {})
    
    logger.error(f"🧪 TEST: Processing {message_type} from {sender_id}")
    
    if message_type == "test_offer":
        # 模拟处理Offer
        logger.error(f"[TEST] Processing test offer from {sender_id}")
        
        # 查找目标客户端（简单的配对逻辑）
        target_id = find_test_target(sender_id)
        if target_id:
            # 转发Offer给目标
            await send_test_message(target_id, {
                "type": "test_offer_received",
                "payload": {
                    "from": sender_id,
                    "offer_data": payload
                }
            })
            
            # 模拟自动生成Answer
            await asyncio.sleep(1)  # 模拟处理时间
            
            answer_data = {
                "type": "answer",
                "sdp": f"mock_answer_sdp_for_{sender_id}",
                "from_target": target_id
            }
            
            # 发送Answer给发送方
            await send_test_message(sender_id, {
                "type": "test_answer",
                "payload": answer_data
            })
            
            logger.error(f"[TEST] Sent mock answer to {sender_id}")
        else:
            logger.error(f"[TEST] No target found for {sender_id}")
    
    elif message_type == "test_answer":
        # 处理Answer
        logger.error(f"🧪 TEST: Processing test answer from {sender_id}")
        
        target_id = find_test_target(sender_id)
        if target_id:
            await send_test_message(target_id, {
                "type": "test_answer_received",
                "payload": {
                    "from": sender_id,
                    "answer_data": payload
                }
            })
    
    elif message_type == "test_ice_candidate":
        # 处理ICE候选
        logger.error(f"🧪 TEST: Processing ICE candidate from {sender_id}")
        
        target_id = find_test_target(sender_id)
        if target_id:
            await send_test_message(target_id, {
                "type": "test_ice_candidate_received",
                "payload": {
                    "from": sender_id,
                    "candidate_data": payload
                }
            })
    
    elif message_type == "test_message":
        # 处理普通消息
        content = payload.get("content", "")
        logger.error(f"🧪 TEST: Message from {sender_id}: {content}")
        
        # 广播给所有其他连接
        for client_id, conn_info in test_connections.items():
            if client_id != sender_id:
                await send_test_message(client_id, {
                    "type": "test_message_received",
                    "payload": {
                        "from": sender_id,
                        "content": content
                    }
                })
    
    else:
        logger.error(f"🧪 TEST: Unknown message type {message_type} from {sender_id}")


def find_test_target(sender_id: str) -> str:
    """查找测试目标（简单的配对逻辑）"""
    sender_info = test_connections.get(sender_id)
    if not sender_info:
        return None
    
    sender_type = sender_info["client_type"]
    
    # 查找不同类型的客户端
    for client_id, conn_info in test_connections.items():
        if client_id != sender_id and conn_info["client_type"] != sender_type:
            return client_id
    
    return None


async def send_test_message(client_id: str, message: dict):
    """发送测试消息给指定客户端"""
    if client_id not in test_connections:
        logger.error(f"[TEST] Client {client_id} not found")
        return

    try:
        websocket = test_connections[client_id]["websocket"]
        await websocket.send_text(json.dumps(message))
        logger.error(f"[TEST] Sent message to {client_id}: {message['type']}")
    except Exception as e:
        logger.error(f"[TEST] Failed to send message to {client_id}: {e}")


# ==================== 设备管理API ====================

@router.get("/devices")
async def get_user_devices(
    include_inactive: bool = Query(False, description="是否包含非活跃设备"),
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_session)
):
    """获取用户绑定的设备列表 - 使用安全的Authorization头认证"""
    try:
        # 获取设备列表
        binding_service = DeviceService(db_session)
        devices = binding_service.get_user_devices(current_user.id, include_inactive)

        return {
            "success": True,
            "data": {
                "user_id": current_user.id,
                "devices": devices,
                "total_count": len(devices)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户设备列表失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/device/{device_id}/status")
async def get_device_status(
    device_id: str,
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_session)
):
    """获取设备状态 - 使用安全的Authorization头认证"""
    try:
        # 检查权限
        device_service = DeviceService(db_session)
        has_permission = device_service.check_user_device_permission(
            user_id=current_user.id,
            device_id=device_id,
            required_permission=PermissionLevel.READ
        )

        if not has_permission:
            raise HTTPException(status_code=403, detail="Access denied")

        # 获取设备在线状态
        redis = get_webrtc_redis()
        all_connections = await redis.get_all_connections()

        # 查找设备连接
        device_online = False
        device_connection = None
        for conn_id, conn_info in all_connections.items():
            if (conn_info.get("participant_type") == "device" and
                conn_info.get("user_info", {}).get("device_id") == device_id):
                device_online = True
                device_connection = {
                    "connection_id": conn_id,
                    "connected_at": conn_info.get("connected_at"),
                    "session_id": conn_info.get("session_id"),
                    "process_id": conn_info.get("process_id")
                }
                break

        return {
            "success": True,
            "data": {
                "device_id": device_id,
                "online": device_online,
                "connection": device_connection,
                "last_check": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备状态失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/device/{device_id}/bind")
async def bind_device(
    device_id: str,
    device_name: str = Query(None, description="设备显示名称"),
    permission_level: str = Query("control", description="权限级别"),
    expires_days: int = Query(None, description="过期天数"),
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_session)
):
    """绑定设备到用户 - 使用安全的Authorization头认证"""
    try:

        # 验证权限级别
        try:
            permission = PermissionLevel(permission_level)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid permission level")

        # 绑定设备
        device_service = DeviceService(db_session)
        result = device_service.bind_device_to_user(
            user_id=current_user.id,
            device_id=device_id,
            binding_code="",  # 直接绑定不需要绑定码
            permission_level=permission
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return {
            "success": True,
            "message": f"Device {device_id} bound successfully",
            "data": {
                "device_id": device_id,
                "device_name": device_name or device_id,
                "permission_level": permission_level,
                "bound_at": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"绑定设备失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/device/{device_id}/bind")
async def unbind_device(
    device_id: str,
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_session)
):
    """解绑设备 - 使用安全的Authorization头认证"""
    try:
        # 解绑设备
        binding_service = DeviceService(db_session)
        success = binding_service.unbind_device_from_user(user_id=current_user.id, device_id=device_id)

        if not success:
            raise HTTPException(status_code=400, detail="Failed to unbind device or device not found")

        return {
            "success": True,
            "message": f"Device {device_id} unbound successfully",
            "data": {
                "device_id": device_id,
                "unbound_at": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解绑设备失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# ==================== 设备绑定码API ====================

@router.post("/device/{device_id}/generate-binding-code")
async def generate_device_binding_code(
    device_id: str,
    expires_minutes: int = Query(30, description="绑定码过期时间（分钟）"),
    db_session: Session = Depends(get_session)
):
    """为设备生成绑定码"""
    try:
        device_service = DeviceService(db_session)
        result = device_service.generate_binding_code(device_id, expires_minutes)

        if not result:
            raise HTTPException(status_code=404, detail="Device not found or failed to generate binding code")

        return {
            "success": True,
            "message": "Binding code generated successfully",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成设备绑定码失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/device/bind-with-code")
async def bind_device_with_code(
    device_id: str = Query(..., description="设备ID"),
    binding_code: str = Query(..., description="6位绑定码"),
    device_name: str = Query(None, description="设备显示名称"),
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_session)
):
    """使用绑定码绑定设备 - 使用安全的Authorization头认证"""
    try:
        # 使用绑定码绑定设备
        device_service = DeviceService(db_session)
        result = device_service.bind_device_with_code(
            user_id=current_user.id,
            device_id=device_id,
            binding_code=binding_code,
            permission_level=PermissionLevel.CONTROL
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return {
            "success": True,
            "message": f"Device {device_id} bound successfully",
            "data": {
                "device_id": device_id,
                "device_name": device_name or device_id,
                "bound_at": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"使用绑定码绑定设备失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/device/{device_id}/binding-info")
async def get_device_binding_info(
    device_id: str,
    db_session: Session = Depends(get_session)
):
    """获取设备绑定信息（包括绑定码和二维码）"""
    try:
        device_service = DeviceService(db_session)
        result = device_service.get_device_binding_info(device_id)

        if not result:
            raise HTTPException(status_code=404, detail="Device not found")

        return {
            "success": True,
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备绑定信息失败: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# ==================== 系统状态API ====================

@router.get("/status")
async def get_webrtc_v2_status():
    """获取WebRTC V2状态"""
    redis = get_webrtc_redis()

    # 获取Redis统计信息
    stats = await redis.get_stats()

    # 获取所有连接和会话
    connections = await redis.get_all_connections()
    sessions = await redis.get_all_sessions()

    return {
        "active_connections": stats["active_connections"],
        "active_sessions": stats["active_sessions"],
        "local_websockets": len(local_websockets),
        "process_id": PROCESS_ID,
        "redis_memory_usage": stats.get("redis_memory_usage", 0),
        "connections": {
            conn_id: {
                "participant_type": info["participant_type"],
                "target_id": info["target_id"],
                "connection_type": info["connection_type"],
                "session_id": info["session_id"],
                "connected_at": info["connected_at"].isoformat() if isinstance(info["connected_at"], datetime) else info["connected_at"],
                "features": info["features"],
                "process_id": info.get("process_id", "unknown")
            }
            for conn_id, info in connections.items()
        },
        "sessions": {
            session_id: {
                "connection_type": session["connection_type"],
                "state": session["state"],
                "participants": list(session["participants"].keys()),
                "created_at": session["created_at"].isoformat() if isinstance(session["created_at"], datetime) else session["created_at"]
            }
            for session_id, session in sessions.items()
        }
    }


@router.get("/sessions/{session_id}")
async def get_session_info(session_id: str):
    """获取特定会话信息"""
    session = webrtc_sessions.get(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    return {
        "session_id": session_id,
        "connection_type": session["connection_type"],
        "state": session["state"],
        "participants": {
            participant_type: {
                "connection_id": conn_id,
                "connection_info": {
                    "target_id": webrtc_connections[conn_id]["target_id"],
                    "connected_at": webrtc_connections[conn_id]["connected_at"].isoformat(),
                    "features": webrtc_connections[conn_id]["features"]
                } if conn_id in webrtc_connections else None
            }
            for participant_type, conn_id in session["participants"].items()
        },
        "created_at": session["created_at"].isoformat()
    }


@router.post("/sessions/{session_id}/close")
async def close_session(session_id: str):
    """关闭会话"""
    session = webrtc_sessions.get(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # 通知所有参与者会话关闭
    for participant_type, connection_id in session["participants"].items():
        if connection_id in webrtc_connections:
            try:
                await webrtc_connections[connection_id]["websocket"].send_text(json.dumps({
                    "type": "session_closed",
                    "payload": {
                        "session_id": session_id,
                        "reason": "Session closed by admin"
                    }
                }))
                # 关闭WebSocket连接
                await webrtc_connections[connection_id]["websocket"].close()
            except Exception as e:
                logger.error(f"Failed to close connection {connection_id}: {e}")

    # 删除会话
    del webrtc_sessions[session_id]

    return {"message": f"Session {session_id} closed successfully"}


@router.get("/health")
async def webrtc_v2_health():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "WebRTC V2",
        "active_connections": len(webrtc_connections),
        "active_sessions": len(webrtc_sessions),
        "timestamp": datetime.now().isoformat()
    }


# 保留测试端点以便兼容
@router.get("/test/status")
async def get_test_status():
    """获取测试连接状态"""
    return {
        "active_connections": len(test_connections),
        "connections": {
            client_id: {
                "client_type": info["client_type"],
                "connected_at": info["connected_at"]
            }
            for client_id, info in test_connections.items()
        }
    }


@router.get("/ping")
async def test_ping():
    """简单的ping测试"""
    return {"message": "pong", "status": "ok"}
