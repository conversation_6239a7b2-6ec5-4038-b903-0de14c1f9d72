from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from typing import List

from database import get_session
from schemas.order import OrderResponse, OrderStatusUpdate, KitchenStatusUpdate
from services.shop_service import ShopService
from services.order_state_machine import OrderStateMachine, OrderWorkflowHelper
from dependencies import require_staff
from models.user import User
from models.order import Order, OrderStatus
from tasks.notification_tasks import notify_downstream_on_pickup_ready, send_order_status_change_notification
from utils.logger import get_logger
from utils.pagination import (
    Paginator, PaginationParams, PaginatedResponse,
    OrderFilterParams, apply_order_filters
)

logger = get_logger(__name__)
router = APIRouter(tags=["Staff"])


@router.get("/orders/pending", response_model=PaginatedResponse[OrderResponse])
async def get_pending_orders(
    pagination: PaginationParams = Depends(),
    filters: OrderFilterParams = Depends(),
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """
    获取待处理订单（分页）- 保留向后兼容性

    返回已支付但未开始处理的订单
    支持分页和过滤功能
    """
    paginator = Paginator(session)

    # 构建基础查询 - 只查询已支付的订单
    statement = select(Order).where(Order.status == OrderStatus.PAID)

    # 应用过滤条件
    statement = apply_order_filters(statement, filters)

    # 数据转换函数
    def transform_order(order: Order) -> OrderResponse:
        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            payment_transaction_id=order.payment_transaction_id,
            video_stream_id=order.video_stream_id,
            video_start_time=order.video_start_time,
            video_end_time=order.video_end_time,
            created_at=order.created_at,
            updated_at=order.updated_at
        )

    # 执行分页查询
    return paginator.paginate(statement, pagination, transform_order)


@router.get("/orders/active", response_model=PaginatedResponse[OrderResponse])
async def get_active_orders(
    pagination: PaginationParams = Depends(),
    filters: OrderFilterParams = Depends(),
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """
    获取活动任务列表（分页）- 符合PRD要求

    返回状态为PAID(待开始处理)或PROCESSING(正在处理中)的订单
    支持分页和过滤功能
    """
    paginator = Paginator(session)

    # 构建基础查询 - 查询活动状态的订单
    statement = select(Order).where(
        Order.status.in_([OrderStatus.PAID, OrderStatus.PROCESSING])
    )

    # 应用过滤条件
    statement = apply_order_filters(statement, filters)

    # 数据转换函数
    def transform_order(order: Order) -> OrderResponse:
        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            payment_transaction_id=order.payment_transaction_id,
            video_stream_id=order.video_stream_id,
            video_start_time=order.video_start_time,
            video_end_time=order.video_end_time,
            created_at=order.created_at,
            updated_at=order.updated_at
        )

    # 执行分页查询
    result = paginator.paginate(statement, pagination, transform_order)

    logger.info(f"Staff {current_user.username} 查询活动任务，找到 {result.total} 个任务")

    return result


@router.put("/orders/{order_id}/status")
async def update_order_status(
    order_id: str,
    status_update: OrderStatusUpdate,
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """Update order processing status (保留向后兼容性)"""
    shop_service = ShopService(session)

    # Validate status
    valid_statuses = ["processing", "completed", "cancelled"]
    if status_update.status not in valid_statuses:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Must be one of: {valid_statuses}"
        )

    success = shop_service.update_order_status(order_id, status_update.status)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    logger.info(f"Staff {current_user.username} updated order {order_id} to {status_update.status}")

    return {"message": "Order status updated successfully"}


@router.put("/orders/{order_id}/kitchen_status")
async def update_kitchen_status(
    order_id: str,
    status_update: KitchenStatusUpdate,
    current_user: User = Depends(require_staff),
    session: Session = Depends(get_session)
):
    """
    更新备料状态 - 符合PRD要求

    支持视频录制和严格的状态转换验证
    """
    try:
        shop_service = ShopService(session)

        # 获取订单信息
        order = shop_service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证状态转换权限
        is_valid, error_msg = OrderStateMachine.validate_status_update_for_role(
            order.status,
            status_update.status,
            current_user.role
        )

        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=error_msg
            )

        # 检查是否需要视频录制
        requires_video = OrderWorkflowHelper.requires_video_recording(
            order.status,
            status_update.status
        )

        if requires_video and not status_update.video_stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="开始备料时必须提供视频流ID"
            )

        # 更新订单状态
        success = shop_service.update_order_status_with_video(
            order_id,
            status_update.status,
            video_stream_id=status_update.video_stream_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新订单状态失败"
            )

        # 根据状态变化触发相应的异步通知
        try:
            # 发送状态变更通知
            user_info = {
                "username": current_user.username,
                "role": current_user.role,
                "user_id": str(current_user.id)
            }
            send_order_status_change_notification.delay(
                order_id,
                order.status,
                status_update.status,
                user_info
            )

            # 如果是备料完成，发送下游通知
            if status_update.status == OrderStatus.READY_FOR_PICKUP:
                order_data = {
                    "id": order.id,
                    "total_amount": order.total_amount,
                    "device_id": order.device_id,
                    "video_stream_id": status_update.video_stream_id,
                    "status": status_update.status
                }
                notify_downstream_on_pickup_ready.delay(order_id, order_data)
                logger.info(f"已触发下游通知任务: {order_id}")

        except Exception as e:
            logger.error(f"触发异步通知任务失败: {order_id}, 错误: {str(e)}")
            # 不影响主流程，只记录错误

        logger.info(
            f"Staff {current_user.username} 更新订单 {order_id} "
            f"从 {order.status} 到 {status_update.status}"
        )

        return {
            "success": True,
            "message": "备料状态更新成功",
            "order_id": order_id,
            "old_status": order.status,
            "new_status": status_update.status,
            "status_display": OrderWorkflowHelper.get_status_display_name(status_update.status)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating kitchen status for order {order_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新备料状态时发生错误: {str(e)}"
        )
