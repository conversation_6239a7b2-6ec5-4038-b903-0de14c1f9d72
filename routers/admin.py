from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select
from typing import List

from database import get_session
from schemas.user import UserResponse, Token
from schemas.device import DeviceResponse
from schemas.order import OrderResponse
from services.auth_service import AuthService
from dependencies import require_admin
from models.user import User
from models.user_device_binding import Device
from models.order import Order
from utils.security import create_access_token
from utils.logger import get_logger, get_log_info, cleanup_old_logs
from utils.pagination import (
    Paginator, PaginationParams, PaginatedResponse,
    OrderFilterParams, UserFilterParams, DeviceFilterParams,
    apply_order_filters, apply_user_filters, apply_device_filters
)
from config import settings
from datetime import timedelta

logger = get_logger(__name__)
router = APIRouter(tags=["Admin"])


# 删除重复的admin登录接口
# 所有用户（包括admin）都应该使用统一的 /api/v1/auth/login 接口
# 这符合KISS原则和角色统一管理的设计理念
#
# 原因：
# 1. 避免接口重复，减少维护成本
# 2. 统一认证流程，提高安全性
# 3. 简化前端调用逻辑
# 4. 符合RESTful API设计原则


@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def get_all_users(
    pagination: PaginationParams = Depends(),
    filters: UserFilterParams = Depends(),
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """
    获取用户列表（分页）

    支持分页和过滤：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20，最大100）
    - search: 搜索用户名
    - role: 角色过滤
    - is_active: 激活状态过滤
    - sort_by: 排序字段
    - sort_order: 排序方向（asc/desc）
    """
    paginator = Paginator(session)

    # 构建基础查询
    statement = select(User)

    # 应用过滤条件
    statement = apply_user_filters(statement, filters)

    # 数据转换函数
    def transform_user(user: User) -> UserResponse:
        return UserResponse(
            id=user.id,
            username=user.username,
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at
        )

    # 执行分页查询
    return paginator.paginate(statement, pagination, transform_user)


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user_details(
    user_id: str,
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get user details"""
    statement = select(User).where(User.id == user_id)
    user = session.exec(statement).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserResponse(
        id=user.id,
        username=user.username,
        role=user.role,
        is_active=user.is_active,
        created_at=user.created_at
    )


@router.get("/devices", response_model=PaginatedResponse[DeviceResponse])
async def get_all_devices(
    pagination: PaginationParams = Depends(),
    filters: DeviceFilterParams = Depends(),
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """
    获取设备列表（分页）

    支持分页和过滤：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20，最大100）
    - search: 搜索设备ID或设备名称
    - device_status: 设备状态过滤
    - sort_by: 排序字段
    - sort_order: 排序方向（asc/desc）
    """
    paginator = Paginator(session)

    # 构建基础查询
    statement = select(Device)

    # 应用过滤条件
    statement = apply_device_filters(statement, filters)

    # 数据转换函数
    def transform_device(device: Device) -> DeviceResponse:
        return DeviceResponse(
            device_id=device.device_id,
            device_name=device.device_name,
            status=device.status,
            last_heartbeat_at=device.last_heartbeat_at
        )

    # 执行分页查询
    return paginator.paginate(statement, pagination, transform_device)


@router.get("/orders", response_model=PaginatedResponse[OrderResponse])
async def get_all_orders(
    pagination: PaginationParams = Depends(),
    filters: OrderFilterParams = Depends(),
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """
    获取订单列表（分页）

    支持分页和过滤：
    - page: 页码（默认1）
    - page_size: 每页数量（默认20，最大100）
    - search: 搜索订单ID或设备ID
    - status: 订单状态过滤
    - device_id: 设备ID过滤
    - min_amount: 最小金额过滤
    - max_amount: 最大金额过滤
    - created_from: 创建时间起始
    - created_to: 创建时间结束
    - sort_by: 排序字段
    - sort_order: 排序方向（asc/desc）
    """
    paginator = Paginator(session)

    # 构建基础查询
    statement = select(Order)

    # 应用过滤条件
    statement = apply_order_filters(statement, filters)

    # 数据转换函数
    def transform_order(order: Order) -> OrderResponse:
        return OrderResponse(
            id=order.id,
            device_id=order.device_id,
            total_amount=order.total_amount,
            status=order.status,
            payment_transaction_id=order.payment_transaction_id,
            video_stream_id=order.video_stream_id,
            video_start_time=order.video_start_time,
            video_end_time=order.video_end_time,
            created_at=order.created_at,
            updated_at=order.updated_at
        )

    # 执行分页查询
    return paginator.paginate(statement, pagination, transform_order)


@router.get("/system_status")
async def get_system_status(
    current_user: User = Depends(require_admin),
    session: Session = Depends(get_session)
):
    """Get system overview status"""
    
    # Count active devices
    active_devices_stmt = select(Device).where(Device.status == "online")
    active_devices_count = len(session.exec(active_devices_stmt).all())
    
    # Count pending orders
    pending_orders_stmt = select(Order).where(Order.status.in_(["pending_payment", "paid"]))
    pending_orders_count = len(session.exec(pending_orders_stmt).all())
    
    # Count total users
    total_users_stmt = select(User)
    total_users_count = len(session.exec(total_users_stmt).all())
    
    return {
        "active_devices": active_devices_count,
        "pending_orders": pending_orders_count,
        "total_users": total_users_count,
        "system_status": "healthy"
    }


@router.get("/logs/info")
async def get_logs_info(current_user: User = Depends(require_admin)):
    """获取日志配置和状态信息"""
    try:
        log_info = get_log_info()
        return {
            "success": True,
            "data": log_info
        }
    except Exception as e:
        logger.error(f"获取日志信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志信息失败: {str(e)}")


@router.post("/logs/cleanup")
async def cleanup_logs(current_user: User = Depends(require_admin)):
    """手动清理旧日志文件"""
    try:
        cleanup_old_logs()
        log_info = get_log_info()
        return {
            "success": True,
            "message": "日志清理完成",
            "data": log_info
        }
    except Exception as e:
        logger.error(f"清理日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")
