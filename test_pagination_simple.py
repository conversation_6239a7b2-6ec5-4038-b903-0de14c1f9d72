#!/usr/bin/env python3
"""
简化的分页功能测试
专注于核心分页功能验证
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def get_admin_token():
    """获取管理员token"""
    login_data = {
        "username": "admin_user",
        "password": "adminpass"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
    except Exception as e:
        print(f"获取管理员token失败: {str(e)}")
    
    return None

def test_core_pagination():
    """测试核心分页功能"""
    print("🧪 测试核心分页功能")
    
    admin_token = get_admin_token()
    if not admin_token:
        print("❌ 无法获取管理员token")
        return False
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    # 测试用户列表分页
    print("\n--- 测试用户列表分页 ---")
    try:
        response = requests.get(f"{BASE_URL}/admin/users?page=1&page_size=3", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查分页响应结构
            required_fields = ["items", "total", "page", "page_size", "total_pages", "has_next", "has_prev"]
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                print(f"❌ 用户列表分页响应缺少字段: {missing_fields}")
                return False
            else:
                print(f"✅ 用户列表分页响应结构正确")
                print(f"  总数: {result['total']}")
                print(f"  当前页: {result['page']}")
                print(f"  每页: {result['page_size']}")
                print(f"  总页数: {result['total_pages']}")
                print(f"  有下一页: {result['has_next']}")
                print(f"  有上一页: {result['has_prev']}")
                print(f"  当前页数据条数: {len(result['items'])}")
                
                # 验证数据逻辑
                if result['page'] == 1 and result['has_prev']:
                    print("⚠️  第一页不应该有上一页")
                    return False
                
                if result['total'] <= result['page_size'] and result['has_next']:
                    print("⚠️  总数小于等于每页数量时不应该有下一页")
                    return False
                
                print("✅ 分页逻辑验证正确")
                
        else:
            print(f"❌ 用户列表分页请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 用户列表分页测试失败: {str(e)}")
        return False
    
    # 测试订单列表分页
    print("\n--- 测试订单列表分页 ---")
    try:
        response = requests.get(f"{BASE_URL}/admin/orders?page=1&page_size=5", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 订单列表分页正常")
            print(f"  总订单数: {result['total']}")
            print(f"  当前页订单数: {len(result['items'])}")
            
            # 测试过滤功能
            filter_response = requests.get(
                f"{BASE_URL}/admin/orders?page=1&page_size=10&status=paid", 
                headers=headers
            )
            if filter_response.status_code == 200:
                filter_result = filter_response.json()
                print(f"✅ 订单状态过滤正常，已支付订单: {filter_result['total']} 个")
            else:
                print(f"⚠️  订单状态过滤异常: {filter_response.status_code}")
                
        else:
            print(f"❌ 订单列表分页请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 订单列表分页测试失败: {str(e)}")
        return False
    
    return True

def test_pagination_parameters():
    """测试分页参数验证"""
    print("\n🧪 测试分页参数验证")
    
    admin_token = get_admin_token()
    if not admin_token:
        print("❌ 无法获取管理员token")
        return False
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    # 测试边界情况
    test_cases = [
        {"params": "?page=0&page_size=10", "desc": "页码为0", "expect": 422},
        {"params": "?page=1&page_size=0", "desc": "每页数量为0", "expect": 422},
        {"params": "?page=1&page_size=101", "desc": "每页数量超过最大值", "expect": 422},
        {"params": "?page=999&page_size=10", "desc": "页码超出范围", "expect": 200},
        {"params": "?page=-1&page_size=10", "desc": "负数页码", "expect": 422},
    ]
    
    success_count = 0
    
    for case in test_cases:
        params = case["params"]
        desc = case["desc"]
        expect = case["expect"]
        
        try:
            response = requests.get(f"{BASE_URL}/admin/users{params}", headers=headers)
            
            if response.status_code == expect:
                print(f"✅ {desc}: 正确返回状态码 {expect}")
                success_count += 1
            else:
                print(f"❌ {desc}: 期望 {expect}，实际 {response.status_code}")
                
        except Exception as e:
            print(f"❌ {desc}测试失败: {str(e)}")
    
    return success_count == len(test_cases)

def test_filter_and_sort():
    """测试过滤和排序功能"""
    print("\n🧪 测试过滤和排序功能")
    
    admin_token = get_admin_token()
    if not admin_token:
        print("❌ 无法获取管理员token")
        return False
    
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    # 测试用户角色过滤
    try:
        response = requests.get(f"{BASE_URL}/admin/users?role=admin&page=1&page_size=10", headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 用户角色过滤正常，管理员用户: {result['total']} 个")
        else:
            print(f"❌ 用户角色过滤失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 用户角色过滤测试失败: {str(e)}")
        return False
    
    # 测试订单排序
    try:
        response = requests.get(
            f"{BASE_URL}/admin/orders?sort_by=created_at&sort_order=desc&page=1&page_size=5", 
            headers=headers
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 订单排序正常，返回 {len(result['items'])} 条记录")
        else:
            print(f"❌ 订单排序失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 订单排序测试失败: {str(e)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始简化分页功能测试")
    print("="*50)
    
    results = []
    
    # 运行测试
    results.append(("核心分页功能", test_core_pagination()))
    results.append(("分页参数验证", test_pagination_parameters()))
    results.append(("过滤和排序", test_filter_and_sort()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 分页功能测试结果:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 分页功能实现成功！")
        print("\n📋 实现的功能:")
        print("✅ 统一的分页响应格式")
        print("✅ 页码和每页数量参数验证")
        print("✅ 完整的分页元数据（总数、页数、是否有上下页）")
        print("✅ 过滤功能（搜索、状态、角色等）")
        print("✅ 排序功能（多字段、升序/降序）")
        print("✅ 边界情况处理")
        
        print("\n📊 分页接口列表:")
        print("  - GET /api/v1/admin/users - 用户列表（分页）")
        print("  - GET /api/v1/admin/orders - 订单列表（分页）")
        print("  - GET /api/v1/staff/orders/pending - 待处理订单（分页）")
        print("  - GET /api/v1/staff/orders/active - 活动订单（分页）")
        
        print("\n🔧 分页参数:")
        print("  - page: 页码（默认1，最小1）")
        print("  - page_size: 每页数量（默认20，最大100）")
        print("  - search: 搜索关键词")
        print("  - status: 状态过滤")
        print("  - role: 角色过滤")
        print("  - sort_by: 排序字段")
        print("  - sort_order: 排序方向（asc/desc）")
        
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要进一步修复")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
