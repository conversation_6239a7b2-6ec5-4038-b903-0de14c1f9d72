#!/usr/bin/env python3
"""
测试订单创建功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_order_creation():
    """测试订单创建"""
    print("🧪 测试订单创建")
    
    # 创建订单数据
    order_data = {
        "device_id": "mobile-app-test",
        "total_amount": 35.0,
        "items": [
            {
                "product_id": "prod-001",
                "quantity": 2,
                "price": 21.0
            },
            {
                "product_id": "prod-002",
                "quantity": 1,
                "price": 14.0
            }
        ]
    }
    
    try:
        response = requests.post(f"{BASE_URL}/orders/", json=order_data)
        print(f"创建订单状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 订单创建成功!")
            print(f"订单ID: {result.get('id')}")
            print(f"设备ID: {result.get('device_id')}")
            print(f"总金额: {result.get('total_amount')}")
            print(f"状态: {result.get('status')}")
            print(f"创建时间: {result.get('created_at')}")
            
            return result.get('id')
        else:
            print(f"❌ 订单创建失败")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None

def test_order_query(order_id):
    """测试订单查询"""
    if not order_id:
        return
        
    print(f"\n🧪 测试订单查询: {order_id}")
    
    try:
        response = requests.get(f"{BASE_URL}/orders/{order_id}")
        print(f"查询订单状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 订单查询成功!")
            print(f"订单详情: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 订单查询失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 查询失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 开始订单创建测试")
    print("="*50)
    
    # 测试订单创建
    order_id = test_order_creation()
    
    # 测试订单查询
    test_order_query(order_id)
    
    print("\n✅ 订单测试完成")

if __name__ == "__main__":
    main()
