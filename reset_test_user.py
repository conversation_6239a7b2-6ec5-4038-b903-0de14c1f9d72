#!/usr/bin/env python3
"""
重置测试用户
"""

from sqlmodel import Session, select, create_engine
from models.user import User
from utils.security import get_password_hash
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def reset_test_user():
    """重置测试用户"""
    print("🔧 重置测试用户")
    
    try:
        # 创建数据库连接
        engine = create_engine(settings.database_url)
        
        with Session(engine) as session:
            # 查找现有的testuser
            statement = select(User).where(User.username == "testuser")
            existing_user = session.exec(statement).first()
            
            if existing_user:
                print(f"找到现有用户: {existing_user.username}")
                print(f"当前密码哈希: {existing_user.password_hash[:20]}...")
                
                # 更新密码
                new_password_hash = get_password_hash("testpass")
                existing_user.password_hash = new_password_hash
                existing_user.is_active = True
                
                session.commit()
                print("✅ 用户密码已重置")
                
            else:
                print("用户不存在，创建新用户...")
                
                # 创建新用户
                new_user = User(
                    username="testuser",
                    password_hash=get_password_hash("testpass"),
                    role="user",
                    is_active=True
                )
                
                session.add(new_user)
                session.commit()
                session.refresh(new_user)
                
                print(f"✅ 新用户创建成功: {new_user.username}")
            
            # 验证密码哈希
            from utils.security import verify_password
            
            updated_user = session.exec(select(User).where(User.username == "testuser")).first()
            if updated_user:
                is_valid = verify_password("testpass", updated_user.password_hash)
                print(f"密码验证结果: {is_valid}")
                
                if is_valid:
                    print("✅ 密码哈希验证成功")
                else:
                    print("❌ 密码哈希验证失败")
            
    except Exception as e:
        print(f"❌ 重置用户失败: {str(e)}")
        logger.error(f"Reset user error: {str(e)}")

def list_all_users():
    """列出所有用户"""
    print("\n📋 数据库中的所有用户:")
    
    try:
        engine = create_engine(settings.database_url)
        
        with Session(engine) as session:
            statement = select(User)
            users = session.exec(statement).all()
            
            if users:
                for user in users:
                    print(f"  - {user.username} (角色: {user.role}, 活跃: {user.is_active})")
            else:
                print("  没有找到任何用户")
                
    except Exception as e:
        print(f"❌ 查询用户失败: {str(e)}")

def test_password_functions():
    """测试密码函数"""
    print("\n🧪 测试密码函数")
    
    try:
        from utils.security import get_password_hash, verify_password
        
        test_password = "testpass"
        
        # 生成哈希
        hash1 = get_password_hash(test_password)
        hash2 = get_password_hash(test_password)
        
        print(f"密码: {test_password}")
        print(f"哈希1: {hash1[:30]}...")
        print(f"哈希2: {hash2[:30]}...")
        
        # 验证哈希
        verify1 = verify_password(test_password, hash1)
        verify2 = verify_password(test_password, hash2)
        verify_wrong = verify_password("wrongpass", hash1)
        
        print(f"验证正确密码1: {verify1}")
        print(f"验证正确密码2: {verify2}")
        print(f"验证错误密码: {verify_wrong}")
        
        if verify1 and verify2 and not verify_wrong:
            print("✅ 密码函数工作正常")
        else:
            print("❌ 密码函数有问题")
            
    except Exception as e:
        print(f"❌ 测试密码函数失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始重置测试用户")
    print("="*50)
    
    # 测试密码函数
    test_password_functions()
    
    # 列出现有用户
    list_all_users()
    
    # 重置测试用户
    reset_test_user()
    
    # 再次列出用户
    list_all_users()
    
    print("\n✅ 重置完成，现在可以使用 testuser/testpass 登录")

if __name__ == "__main__":
    main()
