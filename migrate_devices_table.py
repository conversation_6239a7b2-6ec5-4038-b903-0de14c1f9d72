#!/usr/bin/env python3
"""
设备表迁移脚本
添加缺失的绑定和WebRTC相关字段到devices表
"""

import sqlite3
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def get_db_path():
    """获取数据库路径"""
    db_url = settings.database_url
    if db_url.startswith("sqlite:///"):
        return db_url.replace("sqlite:///", "")
    else:
        # 如果是其他数据库类型，需要不同的处理方式
        raise ValueError(f"不支持的数据库类型: {db_url}")

def check_devices_table_structure():
    """检查当前devices表结构"""
    print("🔍 检查当前devices表结构")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='devices'")
        if not cursor.fetchone():
            print("❌ devices表不存在")
            return False, []
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(devices)")
        columns = cursor.fetchall()
        
        print("当前devices表字段:")
        column_names = []
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
            column_names.append(column[1])
        
        # 检查是否存在新字段
        required_fields = [
            'binding_code', 
            'binding_code_expires_at', 
            'binding_qr_code_data',
            'active_webrtc_user_id',
            'webrtc_session_id', 
            'webrtc_connected_at'
        ]
        
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"\n❌ 缺少字段: {missing_fields}")
            return False, missing_fields
        else:
            print(f"\n✅ 所有必需字段都存在")
            return True, []
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {str(e)}")
        return False, []
    finally:
        if 'conn' in locals():
            conn.close()

def add_missing_fields(missing_fields):
    """添加缺失的字段到devices表"""
    print(f"\n🔧 添加缺失字段到devices表: {missing_fields}")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 定义字段及其类型
        field_definitions = {
            'binding_code': 'TEXT',
            'binding_code_expires_at': 'DATETIME',
            'binding_qr_code_data': 'TEXT',
            'active_webrtc_user_id': 'TEXT',
            'webrtc_session_id': 'TEXT',
            'webrtc_connected_at': 'DATETIME'
        }
        
        # 添加缺失字段
        for field in missing_fields:
            if field in field_definitions:
                sql = f"ALTER TABLE devices ADD COLUMN {field} {field_definitions[field]}"
                try:
                    cursor.execute(sql)
                    print(f"  ✅ 添加字段: {field}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" in str(e):
                        print(f"  ⚠️  字段已存在: {field}")
                    else:
                        raise e
            else:
                print(f"  ❌ 未知字段定义: {field}")
        
        conn.commit()
        print("✅ 字段添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加字段失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def verify_migration():
    """验证迁移结果"""
    print("\n🧪 验证迁移结果")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(devices)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        required_fields = [
            'id', 'device_id', 'device_name', 'device_type', 'status', 'last_heartbeat_at',
            'binding_code', 'binding_code_expires_at', 'binding_qr_code_data',
            'active_webrtc_user_id', 'webrtc_session_id', 'webrtc_connected_at',
            'created_at', 'updated_at'
        ]
        
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"❌ 仍然缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必需字段都存在")
            
            # 测试查询
            try:
                cursor.execute("SELECT device_id, binding_code, webrtc_session_id FROM devices LIMIT 1")
                print("✅ 查询测试成功")
                return True
            except Exception as e:
                print(f"❌ 查询测试失败: {str(e)}")
                return False
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def backup_database():
    """备份数据库"""
    print("💾 备份数据库")
    
    try:
        import shutil
        from datetime import datetime
        
        db_path = get_db_path()
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"❌ 备份失败: {str(e)}")
        return None

def main():
    """主迁移函数"""
    print("🚀 开始devices表迁移")
    print("="*50)
    
    # 1. 备份数据库
    backup_path = backup_database()
    if not backup_path:
        print("❌ 备份失败，停止迁移")
        return
    
    # 2. 检查当前表结构
    is_complete, missing_fields = check_devices_table_structure()
    if is_complete:
        print("✅ devices表结构已经是最新的，无需迁移")
        return
    
    # 3. 添加缺失字段
    if not add_missing_fields(missing_fields):
        print("❌ 添加字段失败，迁移中止")
        return
    
    # 4. 验证迁移结果
    if verify_migration():
        print("\n🎉 devices表迁移成功完成!")
        print("✅ 现在可以正常注册设备了")
    else:
        print("\n❌ 迁移验证失败")
        print(f"可以从备份恢复: {backup_path}")

if __name__ == "__main__":
    main()
