#!/usr/bin/env python3
"""
测试路由是否正确注册
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_route(path, method="GET", data=None):
    """测试单个路由"""
    url = f"{BASE_URL}{path}"
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        else:
            response = requests.request(method, url, json=data)
        
        print(f"{method} {path} -> {response.status_code}")
        
        if response.status_code == 404:
            print(f"  ❌ 路由未找到")
        elif response.status_code in [200, 422]:  # 422是验证错误，说明路由存在
            print(f"  ✅ 路由存在")
        else:
            print(f"  ⚠️  状态码: {response.status_code}")
            
        return response.status_code
        
    except Exception as e:
        print(f"  ❌ 请求失败: {str(e)}")
        return None

def main():
    """测试主要路由"""
    print("🧪 测试API路由注册")
    print("="*50)
    
    # 基础路由
    print("\n📋 基础路由:")
    test_route("/")
    test_route("/health")
    test_route("/ping")
    test_route("/info")
    
    # 认证路由
    print("\n👤 认证路由:")
    test_route("/api/v1/auth/login", "POST", {"username": "test", "password": "test"})
    test_route("/api/v1/auth/register", "POST", {"username": "test", "password": "test"})
    
    # 订单路由
    print("\n🛍️ 订单路由:")
    test_route("/api/v1/orders/", "POST", {"device_id": "test", "total_amount": 10.0})
    test_route("/api/v1/orders/test-order-id")
    
    # 员工路由
    print("\n👨‍🍳 员工路由:")
    test_route("/api/v1/staff/orders/active")
    test_route("/api/v1/staff/orders/pending")
    
    # 支付路由
    print("\n💳 支付路由:")
    test_route("/api/v1/shop/payment/callback/alipay", "POST", {})
    
    # 设备路由
    print("\n📱 设备路由:")
    test_route("/api/v1/device/register", "POST", {"device_id": "test", "device_name": "test"})
    
    # 系统路由
    print("\n🔧 系统路由:")
    test_route("/api/v1/system/performance")
    test_route("/api/v1/system/config")
    test_route("/api/v1/system/routes")
    
    print("\n✅ 路由测试完成")

if __name__ == "__main__":
    main()
