#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试支付链接的可用性
验证设备是否可以直接使用返回的支付链接
"""

import requests
import json
import webbrowser
from urllib.parse import urlparse, parse_qs

# 配置
BASE_URL = "http://localhost:8000"
DEVICE_ID = "test_device_payment_link"

def test_payment_link_accessibility():
    """测试支付链接的可访问性"""
    print("🔗 测试支付链接可用性...")
    
    # 步骤1: 创建订单
    print("\n📝 创建测试订单")
    order_data = {
        "device_id": DEVICE_ID,
        "items": [
            {
                "product_id": "test_product_001",
                "quantity": 1,
                "price": 0.01  # 使用最小金额测试
            }
        ],
        "total_amount": 0.01
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/",
            json=order_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 订单创建失败: {response.status_code}")
            return None
            
        order = response.json()
        order_id = order["id"]
        print(f"✅ 订单创建成功: {order_id}")
        
    except Exception as e:
        print(f"❌ 订单创建异常: {str(e)}")
        return None
    
    # 步骤2: 发起支付并获取支付链接
    print(f"\n💳 发起支付获取支付链接")
    payment_data = {
        "payment_method": "alipay"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/{order_id}/pay",
            json=payment_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 支付发起失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
            
        payment_result = response.json()
        payment_url = payment_result.get('payment_url')
        
        if not payment_url:
            print("❌ 未获取到支付链接")
            return None
            
        print(f"✅ 支付链接获取成功")
        print(f"   订单ID: {order_id}")
        print(f"   支付方式: {payment_result['payment_method']}")
        print(f"   支付链接长度: {len(payment_url)} 字符")
        
        return payment_url, order_id
        
    except Exception as e:
        print(f"❌ 支付发起异常: {str(e)}")
        return None


def analyze_payment_url(payment_url):
    """分析支付链接的结构"""
    print(f"\n🔍 分析支付链接结构")
    
    try:
        # 解析URL
        parsed_url = urlparse(payment_url)
        query_params = parse_qs(parsed_url.query)
        
        print(f"✅ URL解析成功")
        print(f"   协议: {parsed_url.scheme}")
        print(f"   域名: {parsed_url.netloc}")
        print(f"   路径: {parsed_url.path}")
        print(f"   参数数量: {len(query_params)}")
        
        # 检查关键参数
        key_params = ['app_id', 'method', 'sign', 'biz_content']
        print(f"\n📋 关键参数检查:")
        for param in key_params:
            if param in query_params:
                value = query_params[param][0]
                print(f"   ✅ {param}: {value[:50]}{'...' if len(value) > 50 else ''}")
            else:
                print(f"   ❌ {param}: 缺失")
        
        # 检查biz_content内容
        if 'biz_content' in query_params:
            try:
                import urllib.parse
                biz_content = urllib.parse.unquote(query_params['biz_content'][0])
                biz_data = json.loads(biz_content)
                print(f"\n📦 业务内容解析:")
                print(f"   订单号: {biz_data.get('out_trade_no', 'N/A')}")
                print(f"   金额: {biz_data.get('total_amount', 'N/A')}")
                print(f"   标题: {biz_data.get('subject', 'N/A')}")
                print(f"   产品代码: {biz_data.get('product_code', 'N/A')}")
                print(f"   二维码模式: {biz_data.get('qr_pay_mode', 'N/A')}")
            except Exception as e:
                print(f"   ❌ 业务内容解析失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL解析失败: {str(e)}")
        return False


def test_payment_url_accessibility(payment_url):
    """测试支付链接的可访问性"""
    print(f"\n🌐 测试支付链接可访问性")
    
    try:
        # 发送HEAD请求检查链接是否可访问
        response = requests.head(payment_url, timeout=10, allow_redirects=True)
        
        print(f"✅ 支付链接可访问")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头数量: {len(response.headers)}")
        
        # 检查重要的响应头
        important_headers = ['content-type', 'server', 'cache-control']
        for header in important_headers:
            if header in response.headers:
                print(f"   {header}: {response.headers[header]}")
        
        return True
        
    except requests.exceptions.Timeout:
        print(f"❌ 支付链接访问超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 支付链接访问失败: {str(e)}")
        return False


def create_device_test_page(payment_url, order_id):
    """创建设备测试页面"""
    print(f"\n📱 创建设备测试页面")
    
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备支付测试 - 订单 {order_id}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }}
        .order-info {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
        .pay-button {{
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #1677ff;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
        }}
        .pay-button:hover {{
            background-color: #0958d9;
        }}
        .alipay-button {{
            background-color: #00a0e9;
        }}
        .alipay-button:hover {{
            background-color: #0087c1;
        }}
        .info {{
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }}
        .url-display {{
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 机器人设备支付测试</h1>
            <p>模拟设备端支付流程</p>
        </div>
        
        <div class="order-info">
            <h3>📋 订单信息</h3>
            <p><strong>订单号:</strong> {order_id}</p>
            <p><strong>设备ID:</strong> {DEVICE_ID}</p>
            <p><strong>支付金额:</strong> ¥0.01</p>
            <p><strong>支付方式:</strong> 支付宝</p>
        </div>
        
        <a href="{payment_url}" class="pay-button alipay-button" target="_blank">
            💳 点击进行支付宝支付
        </a>
        
        <div class="info">
            <h4>📱 设备使用说明:</h4>
            <ol>
                <li>设备调用API获取支付链接</li>
                <li>设备渲染此页面或直接打开支付链接</li>
                <li>用户使用支付宝扫码或登录支付</li>
                <li>支付完成后，设备轮询查询支付状态</li>
            </ol>
            
            <h4>🔗 支付链接信息:</h4>
            <p><strong>链接长度:</strong> {len(payment_url)} 字符</p>
            <p><strong>域名:</strong> {urlparse(payment_url).netloc}</p>
            
            <details>
                <summary>查看完整支付链接</summary>
                <div class="url-display">{payment_url}</div>
            </details>
        </div>
    </div>
    
    <script>
        // 模拟设备端JavaScript
        console.log('设备支付页面加载完成');
        console.log('订单ID:', '{order_id}');
        console.log('支付链接长度:', {len(payment_url)});
        
        // 可以添加设备端特定的逻辑
        function checkPaymentStatus() {{
            fetch('/api/v1/orders/{order_id}/payment-status')
                .then(response => response.json())
                .then(data => {{
                    console.log('支付状态:', data.status);
                    if (data.status === 'paid') {{
                        alert('支付成功！');
                    }}
                }})
                .catch(error => console.error('查询支付状态失败:', error));
        }}
        
        // 每5秒查询一次支付状态（仅用于演示）
        setInterval(checkPaymentStatus, 5000);
    </script>
</body>
</html>
"""
    
    try:
        with open('device_payment_test.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 设备测试页面创建成功: device_payment_test.html")
        print(f"   可以在浏览器中打开此文件测试支付流程")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试页面失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🔧 支付链接可用性测试工具")
    print("=" * 50)
    
    # 检查服务器状态
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ 服务器状态异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return
    
    print("✅ 服务器运行正常")
    
    # 测试支付链接
    result = test_payment_link_accessibility()
    if not result:
        return
    
    payment_url, order_id = result
    
    # 分析支付链接
    if analyze_payment_url(payment_url):
        print("✅ 支付链接结构正确")
    
    # 测试链接可访问性
    if test_payment_url_accessibility(payment_url):
        print("✅ 支付链接可正常访问")
    
    # 创建设备测试页面
    if create_device_test_page(payment_url, order_id):
        print("✅ 设备测试页面创建成功")
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("1. ✅ 支付链接成功生成")
    print("2. ✅ 支付链接格式正确")
    print("3. ✅ 支付链接可以访问")
    print("4. ✅ 设备可以直接使用此链接")
    print("5. 📱 已创建设备测试页面供验证")
    
    print(f"\n🎯 设备使用流程:")
    print(f"   1. 设备调用: POST /api/v1/orders/{{order_id}}/pay")
    print(f"   2. 获取 payment_url 字段")
    print(f"   3. 设备渲染网页或直接打开链接")
    print(f"   4. 用户使用支付宝完成支付")
    print(f"   5. 设备轮询: GET /api/v1/orders/{{order_id}}/payment-status")


if __name__ == "__main__":
    main()
