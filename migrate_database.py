#!/usr/bin/env python3
"""
数据库迁移脚本
添加视频录制相关字段到orders表
"""

import sqlite3
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def get_db_path():
    """获取数据库路径"""
    db_url = settings.database_url
    if db_url.startswith("sqlite:///"):
        return db_url.replace("sqlite:///", "")
    else:
        # 如果是其他数据库类型，需要不同的处理方式
        raise ValueError(f"不支持的数据库类型: {db_url}")

def check_table_structure():
    """检查当前表结构"""
    print("🔍 检查当前orders表结构")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        print("当前orders表字段:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
        # 检查是否存在视频相关字段
        column_names = [col[1] for col in columns]
        video_fields = ['video_stream_id', 'video_start_time', 'video_end_time']
        
        missing_fields = [field for field in video_fields if field not in column_names]
        
        if missing_fields:
            print(f"\n❌ 缺少字段: {missing_fields}")
            return False
        else:
            print(f"\n✅ 所有视频字段都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def add_video_fields():
    """添加视频录制相关字段"""
    print("\n🔧 添加视频录制字段到orders表")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 添加视频相关字段
        video_fields = [
            "ALTER TABLE orders ADD COLUMN video_stream_id TEXT",
            "ALTER TABLE orders ADD COLUMN video_start_time DATETIME",
            "ALTER TABLE orders ADD COLUMN video_end_time DATETIME"
        ]
        
        for sql in video_fields:
            try:
                cursor.execute(sql)
                field_name = sql.split()[-2]  # 提取字段名
                print(f"  ✅ 添加字段: {field_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    field_name = sql.split()[-2]
                    print(f"  ⚠️  字段已存在: {field_name}")
                else:
                    raise e
        
        conn.commit()
        print("✅ 视频字段添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加字段失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def verify_migration():
    """验证迁移结果"""
    print("\n🧪 验证迁移结果")
    
    try:
        db_path = get_db_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        required_fields = [
            'id', 'device_id', 'total_amount', 'status', 'payment_transaction_id',
            'video_stream_id', 'video_start_time', 'video_end_time',
            'created_at', 'updated_at'
        ]
        
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"❌ 仍然缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必需字段都存在")
            
            # 测试插入一条记录
            test_sql = """
            INSERT INTO orders (
                id, device_id, total_amount, status, payment_transaction_id,
                video_stream_id, video_start_time, video_end_time,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            from datetime import datetime
            test_data = (
                'test-migration-id',
                'test-device',
                10.0,
                'pending_payment',
                None,
                None,
                None,
                None,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            )
            
            cursor.execute(test_sql, test_data)
            
            # 删除测试记录
            cursor.execute("DELETE FROM orders WHERE id = ?", ('test-migration-id',))
            
            conn.commit()
            print("✅ 插入测试成功")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def backup_database():
    """备份数据库"""
    print("💾 备份数据库")
    
    try:
        import shutil
        from datetime import datetime
        
        db_path = get_db_path()
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"❌ 备份失败: {str(e)}")
        return None

def main():
    """主迁移函数"""
    print("🚀 开始数据库迁移")
    print("="*50)
    
    # 1. 备份数据库
    backup_path = backup_database()
    if not backup_path:
        print("❌ 备份失败，停止迁移")
        return
    
    # 2. 检查当前表结构
    if check_table_structure():
        print("✅ 表结构已经是最新的，无需迁移")
        return
    
    # 3. 添加视频字段
    if not add_video_fields():
        print("❌ 添加字段失败，迁移中止")
        return
    
    # 4. 验证迁移结果
    if verify_migration():
        print("\n🎉 数据库迁移成功完成!")
        print("✅ 现在可以正常创建订单了")
    else:
        print("\n❌ 迁移验证失败")
        print(f"可以从备份恢复: {backup_path}")

if __name__ == "__main__":
    main()
