from pydantic import BaseModel
from typing import List, Dict, Any, Optional


class Ingredient(BaseModel):
    name: str
    quantity: float
    unit: str


class StructuredData(BaseModel):
    recipe_name: Optional[str] = None
    ingredients: List[Ingredient] = []


class LLMIntentRequest(BaseModel):
    device_id: str
    intent_type: str
    raw_query: str
    structured_data: StructuredData


class ShoppingListItem(BaseModel):
    product_id: str
    product_name: str
    matched_ingredient: str
    price_per_unit: float
    quantity: int
    total_price: float
    unit: str
    image_url: Optional[str] = None


class LLMIntentResponse(BaseModel):
    message: str
    action_required: str
    shopping_list: List[ShoppingListItem] = []
    total_amount: float = 0.0
    payment_qr_code_url: Optional[str] = None
