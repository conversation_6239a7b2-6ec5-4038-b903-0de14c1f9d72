from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class DeviceRegister(BaseModel):
    device_id: str
    device_name: str
    device_type: str


class DeviceHeartbeat(BaseModel):
    status: str
    battery_level: Optional[float] = None
    firmware_version: Optional[str] = None


class DeviceResponse(BaseModel):
    device_id: str
    device_name: str
    status: str
    last_heartbeat_at: Optional[datetime] = None


class DeviceBindAccess(BaseModel):
    qr_code_data: str


class DeviceRegisterResponse(BaseModel):
    message: str
    device_token: str
    device_id: str
    binding_qr_code_data: str


class DeviceHeartbeatResponse(BaseModel):
    message: str
    timestamp: datetime


class DeviceBindResponse(BaseModel):
    message: str
    device_id: str
    user_id: str
