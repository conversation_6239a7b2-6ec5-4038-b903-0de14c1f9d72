from pydantic import BaseModel, Field
from typing import Optional, Union
from datetime import datetime


class UserCreate(BaseModel):
    username: str
    password: str
    role: str = "webrtc_app_user"


class UserLogin(BaseModel):
    username: str
    password: str


class UserResponse(BaseModel):
    id: Union[int, str] = Field(..., description="用户ID，支持整数或字符串")
    username: str
    role: str
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True  # 允许从ORM对象创建


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: Union[int, str] = Field(..., description="用户ID，支持整数或字符串")
    username: Optional[str] = None
    role: Optional[str] = None
