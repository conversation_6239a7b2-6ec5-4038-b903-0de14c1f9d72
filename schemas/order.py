from pydantic import BaseModel
from typing import List, Optional, Literal
from datetime import datetime
from models.order import OrderStatus


class OrderItemResponse(BaseModel):
    product_id: str
    product_name: str
    quantity: int
    price_at_purchase: float
    total_price: float


class OrderCreate(BaseModel):
    device_id: str
    items: List[dict]
    total_amount: float


class OrderResponse(BaseModel):
    id: str
    device_id: str
    total_amount: float
    status: str
    created_at: datetime
    items: List[OrderItemResponse] = []


class OrderStatusUpdate(BaseModel):
    status: str


class KitchenStatusUpdate(BaseModel):
    """备料状态更新请求 - 符合PRD要求"""
    status: Literal[OrderStatus.PROCESSING, OrderStatus.READY_FOR_PICKUP]
    video_stream_id: Optional[str] = None


class PaymentConfirmRequest(BaseModel):
    """支付确认请求 - 新增接口"""
    payment_transaction_id: str
    payment_method: str
    verification_data: dict = {}


class PaymentInitRequest(BaseModel):
    """支付发起请求"""
    payment_method: str  # "alipay" 或 "wechat"


class PaymentResponse(BaseModel):
    """支付响应"""
    success: bool
    payment_url: Optional[str] = None  # 支付链接
    qr_code: Optional[str] = None  # 二维码内容
    order_id: str
    payment_method: str
    message: Optional[str] = None
    error_code: Optional[str] = None
