<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI订单履约系统 - 手机APP下单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px 20px;
        }

        .section {
            margin-bottom: 30px;
            display: none;
        }

        .section.active {
            display: block;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }

        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 2px solid #2196f3;
        }

        .order-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
        }

        .order-item h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .order-item p {
            color: #666;
            font-size: 14px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e1e1;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 10px 5px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            background: #f0f0f0;
            color: #999;
            margin: 0 2px;
        }

        .step.active {
            background: #4CAF50;
            color: white;
        }

        .step.completed {
            background: #2e7d32;
            color: white;
        }

        .qr-container {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 20px 0;
        }

        .qr-code {
            max-width: 200px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .nav-buttons .btn {
            flex: 1;
            margin-bottom: 0;
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 AI订单履约系统</h1>
            <p>手机APP下单流程测试</p>
        </div>

        <div class="content">
            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active" id="step-login">登录</div>
                <div class="step" id="step-order">下单</div>
                <div class="step" id="step-payment">支付</div>
                <div class="step" id="step-tracking">追踪</div>
            </div>

            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 25%"></div>
            </div>

            <!-- 第1步：用户登录 -->
            <div class="section active" id="login-section">
                <h2>👤 用户登录</h2>
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" value="testuser" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" value="testpass" placeholder="请输入密码">
                </div>
                <button class="btn" onclick="login()">登录</button>
                <button class="btn btn-secondary" onclick="register()">注册新用户</button>
                <div id="login-status"></div>
            </div>

            <!-- 第2步：创建订单 -->
            <div class="section" id="order-section">
                <h2>🛍️ 创建订单</h2>
                <div class="form-group">
                    <label for="device-type">下单设备类型</label>
                    <select id="device-type" onchange="updateDeviceId()">
                        <option value="mobile">手机APP</option>
                        <option value="orangepi">OrangePi设备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="device-id">设备ID</label>
                    <input type="text" id="device-id" value="mobile-app-001" placeholder="设备标识">
                </div>
                <div class="form-group">
                    <label for="total-amount">订单金额 (元)</label>
                    <input type="number" id="total-amount" value="35.00" step="0.01" placeholder="订单总金额">
                </div>
                <button class="btn" onclick="createOrder()">创建订单</button>
                <div class="nav-buttons">
                    <button class="btn btn-secondary" onclick="goToStep('login')">返回登录</button>
                </div>
                <div id="order-status"></div>
            </div>

            <!-- 第3步：支付确认 -->
            <div class="section" id="payment-section">
                <h2>💳 支付确认</h2>
                <div id="order-info"></div>
                <div class="form-group">
                    <label for="payment-method">支付方式</label>
                    <select id="payment-method">
                        <option value="alipay">支付宝</option>
                        <option value="wechat">微信支付</option>
                    </select>
                </div>
                <button class="btn" onclick="initiatePayment()">发起支付</button>
                <button class="btn btn-secondary" onclick="confirmPayment()">确认支付完成</button>
                <div class="nav-buttons">
                    <button class="btn btn-secondary" onclick="goToStep('order')">返回下单</button>
                </div>
                <div id="payment-status"></div>
                <div id="qr-container" class="qr-container" style="display: none;">
                    <p>请使用手机扫描二维码完成支付</p>
                    <img id="qr-code" class="qr-code" alt="支付二维码">
                </div>
            </div>

            <!-- 第4步：订单追踪 -->
            <div class="section" id="tracking-section">
                <h2>📦 订单追踪</h2>
                <div id="tracking-info"></div>
                <button class="btn" onclick="refreshOrderStatus()">刷新状态</button>
                <button class="btn btn-secondary" onclick="simulateStaffActions()">模拟员工操作</button>
                <div class="nav-buttons">
                    <button class="btn btn-secondary" onclick="goToStep('order')">创建新订单</button>
                    <button class="btn btn-danger" onclick="resetFlow()">重置流程</button>
                </div>
                <div id="tracking-status"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 'login';
        let authToken = '';
        let currentOrderId = '';
        let currentUserId = '';
        
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 步骤管理
        function goToStep(step) {
            // 隐藏所有section
            document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            
            // 显示目标section
            document.getElementById(`${step}-section`).classList.add('active');
            document.getElementById(`step-${step}`).classList.add('active');
            
            currentStep = step;
            updateProgress();
        }
        
        function updateProgress() {
            const steps = ['login', 'order', 'payment', 'tracking'];
            const currentIndex = steps.indexOf(currentStep);
            const progress = ((currentIndex + 1) / steps.length) * 100;
            
            document.getElementById('progress-fill').style.width = `${progress}%`;
            
            // 更新步骤状态
            steps.forEach((step, index) => {
                const stepEl = document.getElementById(`step-${step}`);
                if (index < currentIndex) {
                    stepEl.classList.add('completed');
                    stepEl.classList.remove('active');
                } else if (index === currentIndex) {
                    stepEl.classList.add('active');
                    stepEl.classList.remove('completed');
                } else {
                    stepEl.classList.remove('active', 'completed');
                }
            });
        }
        
        function updateDeviceId() {
            const deviceType = document.getElementById('device-type').value;
            const deviceIdInput = document.getElementById('device-id');
            
            if (deviceType === 'mobile') {
                deviceIdInput.value = `mobile-app-${Date.now().toString().slice(-6)}`;
            } else {
                deviceIdInput.value = `orangepi-${Date.now().toString().slice(-6)}`;
            }
        }
        
        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId);
            statusEl.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // API调用函数
        async function apiCall(endpoint, method = 'GET', data = null, useAuth = false) {
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (useAuth && authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            
            const config = {
                method,
                headers
            };
            
            if (data) {
                config.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.detail || `HTTP ${response.status}`);
                }
                
                return result;
            } catch (error) {
                console.error('API调用失败:', error);
                throw error;
            }
        }
        
        // 第1步：用户登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showStatus('login-status', '请输入用户名和密码', 'error');
                return;
            }
            
            try {
                showStatus('login-status', '正在登录...', 'info');
                
                const result = await apiCall('/auth/login', 'POST', {
                    username,
                    password
                });
                
                authToken = result.access_token;
                currentUserId = result.user_id;
                
                showStatus('login-status', `登录成功！欢迎 ${result.username}`, 'success');
                
                setTimeout(() => {
                    goToStep('order');
                }, 1500);
                
            } catch (error) {
                showStatus('login-status', `登录失败: ${error.message}`, 'error');
            }
        }
        
        async function register() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showStatus('login-status', '请输入用户名和密码', 'error');
                return;
            }
            
            try {
                showStatus('login-status', '正在注册...', 'info');
                
                const result = await apiCall('/auth/register', 'POST', {
                    username,
                    password,
                    role: 'user'
                });
                
                showStatus('login-status', `注册成功！用户 ${result.username} 已创建，请登录`, 'success');
                
            } catch (error) {
                showStatus('login-status', `注册失败: ${error.message}`, 'error');
            }
        }
        
        // 第2步：创建订单
        async function createOrder() {
            const deviceId = document.getElementById('device-id').value;
            const totalAmount = parseFloat(document.getElementById('total-amount').value);
            
            if (!deviceId || !totalAmount) {
                showStatus('order-status', '请填写完整的订单信息', 'error');
                return;
            }
            
            try {
                showStatus('order-status', '正在创建订单...', 'info');
                
                // 模拟订单项
                const orderData = {
                    device_id: deviceId,
                    total_amount: totalAmount,
                    items: [
                        {
                            product_id: "prod-001",
                            quantity: 2,
                            price: totalAmount * 0.6
                        },
                        {
                            product_id: "prod-002", 
                            quantity: 1,
                            price: totalAmount * 0.4
                        }
                    ]
                };
                
                const result = await apiCall('/orders/', 'POST', orderData);
                
                currentOrderId = result.id;
                
                showStatus('order-status', `订单创建成功！订单号: ${result.id}`, 'success');
                
                setTimeout(() => {
                    goToStep('payment');
                    displayOrderInfo(result);
                }, 1500);
                
            } catch (error) {
                showStatus('order-status', `创建订单失败: ${error.message}`, 'error');
            }
        }
        
        function displayOrderInfo(order) {
            const orderInfoEl = document.getElementById('order-info');
            orderInfoEl.innerHTML = `
                <div class="order-item">
                    <h4>订单信息</h4>
                    <p><strong>订单号:</strong> ${order.id}</p>
                    <p><strong>设备ID:</strong> ${order.device_id}</p>
                    <p><strong>金额:</strong> ¥${order.total_amount}</p>
                    <p><strong>状态:</strong> ${getStatusDisplay(order.status)}</p>
                    <p><strong>创建时间:</strong> ${new Date(order.created_at).toLocaleString()}</p>
                </div>
            `;
        }
        
        // 第3步：支付处理
        async function initiatePayment() {
            if (!currentOrderId) {
                showStatus('payment-status', '请先创建订单', 'error');
                return;
            }
            
            const paymentMethod = document.getElementById('payment-method').value;
            
            try {
                showStatus('payment-status', '正在发起支付...', 'info');
                
                const result = await apiCall(`/orders/${currentOrderId}/pay`, 'POST', {
                    payment_method: paymentMethod
                });
                
                showStatus('payment-status', '支付发起成功！请完成支付', 'success');
                
                // 显示二维码（模拟）
                if (result.qr_code) {
                    document.getElementById('qr-code').src = result.qr_code;
                    document.getElementById('qr-container').style.display = 'block';
                } else {
                    // 模拟二维码
                    document.getElementById('qr-code').src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=pay_${currentOrderId}`;
                    document.getElementById('qr-container').style.display = 'block';
                }
                
            } catch (error) {
                showStatus('payment-status', `支付发起失败: ${error.message}`, 'error');
            }
        }
        
        async function confirmPayment() {
            if (!currentOrderId) {
                showStatus('payment-status', '请先创建订单', 'error');
                return;
            }
            
            try {
                showStatus('payment-status', '正在确认支付...', 'info');
                
                const result = await apiCall(`/orders/${currentOrderId}/confirm_payment`, 'POST', {
                    payment_transaction_id: `txn_${Date.now()}`,
                    payment_method: document.getElementById('payment-method').value,
                    verification_data: {
                        mock: true,
                        timestamp: new Date().toISOString()
                    }
                });
                
                showStatus('payment-status', '支付确认成功！订单已进入备料队列', 'success');
                
                setTimeout(() => {
                    goToStep('tracking');
                    startOrderTracking();
                }, 2000);
                
            } catch (error) {
                showStatus('payment-status', `支付确认失败: ${error.message}`, 'error');
            }
        }
        
        // 第4步：订单追踪
        async function startOrderTracking() {
            await refreshOrderStatus();
        }
        
        async function refreshOrderStatus() {
            if (!currentOrderId) {
                showStatus('tracking-status', '没有可追踪的订单', 'error');
                return;
            }
            
            try {
                const result = await apiCall(`/orders/${currentOrderId}`);
                
                const trackingInfoEl = document.getElementById('tracking-info');
                trackingInfoEl.innerHTML = `
                    <div class="order-item">
                        <h4>📦 订单追踪</h4>
                        <p><strong>订单号:</strong> ${result.id}</p>
                        <p><strong>当前状态:</strong> ${getStatusDisplay(result.status)}</p>
                        <p><strong>最后更新:</strong> ${new Date().toLocaleString()}</p>
                        ${result.video_stream_id ? `<p><strong>视频记录:</strong> ${result.video_stream_id}</p>` : ''}
                    </div>
                `;
                
                showStatus('tracking-status', `订单状态已更新: ${getStatusDisplay(result.status)}`, 'success');
                
            } catch (error) {
                showStatus('tracking-status', `获取订单状态失败: ${error.message}`, 'error');
            }
        }
        
        async function simulateStaffActions() {
            if (!currentOrderId) {
                showStatus('tracking-status', '没有可操作的订单', 'error');
                return;
            }
            
            try {
                showStatus('tracking-status', '模拟员工开始备料...', 'info');
                
                // 模拟员工开始备料
                await apiCall(`/staff/orders/${currentOrderId}/kitchen_status`, 'PUT', {
                    status: 'processing',
                    video_stream_id: `video_${Date.now()}`
                });
                
                showStatus('tracking-status', '员工已开始备料，正在录制视频...', 'info');
                
                // 等待3秒后模拟备料完成
                setTimeout(async () => {
                    try {
                        await apiCall(`/staff/orders/${currentOrderId}/kitchen_status`, 'PUT', {
                            status: 'ready_for_pickup'
                        });
                        
                        showStatus('tracking-status', '备料完成！等待下游领取', 'success');
                        await refreshOrderStatus();
                        
                    } catch (error) {
                        showStatus('tracking-status', `备料完成操作失败: ${error.message}`, 'error');
                    }
                }, 3000);
                
            } catch (error) {
                showStatus('tracking-status', `模拟员工操作失败: ${error.message}`, 'error');
            }
        }
        
        function getStatusDisplay(status) {
            const statusMap = {
                'pending_payment': '⏳ 待支付',
                'paid': '💰 已支付',
                'processing': '👨‍🍳 备料中',
                'ready_for_pickup': '✅ 备料完成',
                'completed': '🎉 已完成',
                'cancelled': '❌ 已取消'
            };
            return statusMap[status] || status;
        }
        
        function resetFlow() {
            currentStep = 'login';
            authToken = '';
            currentOrderId = '';
            currentUserId = '';
            
            // 清空所有状态显示
            ['login-status', 'order-status', 'payment-status', 'tracking-status'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            // 隐藏二维码
            document.getElementById('qr-container').style.display = 'none';
            
            // 重置表单
            document.getElementById('username').value = 'testuser';
            document.getElementById('password').value = 'testpass';
            document.getElementById('total-amount').value = '35.00';
            updateDeviceId();
            
            goToStep('login');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceId();
            updateProgress();
        });
    </script>
</body>
</html>
