<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能家居 App - 视频通话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px 20px 10px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .card h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .user-info h4 {
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .user-info p {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            opacity: 0.9;
        }

        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }

        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 8px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            color: white;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            margin: 15px 0;
        }

        .status-indicator.connected {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }

        .status-indicator.disconnected {
            background: linear-gradient(45deg, #e17055, #d63031);
        }

        .status-indicator.calling {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            animation: pulse 2s infinite;
        }

        .status-indicator.incall {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
            100% {
                transform: scale(1);
            }
        }

        .video-call-container {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.5);
            margin: 20px 0;
        }

        .main-video {
            width: 100%;
            height: 250px;
            object-fit: contain; /* 改为 contain 避免裁剪 */
            background: #000000; /* 设置黑色背景 */
            border-radius: 15px;
            /* 强制硬件加速和渲染优化 */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* 禁用可能导致绿屏的滤镜 */
            filter: none;
            -webkit-filter: none;
            /* 确保正确的颜色空间 */
            image-rendering: auto;
            -webkit-image-rendering: auto;
        }

        .pip-video {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 80px;
            height: 120px;
            border-radius: 10px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.5);
            background: rgba(0, 0, 0, 0.7);
        }

        .video-controls {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn.video {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .control-btn.audio {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .control-btn.hangup {
            background: #e17055;
            color: white;
        }

        .device-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .device-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .device-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .device-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00b894, #00cec9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .device-details h5 {
            margin-bottom: 3px;
            font-size: 1em;
        }

        .device-details p {
            font-size: 0.8em;
            opacity: 0.8;
        }

        .call-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            font-size: 12px;
            cursor: pointer;
        }

        .log-container {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 10px;
            font-family: monospace;
            font-size: 11px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #00b894;
        }

        .log-entry.error {
            color: #e17055;
        }

        .log-entry.warning {
            color: #fdcb6e;
        }

        .log-entry.info {
            color: #74b9ff;
        }

        .bottom-nav {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-around;
            backdrop-filter: blur(10px);
        }

        .nav-item {
            text-align: center;
            cursor: pointer;
            padding: 5px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-item .icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-item .label {
            font-size: 10px;
            opacity: 0.8;
        }

        /* 设备管理样式 */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header h3 {
            margin: 0;
            color: white;
            font-size: 18px;
        }

        .device-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 15px;
        }

        .device-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .device-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .device-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .device-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .device-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .device-details h4 {
            margin: 0 0 4px 0;
            color: white;
            font-size: 14px;
        }

        .device-details p {
            margin: 0;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .device-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e17055;
        }

        .status-dot.online {
            background: #00b894;
            box-shadow: 0 0 8px rgba(0, 184, 148, 0.5);
        }

        .device-actions-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            padding: 6px 10px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-actions-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .device-stats {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        .stat-item span {
            color: white;
            font-weight: bold;
        }

        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 通话模式选择样式 */
        .call-mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 4px;
        }

        .mode-btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .mode-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        /* 语音通话界面样式 */
        .audio-call-interface {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 20px 0;
        }

        .audio-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin: 0 auto 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .audio-info h3 {
            color: white;
            margin: 0 0 5px 0;
            font-size: 20px;
        }

        .audio-info p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 20px 0;
            font-size: 14px;
        }

        .audio-visualizer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3px;
            height: 40px;
            margin: 20px 0;
        }

        .audio-bar {
            width: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 2px;
            animation: audioWave 1.5s ease-in-out infinite;
        }

        .audio-bar:nth-child(1) {
            height: 10px;
            animation-delay: 0s;
        }

        .audio-bar:nth-child(2) {
            height: 20px;
            animation-delay: 0.1s;
        }

        .audio-bar:nth-child(3) {
            height: 30px;
            animation-delay: 0.2s;
        }

        .audio-bar:nth-child(4) {
            height: 25px;
            animation-delay: 0.3s;
        }

        .audio-bar:nth-child(5) {
            height: 15px;
            animation-delay: 0.4s;
        }

        .audio-bar:nth-child(6) {
            height: 35px;
            animation-delay: 0.5s;
        }

        .audio-bar:nth-child(7) {
            height: 20px;
            animation-delay: 0.6s;
        }

        @keyframes audioWave {
            0%, 100% {
                transform: scaleY(1);
                opacity: 0.6;
            }
            50% {
                transform: scaleY(1.5);
                opacity: 1;
            }
        }

        .call-duration {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }

        .network-quality {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin: 10px 0;
        }

        .quality-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00b894;
        }

        .quality-indicator.poor {
            background: #e17055;
        }

        .quality-indicator.fair {
            background: #fdcb6e;
        }

        .quality-indicator.good {
            background: #00b894;
        }
    </style>
</head>
<body>
<div class="app-container">
    <div class="header">
        <h1>📱 智能家居</h1>
        <p class="subtitle">视频通话 & 设备监控</p>
    </div>

    <div class="main-content">
        <!-- 用户信息卡片 -->
        <div class="card">
            <div class="user-profile">
                <div class="avatar" id="userAvatar">👤</div>
                <div class="user-info">
                    <h4 id="userName">访客用户</h4>
                    <p id="userStatus">未登录</p>
                </div>
            </div>

            <div class="input-group">
                <label>用户名</label>
                <input type="text" id="username" value="testuser" placeholder="输入用户名">
            </div>

            <div class="input-group">
                <label>密码</label>
                <input type="password" id="password" value="123456" placeholder="输入密码">
            </div>

            <button class="btn btn-primary" onclick="userLogin()" id="loginBtn">
                🔐 登录账户
            </button>
        </div>

        <!-- 连接状态 -->
        <div class="status-indicator disconnected" id="connectionStatus">
            🔴 未连接服务器
        </div>

        <!-- 设备管理卡片 -->
        <div class="card" id="deviceManagementCard" style="display: none;">
            <div class="card-header">
                <h3>📱 我的设备</h3>
                <div class="device-actions">
                    <button class="btn btn-small" onclick="refreshDeviceList()">🔄 刷新</button>
                    <button class="btn btn-small" onclick="showAddDeviceDialog()">➕ 添加设备</button>
                </div>
            </div>

            <div class="device-list" id="deviceList">
                <div class="loading-indicator" id="deviceLoading">
                    <div class="spinner"></div>
                    <p>加载设备列表...</p>
                </div>
            </div>

            <div class="device-stats" id="deviceStats">
                <span class="stat-item">总设备: <span id="totalDevices">0</span></span>
                <span class="stat-item">在线: <span id="onlineDevices">0</span></span>
            </div>
        </div>

        <!-- 视频通话区域 -->
        <div class="card">
            <h3>📹 视频通话</h3>
            <div class="video-call-container">
                <video id="remoteVideo" class="main-video" autoplay playsinline muted></video>
                <video id="localVideo" class="pip-video" autoplay muted playsinline></video>

                <div class="video-controls">
                    <button class="control-btn video" onclick="toggleVideo()" id="videoBtn">
                        📹
                    </button>
                    <button class="control-btn audio" onclick="toggleAudio()" id="audioBtn">
                        🎤
                    </button>
                    <button class="control-btn hangup" onclick="hangupCall()" id="hangupBtn" disabled>
                        📞
                    </button>
                </div>
            </div>

            <!-- 通话模式选择 -->
            <div class="call-mode-selector">
                <button class="mode-btn active" onclick="setCallMode('video')" id="videoModeBtn">
                    📹 视频通话
                </button>
                <button class="mode-btn" onclick="setCallMode('audio')" id="audioModeBtn">
                    🎤 语音通话
                </button>
            </div>

            <button class="btn btn-success" onclick="startCamera()" id="startCameraBtn" disabled>
                📹 启动摄像头
            </button>

            <!-- 诊断工具按钮 -->
            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                <button class="btn btn-warning" onclick="diagnoseVideoIssues()" id="diagnoseBtn" style="flex: 1;">
                    🔍 诊断问题
                </button>
                <button class="btn btn-danger" onclick="fixGreenScreen()" id="fixGreenBtn" style="flex: 1;">
                    🟢 修复绿屏
                </button>
            </div>
        </div>

        <!-- 通话日志 -->
        <div class="card">
            <h3>📋 通话日志</h3>
            <div class="log-container" id="callLog">
                <div class="log-entry info">[系统] 智能家居App已启动</div>
                <div class="log-entry info">[系统] 等待用户登录...</div>
            </div>
            <button class="btn btn-warning" onclick="clearLog()"
                    style="margin-top: 10px; font-size: 14px; padding: 8px;">
                🗑️ 清空日志
            </button>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item active">
            <div class="icon">🏠</div>
            <div class="label">首页</div>
        </div>
        <div class="nav-item">
            <div class="icon">📹</div>
            <div class="label">通话</div>
        </div>
        <div class="nav-item">
            <div class="icon">⚙️</div>
            <div class="label">设置</div>
        </div>
        <div class="nav-item">
            <div class="icon">👤</div>
            <div class="label">我的</div>
        </div>
    </div>
</div>

<script>
    // 服务器配置 - 根据实际部署环境修改
    const SERVER_CONFIG = {
        // HTTP API 基础URL
        API_BASE_URL: 'http://*************:8000',
        // WebSocket 基础URL
        WS_BASE_URL: 'ws://*************:8000'
    };

    // WebRTC配置
    const ICE_SERVERS = [
        {urls: 'stun:stun.l.google.com:19302'},
        {urls: 'stun:stun1.l.google.com:19302'},
        {urls: 'stun:*************:3478'},
        {
            urls: 'turn:*************:3478',
            username: 'username1',
            credential: 'password1'
        }
    ];

    // 全局变量
    let userSocket = null;
    let userConnected = false;
    let userPeerConnection = null;
    let userLocalStream = null;
    let userToken = null;
    let userDevices = [];
    let deviceStatusInterval = null;
    let currentCallMode = 'video'; // 'video' 或 'audio'
    let callStartTime = null;
    let callDurationInterval = null;

    // 通话状态
    let callState = 'idle'; // idle, calling, incall
    let videoEnabled = true;
    let audioEnabled = true;
    let currentTargetDevice = 'smart_camera_001';

    function addLog(message, type = 'info') {
        const log = document.getElementById('callLog');
        const timestamp = new Date().toLocaleTimeString();
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.textContent = `[${timestamp}] ${message}`;
        log.appendChild(entry);
        log.scrollTop = log.scrollHeight;
    }

    function updateConnectionStatus(message, status) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.textContent = message;
        statusElement.className = `status-indicator ${status}`;
    }

    function updateUserInfo(username, status) {
        document.getElementById('userName').textContent = username || '访客用户';
        document.getElementById('userStatus').textContent = status || '未登录';

        if (username) {
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
        }
    }

    function updateButtons() {
        const loggedIn = userToken !== null;
        const connected = userConnected;
        const hasMedia = userLocalStream !== null;
        const inCall = callState === 'incall';
        const calling = callState === 'calling';

        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) loginBtn.disabled = loggedIn;

        const startCameraBtn = document.getElementById('startCameraBtn');
        if (startCameraBtn) startCameraBtn.disabled = !connected || hasMedia;

        const hangupBtn = document.getElementById('hangupBtn');
        if (hangupBtn) hangupBtn.disabled = !calling && !inCall;

        updateDeviceCallButtons(!connected || !hasMedia || calling || inCall);
    }

    function updateDeviceCallButtons(disabled) {
        const deviceCallButtons = document.querySelectorAll('.device-actions-btn');
        deviceCallButtons.forEach(button => {
            if (button.textContent.includes('呼叫')) {
                button.disabled = disabled;
            }
        });
    }

    async function userLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();

        if (!username || !password) {
            addLog('请输入用户名和密码', 'error');
            return;
        }

        try {
            addLog('正在登录...', 'info');
            const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();
            if (response.ok) {
                userToken = data.access_token;
                addLog(`✅ 登录成功，欢迎 ${username}`, 'success');
                updateUserInfo(username, '已登录');
                updateButtons();
                connectToServer();

                document.getElementById('deviceManagementCard').style.display = 'block';
                setTimeout(() => loadDeviceList(), 1000);
            } else {
                addLog(`❌ 登录失败: ${data.detail}`, 'error');
            }
        } catch (error) {
            addLog(`❌ 登录异常: ${error.message}`, 'error');
        }
    }

    function connectToServer() {
        if (!userToken) {
            addLog('请先登录', 'error');
            return;
        }

        // 🔒 安全修复：使用WebSocket子协议传递token，而不是URL参数
        const wsUrl = `${SERVER_CONFIG.WS_BASE_URL}/api/v1/webrtc/connect?participant_type=user&target_id=${currentTargetDevice}&connection_type=video&features=messaging`;

        addLog('连接到服务器...', 'info');
        updateConnectionStatus('🟡 正在连接...', 'calling');

        // 使用子协议传递token，更安全
        userSocket = new WebSocket(wsUrl, [`Bearer.${userToken}`]);

        userSocket.onopen = function (event) {
            userConnected = true;
            addLog('✅ 服务器连接成功', 'success');
            updateConnectionStatus('🟢 已连接服务器', 'connected');
            updateButtons();
            initUserWebRTC();
        };

        userSocket.onmessage = function (event) {
            const data = JSON.parse(event.data);
            handleUserMessage(data);
        };

        userSocket.onclose = function (event) {
            userConnected = false;
            addLog(`🔌 连接断开 (代码: ${event.code})`, 'warning');
            updateConnectionStatus('🔴 连接已断开', 'disconnected');
            updateButtons();
            cleanupUserWebRTC();
        };

        userSocket.onerror = function (error) {
            addLog(`❌ 连接错误`, 'error');
            updateConnectionStatus('🔴 连接错误', 'disconnected');
            updateButtons();
        };
    }

    function initUserWebRTC() {
        userPeerConnection = new RTCPeerConnection({iceServers: ICE_SERVERS});

        userPeerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                sendUserMessage({
                    type: 'candidate',
                    payload: {
                        candidate: event.candidate.candidate,
                        sdpMLineIndex: event.candidate.sdpMLineIndex,
                        sdpMid: event.candidate.sdpMid
                    }
                });
            }
        };

        userPeerConnection.ontrack = (event) => {
            addLog('📺 收到设备视频流', 'success');
            const remoteVideo = document.getElementById('remoteVideo');
            const stream = event.streams[0];

            if (stream && stream.getTracks().length > 0) {
                remoteVideo.srcObject = stream;
                addLog(`📺 设备流包含 ${stream.getVideoTracks().length} 个视频轨道和 ${stream.getAudioTracks().length} 个音频轨道`, 'info');

                remoteVideo.play().then(() => {
                    addLog('✅ 视频自动播放成功', 'success');
                }).catch(e => {
                    addLog(`⚠️ 自动播放失败: ${e.message}`, 'warning');
                    addLog('💡 请手动点击视频区域开始播放', 'info');
                });
            } else {
                addLog('⚠️ 收到空的设备流', 'warning');
            }
        };

        userPeerConnection.onconnectionstatechange = () => {
            const state = userPeerConnection.connectionState;
            addLog(`🔗 连接状态: ${state}`, 'info');

            if (state === 'connected') {
                callState = 'incall';
                updateConnectionStatus('📞 通话中', 'incall');
                updateButtons();
            } else if (state === 'disconnected' || state === 'failed') {
                hangupCall();
            }
        };

        updateButtons();
    }

    function cleanupUserWebRTC() {
        if (userPeerConnection) {
            userPeerConnection.close();
            userPeerConnection = null;
        }
        if (userLocalStream) {
            userLocalStream.getTracks().forEach(track => track.stop());
            userLocalStream = null;
        }
        document.getElementById('localVideo').srcObject = null;
        document.getElementById('remoteVideo').srcObject = null;
        callState = 'idle';
        updateButtons();
    }

    // 在 mobile_app_client.html 中

    /**
     * [已修复] 通过重新排序而非删除的方式，将 H.264 Baseline Profile 设为首选编解码器。
     * 这是一个更安全、更健壮的方法，可以避免破坏SDP结构。
     * @param {string} sdp - 原始的SDP字符串
     * @returns {string} - 修改后的SDP字符串
     */
    function setH264BaselineAsPreferred(sdp) {
        let sdpLines = sdp.split('\r\n');
        let h264PayloadType = null;

        // 1. 查找H.264的payload type
        for (const line of sdpLines) {
            if (line.includes('a=rtpmap:') && line.includes('H264/90000')) {
                h264PayloadType = line.match(/a=rtpmap:(\d+)/)[1];
                break;
            }
        }

        if (!h264PayloadType) {
            addLog('⚠️ SDP中未找到H.264，无法进行优化。', 'warning');
            return sdp;
        }
        addLog(`🎬 找到H.264 payload type: ${h264PayloadType}`, 'info');

        // 2. 在m=video行中将H.264的payload type移动到最前面
        const mVideoLineIndex = sdpLines.findIndex(line => line.startsWith('m=video'));
        if (mVideoLineIndex > -1) {
            let mLineParts = sdpLines[mVideoLineIndex].split(' ');
            // 第0,1,2部分是 "m=video", port, protocol
            let payloads = mLineParts.slice(3);

            // 过滤掉H.264，然后把它加到最前面
            const otherPayloads = payloads.filter(p => p !== h264PayloadType);
            const newPayloads = [h264PayloadType, ...otherPayloads];

            sdpLines[mVideoLineIndex] = [...mLineParts.slice(0, 3), ...newPayloads].join(' ');
            addLog('🎬 m=video 行已重新排序，H.264优先。', 'success');
        }

        // 3. 强制修改H.264的fmtp行，指定使用Baseline Profile
        let fmtpLineIndex = sdpLines.findIndex(line => line.startsWith(`a=fmtp:${h264PayloadType}`));
        const baselineFmtp = `a=fmtp:${h264PayloadType} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f`;

        if (fmtpLineIndex > -1) {
            sdpLines[fmtpLineIndex] = baselineFmtp;
            addLog(`🎬 已修改H.264的fmtp行以强制使用Baseline Profile。`, 'success');
        } else {
            // 如果不存在fmtp行，就添加一个
            sdpLines.splice(mVideoLineIndex + 1, 0, baselineFmtp);
            addLog(`🎬 已添加H.264的fmtp行以强制使用Baseline Profile。`, 'success');
        }

        return sdpLines.join('\r\n');
    }

    // 在 mobile_app_client.html 中

    async function makeCall() {
        if (!userLocalStream) {
            addLog('请先启动摄像头/麦克风', 'error');
            return;
        }

        try {
            addLog(`📞 呼叫设备 ${currentTargetDevice}...`, 'info');
            callState = 'calling';
            updateConnectionStatus('📞 正在呼叫...', 'calling');
            updateButtons();

            const offer = await userPeerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });

            // === [核心修复] 调用新的、更安全的SDP修改函数 ===
            addLog('🎬 [关键] 正在使用安全模式修改SDP，将H.264设为首选...', 'info');
            offer.sdp = setH264BaselineAsPreferred(offer.sdp);
            // ===============================================

            // 只有在SDP被正确处理后，才设置本地描述
            await userPeerConnection.setLocalDescription(offer);
            addLog('🎬 已设置本地描述 (LocalDescription)，准备发送Offer。', 'success');

            sendUserMessage({
                type: 'offer',
                payload: {
                    type: 'offer',
                    sdp: offer.sdp
                }
            });

            addLog('📤 通话邀请 (Offer) 已发送', 'success');
        } catch (error) {
            addLog(`❌ 发起通话失败: ${error.message}`, 'error');
            console.error("发起通话失败的详细信息:", error); // 在控制台打印详细错误
            callState = 'idle';
            updateConnectionStatus('🟢 已连接服务器', 'connected');
            updateButtons();
        }
    }

    function hangupCall() {
        addLog('📞 主动挂断通话', 'info');
        sendUserMessage({type: 'hangup', payload: {}});
        callState = 'idle';
        updateConnectionStatus('🟢 已连接服务器', 'connected');
        if (userPeerConnection) {
            userPeerConnection.close();
            userPeerConnection = null;
        }
        document.getElementById('remoteVideo').srcObject = null;
        updateButtons();
    }

    function toggleVideo() {
        if (userLocalStream) {
            const videoTrack = userLocalStream.getVideoTracks()[0];
            if (videoTrack) {
                videoEnabled = !videoEnabled;
                videoTrack.enabled = videoEnabled;
                document.getElementById('videoBtn').style.opacity = videoEnabled ? '1' : '0.5';
                addLog(`📹 视频${videoEnabled ? '开启' : '关闭'}`, 'info');
            }
        }
    }

    function toggleAudio() {
        if (userLocalStream) {
            const audioTrack = userLocalStream.getAudioTracks()[0];
            if (audioTrack) {
                audioEnabled = !audioEnabled;
                audioTrack.enabled = audioEnabled;
                document.getElementById('audioBtn').style.opacity = audioEnabled ? '1' : '0.5';
                addLog(`🎤 音频${audioEnabled ? '开启' : '静音'}`, 'info');
            }
        }
    }

    function handleUserMessage(data) {
        const messageType = data.type;
        addLog(`📨 收到: ${messageType}`, 'success');

        switch (messageType) {
            case 'answer':
                handleUserAnswer(data.payload);
                break;
            case 'candidate':
                handleUserCandidate(data.payload);
                break;
            case 'hangup':
                handleUserHangup();
                break;
        }
    }

    async function handleUserAnswer(payload) {
        try {
            const answer = new RTCSessionDescription(payload);
            await userPeerConnection.setRemoteDescription(answer);
            addLog('✅ Answer处理完成', 'success');
        } catch (error) {
            addLog(`❌ 处理Answer失败: ${error.message}`, 'error');
        }
    }

    async function handleUserCandidate(payload) {
        try {
            const candidate = new RTCIceCandidate(payload);
            await userPeerConnection.addIceCandidate(candidate);
        } catch (error) {
            addLog(`❌ 添加ICE候选失败: ${error.message}`, 'error');
        }
    }

    function handleUserHangup() {
        addLog('📞 对方挂断了通话', 'warning');
        hangupCall();
    }

    function sendUserMessage(message) {
        if (userSocket && userConnected) {
            userSocket.send(JSON.stringify(message));
        }
    }

    function clearLog() {
        document.getElementById('callLog').innerHTML = '<div class="log-entry info">[系统] 日志已清空</div>';
    }

    async function loadDeviceList() { /* ... 保持不变 ... */
    }

    function renderDeviceList(devices) { /* ... 保持不变 ... */
    }

    function getDeviceIcon(deviceType) { /* ... 保持不变 ... */
    }

    async function refreshDeviceList() { /* ... 保持不变 ... */
    }

    function updateDeviceStats() { /* ... 保持不变 ... */
    }

    async function startDeviceStatusUpdates() { /* ... 保持不变 ... */
    }

    async function updateAllDeviceStatus() { /* ... 保持不变 ... */
    }

    async function updateDeviceStatus(deviceId) { /* ... 保持不变 ... */
    }

    function showDeviceMenu(deviceId) { /* ... 保持不变 ... */
    }

    async function unbindDevice(deviceId) { /* ... 保持不变 ... */
    }

    function showAddDeviceDialog() { /* ... 保持不变 ... */
    }

    function showBindingCodeDialog() { /* ... 保持不变 ... */
    }

    async function bindDeviceWithCode(deviceId, bindingCode, deviceName) { /* ... 保持不变 ... */
    }

    async function bindDevice(deviceId, deviceName) { /* ... 保持不变 ... */
    }

    // === 省略了设备管理相关的所有函数，因为它们与绿屏问题无关，保持原样即可 ===
    async function loadDeviceList() {
        if (!userToken) return;
        const deviceListEl = document.getElementById('deviceList');
        deviceListEl.innerHTML = '<div class="loading-indicator"><div class="spinner"></div><p>加载设备列表...</p></div>';
        try {
            const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/devices?token=${userToken}`);
            const result = await response.json();
            if (result.success) {
                userDevices = result.data.devices;
                renderDeviceList(userDevices);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            addLog(`❌ 加载设备列表失败: ${error.message}`, 'error');
            deviceListEl.innerHTML = '<p>加载失败</p>';
        }
    }

    function renderDeviceList(devices) {
        const deviceListEl = document.getElementById('deviceList');
        if (!devices || devices.length === 0) {
            deviceListEl.innerHTML = '<p>没有设备</p>';
            return;
        }
        deviceListEl.innerHTML = devices.map(device => `
                <div class="device-item" data-device-id="${device.device_id}">
                    <div class="device-info">
                        <div class="device-icon">📹</div>
                        <div class="device-details">
                            <h4>${device.device_name}</h4>
                            <p>${device.device_id}</p>
                        </div>
                    </div>
                    <button class="device-actions-btn" onclick="callDevice('${device.device_id}', '${device.device_name}')">📞 呼叫</button>
                </div>
            `).join('');
    }

    function callDevice(deviceId, deviceName) {
        currentTargetDevice = deviceId;
        addLog(`📞 准备呼叫: ${deviceName} (${deviceId})`, 'info');
        makeCall();
    }

    function setCallMode(mode) {
        currentCallMode = mode;
        document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(mode + 'ModeBtn').classList.add('active');
        const cameraBtn = document.getElementById('startCameraBtn');
        cameraBtn.textContent = mode === 'audio' ? '🎤 启动麦克风' : '📹 启动摄像头';
    }

    // 在 mobile_app_client.html 中

    // 在 mobile_app_client.html 中

async function startCamera() {
    try {
        addLog(currentCallMode === 'audio' ? '🎤 启动麦克风...' : '📹 启动摄像头...', 'info');

        // 枚举设备以获取准确信息
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        const audioDevices = devices.filter(device => device.kind === 'audioinput');

        addLog(`✅ 检测到 ${videoDevices.length} 个摄像头和 ${audioDevices.length} 个麦克风。`, 'success');

        let videoStream = null;
        let audioStream = null;

        // === [核心修复] 分步请求媒体流，先视频后音频 ===

        // 步骤 1: 根据通话模式请求视频流 (如果需要)
        if (currentCallMode === 'video' && videoDevices.length > 0) {
            try {
                addLog('🎬 步骤1: 正在请求视频流...', 'info');
                const videoConstraints = { video: true };
                videoStream = await navigator.mediaDevices.getUserMedia(videoConstraints);
                const videoTrack = videoStream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                addLog(`✅ 成功获取视频流: ${settings.width}x${settings.height}`, 'success');
            } catch (videoError) {
                addLog(`❌ 获取视频流失败: ${videoError.message}`, 'error');
                throw videoError; // 如果视频是必须的但失败了，则直接抛出异常
            }
        }

        // 步骤 2: 请求音频流 (总是需要)
        if (audioDevices.length > 0) {
            try {
                addLog('🎬 步骤2: 正在请求音频流...', 'info');
                const audioConstraints = { audio: true };
                audioStream = await navigator.mediaDevices.getUserMedia(audioConstraints);
                addLog('✅ 成功获取音频流', 'success');
            } catch (audioError) {
                addLog(`⚠️ 获取音频流失败: ${audioError.message}。通话将没有声音。`, 'warning');
                // 不抛出异常，允许无声通话
            }
        } else {
            addLog('⚠️ 未检测到麦克风，通话将没有声音。', 'warning');
        }

        // 步骤 3: 合并视频和音频轨道到一个 userLocalStream 中
        if (videoStream) {
            userLocalStream = videoStream;
            if (audioStream) {
                userLocalStream.addTrack(audioStream.getAudioTracks()[0]);
            }
        } else if (audioStream) {
            userLocalStream = audioStream;
        } else {
            throw new Error("无法获取任何视频或音频设备。");
        }

        addLog(`🎵 最终媒体流包含 ${userLocalStream.getVideoTracks().length} 个视频轨道和 ${userLocalStream.getAudioTracks().length} 个音频轨道。`, 'info');

        // 将流显示在界面上
        if (currentCallMode === 'video' && userLocalStream.getVideoTracks().length > 0) {
            document.getElementById('localVideo').srcObject = userLocalStream;
        }

        // 将轨道添加到 WebRTC 连接
        if (userPeerConnection) {
            userLocalStream.getTracks().forEach(track => {
                const sender = userPeerConnection.getSenders().find(s => s.track && s.track.kind === track.kind);
                if (sender) {
                    sender.replaceTrack(track);
                } else {
                    userPeerConnection.addTrack(track, userLocalStream);
                }
            });
        }

        addLog('✅ 媒体设备已就绪', 'success');
        updateButtons();

    } catch (error) {
        let errorMessage = `❌ 启动媒体设备失败: ${error.message}`;
        if (error.name === 'NotAllowedError') {
            errorMessage += ' (您可能拒绝了媒体权限请求)';
        } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
            errorMessage += ' (浏览器找不到任何匹配的设备)';
        } else if (error.name === 'NotReadableError') {
             errorMessage += ' (设备可能被其他程序占用或存在硬件/驱动问题)';
        }
        addLog(errorMessage, 'error');
        console.error("启动媒体设备失败的详细信息:", error);
    }
}

    // === 绿屏修复：强制SDP只使用H.264 Baseline Profile的函数 ===
    /**
     * 强制SDP只使用H.264 Baseline Profile，并移除其他视频编解码器。
     * @param {string} sdp - 原始的SDP字符串
     * @returns {string} - 修改后的SDP字符串
     */
    function forceH264BaselineProfile(sdp) {
        let sdpLines = sdp.split('\r\n');
        let h264PayloadType = null;
        const otherVideoPayloadTypes = [];

        // 1. 查找H.264的payload type
        const mVideoLineIndex = sdpLines.findIndex(line => line.startsWith('m=video'));
        if (mVideoLineIndex === -1) return sdp; // 没有视频，无需处理

        const mVideoLine = sdpLines[mVideoLineIndex];
        const videoPayloads = mVideoLine.split(' ').slice(3);

        for (const payload of videoPayloads) {
            const rtpmapLine = sdpLines.find(line => line.includes(`a=rtpmap:${payload} H264/90000`));
            if (rtpmapLine) {
                h264PayloadType = payload;
            } else {
                otherVideoPayloadTypes.push(payload);
            }
        }

        if (!h264PayloadType) {
            addLog('⚠️ 警告：在Offer中未找到H.264编解码器，无法强制配置。', 'warning');
            return sdp;
        }

        addLog(`🎬 找到H.264 payload type: ${h264PayloadType}`, 'info');

        // 2. 修改m=video行，只保留H.264
        const parts = mVideoLine.split(' ');
        sdpLines[mVideoLineIndex] = `${parts.slice(0, 3).join(' ')} ${h264PayloadType}`;
        addLog(`🎬 m=video行已修改，只保留H.264`, 'info');

        // 3. 移除其他视频编解码器的相关行 (rtpmap, rtcp-fb, fmtp)
        sdpLines = sdpLines.filter(line => {
            const isOtherVideoCodecLine = otherVideoPayloadTypes.some(pt =>
                line.includes(`a=rtpmap:${pt}`) || line.includes(`a=rtcp-fb:${pt}`) || line.includes(`a=fmtp:${pt}`)
            );
            if (isOtherVideoCodecLine) {
                addLog(`🎬 移除不兼容的编解码器行: ${line}`, 'info');
            }
            return !isOtherVideoCodecLine;
        });

        // 4. 修改或添加H.264的fmtp行，强制使用Baseline Profile (profile-level-id=42e01f)
        let fmtpLineIndex = sdpLines.findIndex(line => line.includes(`a=fmtp:${h264PayloadType}`));
        const baselineFmtp = `a=fmtp:${h264PayloadType} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f`;

        if (fmtpLineIndex > -1) {
            addLog(`🎬 强制设置 profile-level-id=42e01f (Baseline Profile)`, 'success');
            sdpLines[fmtpLineIndex] = baselineFmtp;
        } else {
            sdpLines.splice(mVideoLineIndex + 1, 0, baselineFmtp);
            addLog(`🎬 添加了强制的H.264 fmtp行`, 'success');
        }

        return sdpLines.join('\r\n');
    }

    function diagnoseVideoIssues() {
        addLog('🔍 开始视频问题诊断...', 'info');
        // ... (诊断函数保持不变)
    }

    function fixGreenScreen() {
        addLog('🟢 尝试通过重新协商连接来修复绿屏...', 'info');
        if (userPeerConnection && (callState === 'incall' || callState === 'calling')) {
            makeCall(); // 重新发起带有强制SDP的呼叫
        } else {
            addLog('❌ 不在通话中，无法修复', 'error');
        }
    }

    function initializeApp() {
        updateButtons();
        addLog('智能家居App初始化完成', 'success');
    }

    document.addEventListener('DOMContentLoaded', initializeApp);

</script>
</body>
</html>