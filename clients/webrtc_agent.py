# -*- coding: utf-8 -*-
"""
WebRTC 模块，负责处理 WebRTC 视频流、通话相关的逻辑。
"""
from PySide6.QtCore import QObject, Signal, Slot

class WebRTC_Agent(QObject):
    # 信号用于向 QML 发送视频流和通话状态更新
    video_frame_signal = Signal(str) # QML中使用Image，需要base64或URL
    webrtc_log_signal = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.webrtc_log_signal.emit("[WebRTC] 模块已加载。")

    @Slot()
    def start_device_camera(self):
        """启动设备摄像头"""
        self.webrtc_log_signal.emit("[WebRTC] 正在启动摄像头...")
        # 实际代码会在这里获取摄像头流并添加到 webrtc_peer_connection
        self.webrtc_log_signal.emit("[WebRTC] 摄像头已启动。")
        # 模拟视频帧更新，实际中会从摄像头获取
        # self.video_frame_signal.emit("data:image/png;base64,...")

    @Slot()
    def answer_call(self):
        """接听通话"""
        self.webrtc_log_signal.emit("[WebRTC] 正在接听通话...")
        self.webrtc_log_signal.emit("[WebRTC] 通话已接通。")

    @Slot()
    def hangup_device(self):
        """挂断通话"""
        self.webrtc_log_signal.emit("[WebRTC] 正在挂断通话...")
        self.webrtc_log_signal.emit("[WebRTC] 通话已挂断。")

