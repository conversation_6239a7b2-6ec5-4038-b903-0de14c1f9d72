import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import com.example 1.0 // 导入新注册的QML模块

ApplicationWindow {
    id: appWindow
    width: 800
    height: 600
    visible: true
    title: "智能设备代理"

    // 修复: 在QML组件完成加载时，安全地设置依赖于Python后端的属性
    Component.onCompleted: {
        // 检查api对象是否已存在，并设置设备ID
        if (api) {
            deviceIdLabel.text = api.device_id
        }
    }

    // 主布局
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        // 标题和副标题
        ColumnLayout {
            Layout.fillWidth: true
            Layout.alignment: Qt.AlignHCenter

            Text {
                text: "🤖 智能设备代理"
                font.pixelSize: 24
                font.bold: true
                Layout.alignment: Qt.AlignHCenter
            }
            Text {
                text: "WebRTC 视频通话系统 - 设备控制台"
                font.pixelSize: 16
                color: "gray"
                Layout.alignment: Qt.AlignHCenter
            }
        }

        // 设备信息面板
        Frame {
            Layout.fillWidth: true
            Layout.preferredHeight: 80
            background: Rectangle {
                color: "#e8e8e8"
                radius: 10
            }

            GridLayout {
                anchors.fill: parent
                anchors.margins: 10
                columns: 2
                Text { text: "🏷️ 设备ID:" }
                Text {
                    id: deviceIdLabel
                    text: "N/A" // 默认值
                }
                Text { text: "📡 连接状态:" }
                Text {
                    id: networkStatusLabel
                    text: "离线"
                    color: "red"
                    font.bold: true
                }
                Text { text: "🌡️ 设备温度:" }
                Text { text: "42°C" }
            }
        }

        // 信号槽连接
        Connections {
            target: api
            function onNetwork_status_signal(status) {
                networkStatusLabel.text = status
                networkStatusLabel.color = status === "在线" ? "#2ecc71" : "red"
            }
        }

        // 设备配置与控制面板
        Frame {
            Layout.fillWidth: true
            background: Rectangle {
                color: "#e8e8e8"
                radius: 10
            }

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10

                Text { text: "<h3>设备配置</h3>" }

                GridLayout {
                    Layout.fillWidth: true
                    columns: 2
                    Text { text: "设备名称:" }
                    TextInput {
                        id: deviceNameInput
                        Layout.fillWidth: true
                        text: "客厅智能摄像头"
                    }
                    Text { text: "设备位置:" }
                    TextInput {
                        id: deviceLocationInput
                        Layout.fillWidth: true
                        text: "客厅"
                    }
                }

                RowLayout {
                    Layout.fillWidth: true
                    Button {
                        id: registerButton
                        text: "📝 注册设备"
                        background: Rectangle { color: "#2ecc71"; radius: 5 }
                        contentItem: Text {
                            text: parent.text; font.pixelSize: 14; color: "white";
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        onClicked: if (api) api.register_device(deviceNameInput.text, deviceLocationInput.text)
                    }
                    Button {
                        id: connectButton
                        text: "🔗 连接服务器"
                        background: Rectangle { color: "#f1c40f"; radius: 5 }
                        contentItem: Text {
                            text: parent.text; font.pixelSize: 14; color: "white";
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        onClicked: if (api) api.connect_device()
                    }
                    Button {
                        id: disconnectButton
                        text: "❌ 断开连接"
                        background: Rectangle { color: "#e74c3c"; radius: 5 }
                        contentItem: Text {
                            text: parent.text; font.pixelSize: 14; color: "white";
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        onClicked: if (api) api.disconnect_device()
                    }
                }

                RowLayout {
                    Layout.fillWidth: true
                    // 调用新模块中的方法
                    Button { text: "📹 启动摄像头"; onClicked: if (webrtc) webrtc.start_device_camera() }
                    Button { text: "📞 接听通话"; onClicked: if (webrtc) webrtc.answer_call() }
                    Button { text: "📞 挂断通话"; onClicked: if (webrtc) webrtc.hangup_device() }
                }

                Button {
                    Layout.fillWidth: true
                    text: "🛒 测试支付"
                    onClicked: if (api) api.test_payment_flow()
                }
            }
        }

        // WebRTC 视频流面板 (占位符)
        Frame {
            Layout.fillWidth: true
            Layout.preferredHeight: 480
            background: Rectangle {
                color: "black"
                radius: 10
            }
            ColumnLayout {
                anchors.fill: parent
                Text { text: "<h3>WebRTC 视频流</h3>"; color: "white"; Layout.alignment: Qt.AlignHCenter }
                Rectangle {
                    id: videoDisplay
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: "black"
                    radius: 5
                    border.width: 2
                    border.color: "#555"

                    Text {
                        anchors.centerIn: parent
                        text: "等待视频流..."
                        color: "white"
                        font.pixelSize: 18
                    }
                }
            }
        }

        // 日志面板
        Frame {
            Layout.fillWidth: true
            Layout.preferredHeight: 150
            background: Rectangle {
                color: "black"
                radius: 10
            }

            ColumnLayout {
                anchors.fill: parent
                Text { text: "<h3>设备日志</h3>"; color: "green" }
                TextArea {
                    id: logTextEdit
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    readOnly: true
                    text: "[系统] 设备代理已启动。\n[系统] 等待设备注册..."
                    background: Rectangle { color: "black" }
                    font.pixelSize: 12
                    font.family: "Monospace"
                    color: "green"

                    // 滚动到底部
                    onTextChanged: {
                        // 修复: 添加安全检查
                        if (logTextEdit.flickable) {
                            logTextEdit.flickable.contentY = logTextEdit.flickable.contentHeight - logTextEdit.flickable.height
                        }
                    }
                }
            }
        }

        // 将Python的log_signal连接到QML的日志文本区
        Connections {
            target: api
            function onLog_signal(message) {
                logTextEdit.text += "\n" + message
            }
        }
    }
}
