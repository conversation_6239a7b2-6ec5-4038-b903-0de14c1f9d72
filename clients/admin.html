<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI订单履约系统后台管理</title>
  <!-- 引入 Element Plus 的样式 -->
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
  <!--
    Tailwind CSS CDN for quick prototyping.
    For production, it is recommended to install Tailwind as a PostCSS plugin:
    https://tailwindcss.com/docs/installation
  -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!--
    Vue.js development build.
    For production, use the production build (*.prod.js) for better performance:
    https://vuejs.org/guide/quick-start.html#using-vue-from-cdn
  -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- 引入 Element Plus 的 JS -->
  <script src="https://unpkg.com/element-plus"></script>
  <style>
    /* 移除之前自定义的登录页面样式，直接使用 Tailwind 进行布局 */
  </style>
</head>
<body class="font-sans antialiased text-gray-900 bg-gray-50">

<div id="app" class="flex h-screen overflow-hidden">
  <!-- Vue.js 组件将在此处渲染 -->
</div>

<script type="module">
  // Vue Composition API 的辅助函数
  const { createApp, ref, reactive, onMounted, watch } = Vue;
  const { ElButton, ElTable, ElTableColumn, ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElTag, ElLoading, ElAlert, ElMessage, ElPagination } = ElementPlus;

  // ===================== 状态映射 =====================
  const statusMap = {
    pending_payment: { text: '待支付', type: 'info' },
    paid: { text: '已支付', type: '' },
    processing: { text: '处理中', type: 'warning' },
    ready_for_pickup: { text: '备料完成', type: 'success' },
    completed: { text: '已完成', type: 'info' },
    cancelled: { text: '已取消', type: 'danger' },
  };

  const baseUrl = 'http://127.0.0.1:8000';

  // ===================== 登录/注册组件 =====================
  const AuthPage = {
    emits: ['login-success'],
    setup(props, { emit }) {
      const isLoading = ref(false);
      const isLoginMode = ref(true);
      const authError = ref(null);
      const loginForm = reactive({ username: '', password: '' });
      const registerForm = reactive({
        username: '',
        password: '',
        role: 'webrtc_app_user'
      });

      const handleLogin = async () => {
        isLoading.value = true;
        authError.value = null;
        try {
          const response = await fetch(`${baseUrl}/api/v1/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              username: loginForm.username,
              password: loginForm.password
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `API error: ${response.statusText}`);
          }
          const data = await response.json();
          emit('login-success', data.access_token);
        } catch (error) {
          console.error('Login failed:', error);
          authError.value = `登录失败: ${error.message}`;
        } finally {
          isLoading.value = false;
        }
      };

      const handleRegister = async () => {
        isLoading.value = true;
        authError.value = null;
        try {
          const response = await fetch(`${baseUrl}/api/v1/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(registerForm)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `API error: ${response.statusText}`);
          }

          ElMessage.success('注册成功，请使用新账号登录！');
          isLoginMode.value = true;
          loginForm.username = registerForm.username;
          loginForm.password = '';
        } catch (error) {
          console.error('Register failed:', error);
          authError.value = `注册失败: ${error.message}`;
        } finally {
          isLoading.value = false;
        }
      };

      return {
        isLoginMode,
        loginForm,
        registerForm,
        authError,
        isLoading,
        handleLogin,
        handleRegister,
      };
    },
    template: `
      <div class="flex flex-col items-center justify-center w-full h-full bg-gray-100 p-6">
        <el-card class="w-full max-w-sm rounded-xl shadow-md">
          <h2 class="text-3xl font-bold mb-6 text-center text-indigo-600">{{ isLoginMode ? '后台管理登录' : '新用户注册' }}</h2>
          <el-alert v-if="authError" :title="authError" type="error" show-icon class="mb-4" />
          <el-form v-if="isLoginMode" :model="loginForm" @submit.prevent="handleLogin" label-position="top">
            <el-form-item label="用户名">
              <el-input v-model="loginForm.username" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" native-type="submit" :loading="isLoading" class="w-full mt-4">登录</el-button>
            </el-form-item>
          </el-form>
          <el-form v-else :model="registerForm" @submit.prevent="handleRegister" label-position="top">
            <el-form-item label="用户名">
              <el-input v-model="registerForm.username" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" show-password />
            </el-form-item>
            <el-form-item label="角色（默认为普通用户）">
              <el-input v-model="registerForm.role" disabled />
            </el-form-item>
            <el-form-item>
              <el-button type="success" native-type="submit" :loading="isLoading" class="w-full mt-4">注册</el-button>
            </el-form-item>
          </el-form>
          <div class="mt-4 text-center text-sm">
            <a href="#" @click.prevent="isLoginMode = !isLoginMode" class="text-indigo-600 hover:underline">
              {{ isLoginMode ? '没有账号？立即注册' : '已有账号？返回登录' }}
            </a>
          </div>
        </el-card>
      </div>
    `
  };

  // ===================== 侧边栏组件 =====================
  const Sidebar = {
    emits: ['go-back'],
    template: `
      <div class="w-64 bg-white border-r border-gray-200 p-4">
        <h1 class="text-2xl font-bold mb-8 text-indigo-600">AI 订单履约</h1>
        <nav class="space-y-2">
          <a href="#" @click.prevent="$emit('go-back')" class="flex items-center p-3 rounded-xl bg-indigo-100 text-indigo-600 font-semibold">
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a8 8 0 100 16 8 8 0 000-16zM5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>
            订单管理
          </a>
          <a href="#" @click.prevent class="flex items-center p-3 rounded-xl text-gray-500 hover:bg-gray-100">
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 11.237A4.001 4.001 0 0010 10a4 4 0 00-2.93 1.237 5.999 5.999 0 00-4.07 4.763c.***************.022.253a6 6 0 0012.016 0c.01-.084.017-.167.022-.253a5.999 5.999 0 00-4.07-4.763z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>
            备料员工管理
          </a>
          <a href="#" @click.prevent class="flex items-center p-3 rounded-xl text-gray-500 hover:bg-gray-100">
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 10H5a1 1 0 110-2h4V5a1 1 0 112 0v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>
            系统设置
          </a>
        </nav>
      </div>
    `
  };

  // ===================== 头部组件 =====================
  const AppHeader = {
    props: ['handleLogout'],
    template: `
      <header class="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
        <div class="text-gray-600 font-medium">当前用户：系统管理员</div>
        <el-button @click="handleLogout">退出</el-button>
      </header>
    `
  };

  // ===================== 订单列表组件 =====================
  const OrderList = {
    props: ['orders', 'isLoading', 'apiError', 'searchForm', 'statusMap', 'currentPage', 'pageSize', 'totalItems'],
    emits: ['select-order', 'search', 'reset', 'update:currentPage'],
    template: `
      <div class="p-6 bg-gray-100 min-h-screen">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">订单列表</h2>
        <el-alert v-if="apiError" :title="apiError" type="error" show-icon class="mb-4" />
        <el-card class="mb-6 rounded-xl shadow-md">
          <el-form :inline="true" :model="searchForm">
            <el-form-item label="订单ID">
              <el-input v-model="searchForm.id" placeholder="请输入订单ID" />
            </el-form-item>
            <el-form-item label="订单状态">
              <el-select v-model="searchForm.status" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option v-for="(item, key) in statusMap" :key="key" :label="item.text" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="$emit('search')" class="mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-4 h-4 mr-1"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
                搜索
              </el-button>
              <el-button @click="$emit('reset')">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter w-4 h-4 mr-1"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"/></svg>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="rounded-xl shadow-md">
          <div v-loading="isLoading">
            <el-table :data="orders" class="w-full">
              <el-table-column prop="id" label="订单ID" width="180" />
              <el-table-column prop="created_at" label="创建时间" width="180" />
              <el-table-column label="状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="statusMap[row.status]?.type">
                    {{ statusMap[row.status]?.text || '未知' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="device_id" label="设备ID" />
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="primary" link @click="$emit('select-order', row.id)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-center mt-6">
                <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :total="totalItems"
                    layout="total, prev, pager, next"
                    @current-change="$emit('update:currentPage', $event)"
                />
            </div>
          </div>
        </el-card>
      </div>
    `
  };

  // ===================== 订单详情组件 =====================
  const OrderDetail = {
    props: ['currentOrder', 'isLoading', 'statusMap'],
    emits: ['go-back', 'download'],
    template: `
      <div v-if="currentOrder" class="p-6 bg-gray-100 min-h-screen">
        <div class="flex items-center mb-6">
          <el-button @click="$emit('go-back')" text>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left w-5 h-5 mr-1"><path d="m15 18-6-6 6-6"/></svg>
            返回订单列表
          </el-button>
          <h2 class="ml-4 text-2xl font-bold text-gray-800">订单详情 - {{ currentOrder.id }}</h2>
        </div>
        <div v-loading="isLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <el-card class="rounded-xl shadow-md">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">订单概览</h3>
            <div class="space-y-3 text-sm">
              <p><strong>订单ID:</strong> {{ currentOrder.id }}</p>
              <p><strong>状态:</strong> <el-tag :type="statusMap[currentOrder.status]?.type">{{ statusMap[currentOrder.status]?.text }}</el-tag></p>
              <p><strong>创建时间:</strong> {{ currentOrder.created_at }}</p>
              <p><strong>设备ID:</strong> {{ currentOrder.device_id }}</p>
              <p><strong>总金额:</strong> ¥{{ currentOrder.total_amount?.toFixed(2) }}</p>
            </div>
          </el-card>
          <el-card class="rounded-xl shadow-md">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">备料任务详情</h3>
            <el-table :data="currentOrder.items" class="w-full">
              <el-table-column prop="product_name" label="菜品名称" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="price_at_purchase" label="单价" width="80" />
              <el-table-column prop="total_price" label="总价" width="80" />
            </el-table>
          </el-card>
        </div>
        <el-card class="mt-6 rounded-xl shadow-md">
          <h3 class="text-xl font-semibold mb-4 text-gray-700">备料过程视频</h3>
          <div class="flex flex-col items-center">
            <div class="w-full max-w-4xl bg-black rounded-lg overflow-hidden mb-4">
              <video class="w-full" controls src="https://www.w3schools.com/html/mov_bbb.mp4">
                您的浏览器不支持视频播放。
              </video>
            </div>
            <el-button type="primary" @click="$emit('download')">下载视频</el-button>
          </div>
        </el-card>
      </div>
      <div v-else class="p-6">
        <el-alert title="找不到订单详情或数据正在加载中" type="warning" show-icon />
      </div>
    `
  };


  // ===================== 主应用组件 =====================
  const App = {
    setup() {
      const isLoading = ref(false);
      const orders = ref([]);
      const currentOrder = ref(null);
      const apiError = ref(null);
      const isLoggedIn = ref(false);
      const authToken = ref(localStorage.getItem('authToken') || null);
      const currentPage = ref('list');
      const selectedOrderId = ref(null);
      const searchForm = reactive({ id: '', status: '', dateRange: '' });

      // 分页状态
      const page = ref(1);
      const pageSize = ref(20);
      const totalItems = ref(0);

      const onLoginSuccess = (token) => {
        authToken.value = token;
        localStorage.setItem('authToken', token);
        isLoggedIn.value = true;
        fetchOrders();
      };

      const handleLogout = () => {
        authToken.value = null;
        localStorage.removeItem('authToken');
        isLoggedIn.value = false;
        orders.value = [];
        currentOrder.value = null;
        currentPage.value = 'list';
      };

      const fetchOrders = async () => {
        if (!authToken.value) return;
        isLoading.value = true;
        apiError.value = null;
        try {
          const queryParams = new URLSearchParams({
            page: page.value,
            page_size: pageSize.value,
          });

          // The 'pending' endpoint does not support search filters, so we only pass pagination params.
          const response = await fetch(`${baseUrl}/api/v1/staff/orders/pending?${queryParams.toString()}`, {
            headers: { 'Authorization': `Bearer ${authToken.value}` }
          });

          if (!response.ok) {
            if (response.status === 401) {
              handleLogout();
              throw new Error('认证失败，请重新登录。');
            }
            throw new Error(`API error: ${response.statusText}`);
          }
          const data = await response.json();
          orders.value = data.items;
          // Correctly use the 'total' field from the API response
          totalItems.value = data.total;
        } catch (error) {
          console.error('Failed to fetch orders:', error);
          apiError.value = `获取订单列表失败: ${error.message}`;
        } finally {
          isLoading.value = false;
        }
      };

      const fetchOrderDetails = async (orderId) => {
        if (!orderId || !authToken.value) return;
        isLoading.value = true;
        apiError.value = null;
        try {
          const response = await fetch(`${baseUrl}/api/v1/orders/${orderId}`, {
            headers: { 'Authorization': `Bearer ${authToken.value}` }
          });
          if (!response.ok) {
            if (response.status === 401) {
              handleLogout();
              throw new Error('认证失败，请重新登录。');
            }
            throw new Error(`API error: ${response.statusText}`);
          }
          const data = await response.json();
          currentOrder.value = data;
        } catch (error) {
          console.error('Failed to fetch order details:', error);
          apiError.value = `获取订单详情失败: ${error.message}`;
        } finally {
          isLoading.value = false;
        }
      };

      const handleSearch = () => {
        // Reset to the first page when a new search is performed
        page.value = 1;
        fetchOrders();
      };

      const handleReset = () => {
        searchForm.id = '';
        searchForm.status = '';
        searchForm.dateRange = '';
        // Reset to the first page and fetch all orders
        page.value = 1;
        fetchOrders();
      };

      const selectOrder = (id) => {
        selectedOrderId.value = id;
        currentPage.value = 'details';
      };

      const goBack = () => {
        currentPage.value = 'list';
        selectedOrderId.value = null;
      };

      const handleDownload = () => {
        if (currentOrder.value && currentOrder.value.videoUrl) {
          window.open(currentOrder.value.videoUrl, '_blank');
        } else {
          ElMessage.warning('没有可供下载的视频。');
        }
      };

      const handlePageChange = (newPage) => {
          page.value = newPage;
          fetchOrders();
      };

      onMounted(() => {
        if (authToken.value) {
          isLoggedIn.value = true;
          fetchOrders();
        }
      });

      watch(selectedOrderId, (newId) => {
        if (newId) {
          fetchOrderDetails(newId);
        }
      });

      return {
        isLoggedIn,
        onLoginSuccess,
        handleLogout,
        goBack,
        handleDownload,
        currentPage,
        orders,
        isLoading,
        apiError,
        searchForm,
        statusMap,
        selectOrder,
        handleSearch,
        handleReset,
        currentOrder,
        page,
        pageSize,
        totalItems,
        handlePageChange,
      };
    },
    template: `
      <auth-page v-if="!isLoggedIn" @login-success="onLoginSuccess" />
      <template v-else>
        <sidebar @go-back="goBack" />
        <div class="flex-1 overflow-y-auto">
          <app-header :handle-logout="handleLogout" />
          <order-list
            v-if="currentPage === 'list'"
            :orders="orders"
            :is-loading="isLoading"
            :api-error="apiError"
            :search-form="searchForm"
            :status-map="statusMap"
            :currentPage="page"
            :pageSize="pageSize"
            :totalItems="totalItems"
            @select-order="selectOrder"
            @search="handleSearch"
            @reset="handleReset"
            @update:currentPage="handlePageChange"
          />
          <order-detail
            v-else-if="currentPage === 'details'"
            :current-order="currentOrder"
            :is-loading="isLoading"
            :status-map="statusMap"
            @go-back="goBack"
            @download="handleDownload"
          />
        </div>
      </template>
    `
  };

  const app = createApp(App);
  app.component('AuthPage', AuthPage);
  app.component('Sidebar', Sidebar);
  app.component('AppHeader', AppHeader);
  app.component('OrderList', OrderList);
  app.component('OrderDetail', OrderDetail);
  app.use(ElementPlus);
  app.mount('#app');
</script>

</body>
</html>
