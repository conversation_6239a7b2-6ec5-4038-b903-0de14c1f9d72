<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员页面</title>
    <!-- 引入 Tailwind CSS 进行布局和样式设计 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 使用 Inter 字体获得更现代的观感 */
        body {
            font-family: 'Inter', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f3f4f6;
        }
        /* 自定义 Element Plus 样式以匹配整体设计 */
        .el-menu {
            border-right: none;
        }
        .el-card__header {
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
        }
        /* 确保页面填满视口 */
        #app {
            height: 100vh;
        }
    </style>
    <!-- 引入 Vue 3 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.min.js"></script>
    <!-- 引入 Element Plus 的 CSS 和 JS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus@2.6.2/dist/index.css">
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.6.2/dist/index.full.min.js"></script>
    <!-- 引入 Element Plus 的图标库 -->
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js"></script>
</head>
<body>

    <div id="app" class="flex flex-col h-screen">
        <!-- 根据登录状态显示不同内容 -->
        <div v-if="isLoggedIn" class="flex flex-grow">
            <!-- 侧边栏导航区 -->
            <aside class="w-64 bg-white shadow-md flex-shrink-0">
                <div class="p-6 text-2xl font-bold text-gray-800 border-b border-gray-200 flex justify-between items-center">
                    管理面板
                    <el-button text @click="logout">
                        <el-icon><el-icon-switch-button /></el-icon>
                    </el-button>
                </div>
                <!-- Element Plus 导航菜单 -->
                <el-menu default-active="Overview" class="el-menu" @select="handleMenuSelect">
                    <el-menu-item index="Overview">
                        <el-icon><el-icon-data-line /></el-icon>
                        <span>概览</span>
                    </el-menu-item>
                    <el-menu-item index="UserManagement">
                        <el-icon><el-icon-user /></el-icon>
                        <span>用户管理</span>
                    </el-menu-item>
                    <el-menu-item index="DeviceManagement">
                        <el-icon><el-icon-monitor /></el-icon>
                        <span>设备管理</span>
                    </el-menu-item>
                    <el-menu-item index="ConfigurationManagement">
                        <el-icon><el-icon-setting /></el-icon>
                        <span>配置管理</span>
                    </el-menu-item>
                </el-menu>
            </aside>

            <!-- 主内容区 -->
            <main class="flex-grow p-8 overflow-auto">
                <!-- 动态渲染组件的区域 -->
                <component :is="activeComponent" class="p-6 bg-white rounded-lg shadow-md"></component>
            </main>
        </div>

        <!-- 登录组件 -->
        <Login v-else @login-success="handleLoginSuccess" />
    </div>

    <script>
        // Vue 3 应用的模块化代码
        const { createApp, ref, markRaw, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        // 定义 API 服务器基础地址
        const BASE_URL = 'http://127.0.0.1:8000';
        let authToken = null; // 用于存储登录后的 token

        // API 调用服务
        const apiService = {
            // 登录接口，对应 OpenAPI 文档中的 /api/v1/auth/login
            login: async (username, password) => {
                const response = await fetch(`${BASE_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                if (!response.ok) {
                    throw new Error('用户名或密码错误');
                }
                const data = await response.json();
                return data; // 返回包含 token 和用户信息的对象
            },
            // 通用的带认证的 fetch 方法，现在统一使用 Authorization Header
            fetchAuthenticated: async (url, options = {}) => {
                const headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${authToken}`,
                };
                const response = await fetch(url, { ...options, headers });
                if (!response.ok) {
                    throw new Error(`Failed to fetch: ${response.statusText}`);
                }
                return response.json();
            },
            // 获取用户列表，对应接口: /api/v1/admin/users
            getUsers: async () => {
                const response = await apiService.fetchAuthenticated(`${BASE_URL}/api/v1/admin/users`);
                return response.items; // 返回用户列表数组
            },
            // 获取设备列表，对应接口: /api/v1/webrtc/devices
            getDevices: async () => {
                // 修正：根据新的 API 响应结构，从 'data.devices' 提取数组
                const response = await apiService.fetchAuthenticated(`${BASE_URL}/api/v1/webrtc/devices`);
                // 检查响应结构，防止 data 或 devices 不存在
                if (response.data && Array.isArray(response.data.devices)) {
                    return response.data.devices;
                }
                // 如果数据结构不匹配，返回空数组
                return [];
            },
            // 获取活动任务，对应接口: /api/v1/staff/orders/active
            getActiveOrders: async () => {
                return await apiService.fetchAuthenticated(`${BASE_URL}/api/v1/staff/orders/active`);
            },
            // 获取 WebRTC 统计信息，对应接口: /api/v1/webrtc/stats
            getWebRTCStats: async () => {
                return await apiService.fetchAuthenticated(`${BASE_URL}/api/v1/webrtc/stats`);
            },
        };

        // 登录组件
        const Login = {
            template: `
                <div class="flex items-center justify-center flex-grow p-8">
                    <el-card class="w-96 p-6 rounded-lg shadow-xl">
                        <template #header>
                            <div class="text-center text-2xl font-bold">管理员登录</div>
                        </template>
                        <el-form :model="loginForm" label-position="top">
                            <el-form-item label="用户名">
                                <el-input v-model="loginForm.username" placeholder="请输入用户名" />
                            </el-form-item>
                            <el-form-item label="密码">
                                <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter="handleLogin" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" class="w-full" :loading="loading" @click="handleLogin">登录</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            `,
            setup(props, { emit }) {
                const loginForm = ref({
                    username: '',
                    password: ''
                });
                const loading = ref(false);

                const handleLogin = async () => {
                    loading.value = true;
                    try {
                        const response = await apiService.login(loginForm.value.username, loginForm.value.password);
                        ElMessage.success('登录成功！');
                        emit('login-success', response);
                    } catch (error) {
                        ElMessage.error(error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loginForm,
                    loading,
                    handleLogin
                };
            }
        };

        // 新增的概览组件
        const Overview = {
            template: `
                <div>
                    <h1 class="text-3xl font-bold mb-6 text-gray-800">系统概览</h1>
                    <p v-if="loading" class="text-gray-500 mb-4">正在加载系统数据...</p>
                    <template v-else>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <!-- 总用户数卡片 -->
                            <el-card class="rounded-xl shadow-md p-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 rounded-full p-3 mr-4">
                                        <el-icon :size="24"><el-icon-user-filled /></el-icon>
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold opacity-80">总用户数</div>
                                        <div class="text-3xl font-bold">{{ totalUsers }}</div>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 总设备数卡片 -->
                            <el-card class="rounded-xl shadow-md p-6 bg-gradient-to-r from-green-500 to-green-600 text-white">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 rounded-full p-3 mr-4">
                                        <el-icon :size="24"><el-icon-monitor /></el-icon>
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold opacity-80">总设备数</div>
                                        <div class="text-3xl font-bold">{{ totalDevices }}</div>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 在线设备数卡片 -->
                            <el-card class="rounded-xl shadow-md p-6 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 rounded-full p-3 mr-4">
                                        <el-icon :size="24"><el-icon-circle-check-filled /></el-icon>
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold opacity-80">在线设备数</div>
                                        <div class="text-3xl font-bold">{{ onlineDevicesCount }}</div>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 活动任务数卡片 -->
                            <el-card class="rounded-xl shadow-md p-6 bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 rounded-full p-3 mr-4">
                                        <el-icon :size="24"><el-icon-document-copy /></el-icon>
                                    </div>
                                    <div>
                                        <div class="text-sm font-semibold opacity-80">活动任务数</div>
                                        <div class="text-3xl font-bold">{{ activeOrdersCount }}</div>
                                    </div>
                                </div>
                            </el-card>
                        </div>

                        <!-- 详细监控信息 -->
                        <el-card class="box-card">
                            <template #header>
                                <span>详细监控信息</span>
                            </template>
                            <el-descriptions title="WebRTC 连接状态" border :column="2">
                                <el-descriptions-item label="WebRTC活跃连接">
                                    {{ webrtcStats.active_connections || 'N/A' }}
                                    <el-icon class="ml-2 text-purple-500"><el-icon-video-camera-filled /></el-icon>
                                </el-descriptions-item>
                                <el-descriptions-item label="数据传输速率">
                                    {{ webrtcStats.data_rate_kbps ? webrtcStats.data_rate_kbps + ' kbps' : 'N/A' }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-card>
                    </template>
                </div>
            `,
            setup() {
                const totalUsers = ref(0);
                const totalDevices = ref(0);
                const onlineDevicesCount = ref(0);
                const activeOrdersCount = ref(0);
                const webrtcStats = ref({});
                const loading = ref(true);

                const fetchData = async () => {
                    loading.value = true;
                    try {
                        const [usersData, devicesData, ordersData, statsData] = await Promise.all([
                            apiService.getUsers(),
                            apiService.getDevices(),
                            apiService.getActiveOrders(),
                            apiService.getWebRTCStats()
                        ]);
                        totalUsers.value = usersData.length;
                        totalDevices.value = devicesData.length;
                        onlineDevicesCount.value = devicesData.filter(d => d.status === 'online').length;
                        activeOrdersCount.value = ordersData.total;
                        webrtcStats.value = statsData;
                        ElMessage.success('系统概览数据加载成功！');
                    } catch (error) {
                        ElMessage.error(`加载系统概览数据失败: ${error.message}`);
                    } finally {
                        loading.value = false;
                    }
                };

                onMounted(fetchData);

                return {
                    totalUsers,
                    totalDevices,
                    onlineDevicesCount,
                    activeOrdersCount,
                    webrtcStats,
                    loading
                };
            }
        };

        // 1. 定义组件：用户管理模块
        const UserManagement = {
            template: `
                <div class="h-full">
                    <el-card class="box-card">
                        <template #header>
                            <span>用户管理</span>
                            <el-button style="float: right; padding: 3px 0" text @click="addUser">添加用户</el-button>
                        </template>
                        <p v-if="loading" class="text-gray-500 mb-4">正在加载用户数据...</p>
                        <template v-else>
                            <p class="text-gray-500 mb-4">此处展示用户列表，数据来源于 API 中的 /api/v1/admin/users 接口。</p>
                            <el-table :data="users" style="width: 100%">
                                <el-table-column prop="id" label="ID" width="200" />
                                <el-table-column prop="username" label="用户名" width="180" />
                                <el-table-column prop="role" label="角色" width="120" />
                                <el-table-column prop="is_active" label="状态">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                                            {{ scope.row.is_active ? '活跃' : '非活跃' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作">
                                    <template #default="scope">
                                        <el-button size="small">编辑</el-button>
                                        <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-card>
                </div>
            `,
            setup() {
                const users = ref([]);
                const loading = ref(true);

                const fetchUsers = async () => {
                    loading.value = true;
                    try {
                        const usersData = await apiService.getUsers();
                        users.value = usersData;
                        ElMessage.success('用户数据加载成功！');
                    } catch (error) {
                        ElMessage.error(`加载用户数据失败: ${error.message}`);
                    } finally {
                        loading.value = false;
                    }
                };

                onMounted(fetchUsers);

                const addUser = () => {
                    ElMessage.info('此操作将调用 /api/v1/auth/register 接口');
                };

                const deleteUser = (user) => {
                    ElMessage.info(`此操作将调用 API 删除用户: ${user.username}`);
                };

                return {
                    users,
                    loading,
                    addUser,
                    deleteUser
                };
            }
        };

        // 2. 定义组件：设备管理模块
        const DeviceManagement = {
            template: `
                <div>
                    <el-card class="box-card">
                        <template #header>
                            <span>设备管理</span>
                            <el-button style="float: right; padding: 3px 0" text @click="addDevice">添加设备</el-button>
                        </template>
                        <p v-if="loading" class="text-gray-500 mb-4">正在加载设备数据...</p>
                        <template v-else>
                            <p class="text-gray-500 mb-4">此处展示已注册的设备列表，数据来源于 API 中的 /api/v1/webrtc/devices 接口。</p>
                            <el-table :data="devices" style="width: 100%">
                                <el-table-column prop="id" label="设备ID" width="200" />
                                <el-table-column prop="name" label="设备名称" width="180" />
                                <el-table-column prop="status" label="状态" width="180">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.status === 'online' ? 'success' : 'danger'">
                                            {{ scope.row.status === 'online' ? '在线' : '离线' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="last_activity" label="最后活动时间" />
                                <el-table-column label="操作">
                                    <template #default="scope">
                                        <el-button size="small" type="danger" @click="deleteDevice(scope.row)">解绑</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-card>
                </div>
            `,
            setup() {
                const devices = ref([]);
                const loading = ref(true);

                const fetchDevices = async () => {
                    loading.value = true;
                    try {
                        devices.value = await apiService.getDevices();
                        ElMessage.success('设备数据加载成功！');
                    } catch (error) {
                        ElMessage.error(`加载设备数据失败: ${error.message}`);
                    } finally {
                        loading.value = false;
                    }
                };

                onMounted(fetchDevices);

                const addDevice = () => {
                    ElMessage.info('此操作将调用 /api/v1/webrtc/device/{device_id}/bind 接口');
                };

                const deleteDevice = (device) => {
                    ElMessage.info(`此操作将调用 /api/v1/webrtc/device/{device_id}/bind (DELETE) 解绑设备: ${device.id}`);
                };

                return {
                    devices,
                    loading,
                    addDevice,
                    deleteDevice
                };
            }
        };

        // 3. 定义组件：配置管理模块
        const ConfigurationManagement = {
            template: `
                <div>
                    <el-card class="box-card">
                        <template #header>
                            <span>配置管理</span>
                        </template>
                        <p class="text-gray-500 mb-4">此处进行系统级别的配置，如支付设置、通知设置等，这些配置最终会影响到 API 行为，例如 /api/v1/shop/payment/callback/{payment_method}。</p>
                        <el-form :model="form" label-width="120px">
                            <el-form-item label="支付方式">
                                <el-radio-group v-model="form.paymentMethod">
                                    <el-radio label="Alipay">支付宝</el-radio>
                                    <el-radio label="WechatPay">微信支付</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="通知服务">
                                <el-switch v-model="form.notificationEnabled" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="saveConfig">保存</el-button>
                                <el-button>取消</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            `,
            data() {
                return {
                    form: {
                        paymentMethod: 'Alipay',
                        notificationEnabled: true
                    }
                };
            },
            methods: {
                saveConfig() {
                    ElMessage.info('此操作将调用相应的 API 来保存配置。');
                }
            }
        };

        // 创建 Vue 应用实例
        const app = createApp({
            setup() {
                const activeComponent = ref(markRaw(Overview));
                const isLoggedIn = ref(false);
                const user = ref(null);

                const handleMenuSelect = (index) => {
                    switch (index) {
                        case 'Overview':
                            activeComponent.value = markRaw(Overview);
                            break;
                        case 'UserManagement':
                            activeComponent.value = markRaw(UserManagement);
                            break;
                        case 'DeviceManagement':
                            activeComponent.value = markRaw(DeviceManagement);
                            break;
                        case 'ConfigurationManagement':
                            activeComponent.value = markRaw(ConfigurationManagement);
                            break;
                    }
                };

                const handleLoginSuccess = (loginResponse) => {
                    // 存储 token 和用户信息
                    authToken = loginResponse.access_token;
                    user.value = loginResponse;
                    isLoggedIn.value = true;
                };

                const logout = () => {
                    authToken = null;
                    user.value = null;
                    isLoggedIn.value = false;
                    ElMessage.info('已退出登录');
                };

                return {
                    activeComponent,
                    handleMenuSelect,
                    isLoggedIn,
                    user,
                    logout,
                    handleLoginSuccess
                };
            }
        });

        // 5. 全局注册组件
        app.component('Login', Login);
        app.component('Overview', Overview);
        app.component('UserManagement', UserManagement);
        app.component('DeviceManagement', DeviceManagement);
        app.component('ConfigurationManagement', ConfigurationManagement);

        // 6. 注册 Element Plus 的所有组件和图标
        app.use(ElementPlus);
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(`ElIcon${key}`, component);
        }

        // 7. 将应用挂载到 DOM
        app.mount('#app');
    </script>
</body>
</html>
