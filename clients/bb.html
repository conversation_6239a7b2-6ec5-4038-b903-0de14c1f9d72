<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备端 - WebRTC 视频通话</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            text-align: center;
            opacity: 0.8;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        button:disabled {
            background: rgba(255,255,255,0.3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        .btn-danger {
            background: linear-gradient(45deg, #e17055, #d63031);
        }
        .btn-warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .log {
            background: rgba(0, 0, 0, 0.7);
            color: #0f0;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .error { color: #ff6b6b; }
        .success { color: #00b894; }
        .info { color: #74b9ff; }
        .warning { color: #fdcb6e; }

        input, select {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
        }
        .status.connected {
            background: linear-gradient(45deg, #00b894, #00cec9);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }
        .status.disconnected {
            background: linear-gradient(45deg, #e17055, #d63031);
            box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
        }
        .status.calling {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
            animation: pulse 2s infinite;
        }
        .status.incall {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .video-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }
        .video-row {
            display: flex;
            gap: 15px;
        }
        .video-box {
            flex: 1;
            position: relative;
            background: #000000; /* 改为纯黑色背景 */
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.2);
            min-height: 200px;
        }
        .video-box video {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 13px;
            background: #000000; /* 视频元素也设置黑色背景 */
        }
        .video-box.no-stream {
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }
        .video-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .call-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .device-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .device-info .info-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }

        .icon {
            font-size: 1.2em;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能设备端</h1>
        <p class="subtitle">WebRTC 视频通话系统 - 设备控制台</p>

        <div class="device-info">
             <div class="info-item">
                <span>🏷️ 设备类型:</span>
                <span>Orange Pi 智能摄像头</span>
            </div>
            <div class="info-item">
                <span>📡 连接状态:</span>
                <span id="networkStatus">离线</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h3><span class="icon">🔧</span>设备配置</h3>
            <div>
                <label>设备ID:</label>
                <input type="text" id="deviceId" value="smart_camera_001" placeholder="输入设备唯一标识">
            </div>
            <button onclick="connectDevice()" id="deviceConnectBtn" class="btn-warning">🔗 连接服务器</button>
            <button onclick="disconnectDevice()" id="deviceDisconnectBtn" disabled class="btn-danger">❌ 断开连接</button>
        </div>

        <div class="status disconnected" id="deviceStatus">
            🔴 设备离线
        </div>

        <div class="section">
            <h3><span class="icon">📹</span>视频监控</h3>
            <div class="video-container">
                <div class="video-row">
                    <div class="video-box">
                        <div class="video-label">📹 本地摄像头</div>
                        <video id="deviceLocalVideo" autoplay muted playsinline></video>
                    </div>
                    <div class="video-box">
                        <div class="video-label">📱 远程用户</div>
                        <video id="deviceRemoteVideo" autoplay playsinline></video>
                    </div>
                </div>
            </div>

            <div class="call-controls">
                <button onclick="startDeviceCamera()" id="deviceStartCameraBtn" disabled class="btn-success">
                    📹 启动摄像头
                </button>
                <button onclick="hangupDevice()" id="deviceHangupBtn" disabled class="btn-danger">
                    📞 挂断通话
                </button>
            </div>
        </div>

        <div class="section">
            <h3><span class="icon">📋</span>设备日志</h3>
            <div class="log" id="deviceLog">
                <div class="info">[系统] 设备控制台已启动</div>
            </div>
            <button onclick="clearDeviceLog()">🗑️ 清空日志</button>
        </div>
    </div>

    <script>
        // WebRTC配置
        const ICE_SERVERS = [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:*************:3478' },
            {
                urls: 'turn:*************:3478',
                username: 'username1',
                credential: 'password1'
            }
        ];

        // 全局变量
        let deviceSocket = null;
        let deviceConnected = false;
        let devicePeerConnection = null;
        let deviceLocalStream = null;
        let deviceToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOiJzbWFydF9jYW1lcmFfMDAxIiwidHlwZSI6ImRldmljZSIsImV4cCI6MTc4NTM4ODg3OH0.WwAXKY2S1hihXFFi0pi2dMeUMQZ6JYo7CnSWOkBsHZo";
        let currentDeviceId = "smart_camera_001";

        // 设备状态
        let deviceCallState = 'idle'; // idle, ringing, incall


        function addLog(message, type = 'info') {
            const log = document.getElementById('deviceLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = type;
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(message, status) {
            const statusElement = document.getElementById('deviceStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${status}`;
            const networkStatus = document.getElementById('networkStatus');
            networkStatus.textContent = status === 'connected' ? '在线' : '离线';
        }

        function updateDeviceButtons() {
            const connected = deviceConnected;
            const hasMedia = deviceLocalStream !== null;
            const inCall = deviceCallState === 'incall';
            const ringing = deviceCallState === 'ringing';

            document.getElementById('deviceConnectBtn').disabled = connected;
            document.getElementById('deviceDisconnectBtn').disabled = !connected;
            document.getElementById('deviceStartCameraBtn').disabled = !connected || hasMedia;
            document.getElementById('deviceHangupBtn').disabled = !ringing && !inCall;
        }

        function connectDevice() {
            currentDeviceId = document.getElementById('deviceId').value.trim();
            if (!currentDeviceId) {
                addLog('请输入设备ID', 'error');
                return;
            }

            // A hardcoded token is used for simplicity in this example.
            // In a real application, you should fetch this securely after registration.
            if (!deviceToken) {
                 addLog('设备Token未设置，请先在代码中提供一个有效的Token', 'error');
                 return;
            }

            // 🔒 安全修复：使用WebSocket子协议传递token，而不是URL参数
            const wsUrl = `ws://*************:8000/api/v1/webrtc/connect?participant_type=device&target_id=testuser&connection_type=video&features=messaging`;

            addLog('连接到服务器...', 'info');
            updateStatus('🟡 正在连接...', 'calling');

            // 使用子协议传递token，更安全
            deviceSocket = new WebSocket(wsUrl, [`Bearer.${deviceToken}`]);

            deviceSocket.onopen = function(event) {
                deviceConnected = true;
                addLog('✅ 服务器连接成功', 'success');
                updateStatus('🟢 设备在线', 'connected');
                updateDeviceButtons();
                initDeviceWebRTC();
            };

            deviceSocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleDeviceMessage(data);
            };

            deviceSocket.onclose = function(event) {
                deviceConnected = false;
                addLog(`🔌 连接断开 (代码: ${event.code})`, 'warning');
                updateStatus('🔴 设备离线', 'disconnected');
                updateDeviceButtons();
                cleanupDeviceWebRTC();
            };

            deviceSocket.onerror = function(error) {
                addLog(`❌ 连接错误`, 'error');
                updateStatus('🔴 连接错误', 'disconnected');
                updateDeviceButtons();
            };
        }

        function disconnectDevice() {
            if (deviceSocket) {
                deviceSocket.close();
            }
        }

        function initDeviceWebRTC() {
            devicePeerConnection = new RTCPeerConnection({ iceServers: ICE_SERVERS });

            devicePeerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    sendDeviceMessage({
                        type: 'candidate',
                        payload: {
                            candidate: event.candidate.candidate,
                            sdpMLineIndex: event.candidate.sdpMLineIndex,
                            sdpMid: event.candidate.sdpMid
                        }
                    });
                }
            };

            devicePeerConnection.ontrack = (event) => {
                addLog('📺 收到远程视频流', 'success');
                const remoteVideo = document.getElementById('deviceRemoteVideo');
                if (event.streams && event.streams[0]) {
                    remoteVideo.srcObject = event.streams[0];
                }
            };

            devicePeerConnection.onconnectionstatechange = () => {
                const state = devicePeerConnection.connectionState;
                addLog(`🔗 连接状态: ${state}`, 'info');

                if (state === 'connected') {
                    deviceCallState = 'incall';
                    updateStatus('📞 通话中', 'incall');
                } else if (state === 'disconnected' || state === 'failed') {
                    hangupDevice();
                }
                updateDeviceButtons();
            };
        }

        function cleanupDeviceWebRTC() {
            if (devicePeerConnection) {
                devicePeerConnection.close();
                devicePeerConnection = null;
            }
            if (deviceLocalStream) {
                deviceLocalStream.getTracks().forEach(track => track.stop());
                deviceLocalStream = null;
            }
            document.getElementById('deviceLocalVideo').srcObject = null;
            document.getElementById('deviceRemoteVideo').srcObject = null;
            deviceCallState = 'idle';
            updateDeviceButtons();
        }

        async function startDeviceCamera() {
            try {
                addLog('📹 启动摄像头...', 'info');
                deviceLocalStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480, frameRate: 30 },
                    audio: true
                });

                document.getElementById('deviceLocalVideo').srcObject = deviceLocalStream;

                if (devicePeerConnection) {
                    deviceLocalStream.getTracks().forEach(track => {
                        devicePeerConnection.addTrack(track, deviceLocalStream);
                    });
                }
                addLog('✅ 摄像头启动成功', 'success');
                updateDeviceButtons();
            } catch (error) {
                addLog(`❌ 摄像头启动失败: ${error.message}`, 'error');
            }
        }

        function hangupDevice() {
            addLog('📞 主动挂断通话', 'info');
            sendDeviceMessage({ type: 'hangup', payload: {} });
            deviceCallState = 'idle';
            updateStatus('🟢 设备在线', 'connected');
            if (devicePeerConnection) {
                devicePeerConnection.close();
                devicePeerConnection = null;
            }
            document.getElementById('deviceRemoteVideo').srcObject = null;
            updateDeviceButtons();
        }

        function handleDeviceMessage(data) {
            const messageType = data.type;
            addLog(`📨 收到: ${messageType}`, 'success');
            if (messageType === 'offer') {
                handleDeviceOffer(data.payload);
            } else if (messageType === 'candidate') {
                handleDeviceCandidate(data.payload);
            } else if (messageType === 'hangup') {
                handleDeviceHangup();
            }
        }

        async function handleDeviceOffer(payload) {
            try {
                addLog('📥 收到通话邀请 (Offer)', 'warning');
                deviceCallState = 'ringing';
                updateStatus('📞 来电中...', 'calling');
                updateDeviceButtons();

                if (!deviceLocalStream) {
                    addLog('⚠️ 本地摄像头未启动，将自动启动...', 'warning');
                    await startDeviceCamera();
                    if(!deviceLocalStream) {
                        addLog('❌ 无法自动启动摄像头，无法接听电话。', 'error');
                        return;
                    }
                }

                // === 绿屏修复：检查并优化收到的SDP ===
                addLog('🎬 正在分析收到的Offer SDP...', 'info');
                payload.sdp = prioritizeH264InSdp(payload.sdp);
                // ===================================

                await devicePeerConnection.setRemoteDescription(new RTCSessionDescription(payload));

                const answer = await devicePeerConnection.createAnswer();

                // === 绿屏修复：再次优化自己的Answer SDP ===
                addLog('🎬 正在优化要发送的Answer SDP...', 'info');
                answer.sdp = prioritizeH264InSdp(answer.sdp);
                // =======================================

                await devicePeerConnection.setLocalDescription(answer);

                sendDeviceMessage({
                    type: 'answer',
                    payload: { type: 'answer', sdp: answer.sdp }
                });

                addLog('✅ 自动接听，Answer已发送', 'success');
                deviceCallState = 'incall';
                updateStatus('📞 通话中', 'incall');
                updateDeviceButtons();

            } catch (error) {
                addLog(`❌ 处理Offer失败: ${error.message}`, 'error');
            }
        }

        async function handleDeviceCandidate(payload) {
            try {
                await devicePeerConnection.addIceCandidate(new RTCIceCandidate(payload));
            } catch (error) {
                addLog(`❌ 添加ICE候选失败: ${error.message}`, 'error');
            }
        }

        function handleDeviceHangup() {
            addLog('📞 对方挂断了通话', 'warning');
            hangupDevice();
        }

        function sendDeviceMessage(message) {
            if (deviceSocket && deviceConnected) {
                deviceSocket.send(JSON.stringify(message));
            }
        }

        function clearDeviceLog() {
            document.getElementById('deviceLog').innerHTML = '<div class="info">[系统] 日志已清空</div>';
        }

        // === 绿屏修复：通用的SDP H.264优化函数 ===
        /**
         * 优化SDP，将H.264作为首选视频编解码器
         * @param {string} sdp - 原始SDP
         * @returns {string} - 修改后的SDP
         */
        function prioritizeH264InSdp(sdp) {
            let sdpLines = sdp.split('\r\n');
            let h264PayloadType = null;

            // 查找H.264的payload type
            for (const line of sdpLines) {
                if (line.includes('H264/90000')) {
                    const match = line.match(/a=rtpmap:(\d+) H264\/90000/);
                    if (match) {
                        h264PayloadType = match[1];
                        break;
                    }
                }
            }

            if (!h264PayloadType) {
                addLog('⚠️ 在SDP中未找到H.264，无法优化。', 'warning');
                return sdp;
            }

            // 将H.264移动到m=video行的最前面
            const mVideoLineIndex = sdpLines.findIndex(line => line.startsWith('m=video'));
            if (mVideoLineIndex > -1) {
                let mLine = sdpLines[mVideoLineIndex].split(' ');
                let payloads = mLine.slice(3);
                // 如果H264存在但不在第一位，则移动它
                if (payloads.includes(h264PayloadType) && payloads.indexOf(h264PayloadType) > 0) {
                    payloads = payloads.filter(p => p !== h264PayloadType);
                    payloads.unshift(h264PayloadType);
                    mLine = [...mLine.slice(0, 3), ...payloads];
                    sdpLines[mVideoLineIndex] = mLine.join(' ');
                    addLog('🎬 SDP已优化，H.264被设为首选视频编解码器。', 'success');
                }
            }

            return sdpLines.join('\r\n');
        }

        updateDeviceButtons();
    </script>
</body>
</html>