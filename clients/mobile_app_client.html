<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能家居 App - 视频通话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px 20px 10px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .card h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .user-info h4 {
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .user-info p {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            opacity: 0.9;
        }

        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }

        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 8px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #00b894, #00cec9);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            color: white;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            margin: 15px 0;
        }

        .status-indicator.connected {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }

        .status-indicator.disconnected {
            background: linear-gradient(45deg, #e17055, #d63031);
        }

        .status-indicator.calling {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            animation: pulse 2s infinite;
        }

        .status-indicator.incall {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .video-call-container {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.5);
            margin: 20px 0;
        }

        .main-video {
            width: 100%;
            height: 250px;
            object-fit: contain; /* 改为 contain 避免裁剪 */
            background: #000000; /* 设置黑色背景 */
            border-radius: 15px;
            /* 强制硬件加速和渲染优化 */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* 禁用可能导致绿屏的滤镜 */
            filter: none;
            -webkit-filter: none;
            /* 确保正确的颜色空间 */
            image-rendering: auto;
            -webkit-image-rendering: auto;
        }

        .pip-video {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 80px;
            height: 120px;
            border-radius: 10px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.5);
            background: rgba(0, 0, 0, 0.7);
        }

        .video-controls {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn.video {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .control-btn.audio {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .control-btn.hangup {
            background: #e17055;
            color: white;
        }

        .device-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .device-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .device-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .device-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00b894, #00cec9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .device-details h5 {
            margin-bottom: 3px;
            font-size: 1em;
        }

        .device-details p {
            font-size: 0.8em;
            opacity: 0.8;
        }

        .call-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            font-size: 12px;
            cursor: pointer;
        }

        .log-container {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 10px;
            font-family: monospace;
            font-size: 11px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-entry.success { color: #00b894; }
        .log-entry.error { color: #e17055; }
        .log-entry.warning { color: #fdcb6e; }
        .log-entry.info { color: #74b9ff; }

        .bottom-nav {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-around;
            backdrop-filter: blur(10px);
        }

        .nav-item {
            text-align: center;
            cursor: pointer;
            padding: 5px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-item .icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-item .label {
            font-size: 10px;
            opacity: 0.8;
        }

        /* 设备管理样式 */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header h3 {
            margin: 0;
            color: white;
            font-size: 18px;
        }

        .device-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 15px;
        }

        .device-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .device-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .device-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .device-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .device-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .device-details h4 {
            margin: 0 0 4px 0;
            color: white;
            font-size: 14px;
        }

        .device-details p {
            margin: 0;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .device-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e17055;
        }

        .status-dot.online {
            background: #00b894;
            box-shadow: 0 0 8px rgba(0, 184, 148, 0.5);
        }

        .device-actions-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            padding: 6px 10px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-actions-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .device-stats {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        .stat-item span {
            color: white;
            font-weight: bold;
        }

        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 通话模式选择样式 */
        .call-mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 4px;
        }

        .mode-btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .mode-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        /* 语音通话界面样式 */
        .audio-call-interface {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 20px 0;
        }

        .audio-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin: 0 auto 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .audio-info h3 {
            color: white;
            margin: 0 0 5px 0;
            font-size: 20px;
        }

        .audio-info p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 20px 0;
            font-size: 14px;
        }

        .audio-visualizer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3px;
            height: 40px;
            margin: 20px 0;
        }

        .audio-bar {
            width: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 2px;
            animation: audioWave 1.5s ease-in-out infinite;
        }

        .audio-bar:nth-child(1) { height: 10px; animation-delay: 0s; }
        .audio-bar:nth-child(2) { height: 20px; animation-delay: 0.1s; }
        .audio-bar:nth-child(3) { height: 30px; animation-delay: 0.2s; }
        .audio-bar:nth-child(4) { height: 25px; animation-delay: 0.3s; }
        .audio-bar:nth-child(5) { height: 15px; animation-delay: 0.4s; }
        .audio-bar:nth-child(6) { height: 35px; animation-delay: 0.5s; }
        .audio-bar:nth-child(7) { height: 20px; animation-delay: 0.6s; }

        @keyframes audioWave {
            0%, 100% { transform: scaleY(1); opacity: 0.6; }
            50% { transform: scaleY(1.5); opacity: 1; }
        }

        .call-duration {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        }

        .network-quality {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin: 10px 0;
        }

        .quality-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00b894;
        }

        .quality-indicator.poor { background: #e17055; }
        .quality-indicator.fair { background: #fdcb6e; }
        .quality-indicator.good { background: #00b894; }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>📱 智能家居</h1>
            <p class="subtitle">视频通话 & 设备监控</p>
        </div>

        <div class="main-content">
            <!-- 用户信息卡片 -->
            <div class="card">
                <div class="user-profile">
                    <div class="avatar" id="userAvatar">👤</div>
                    <div class="user-info">
                        <h4 id="userName">访客用户</h4>
                        <p id="userStatus">未登录</p>
                    </div>
                </div>

                <div class="input-group">
                    <label>用户名</label>
                    <input type="text" id="username" value="testuser" placeholder="输入用户名">
                </div>

                <div class="input-group">
                    <label>密码</label>
                    <input type="password" id="password" value="123456" placeholder="输入密码">
                </div>

                <button class="btn btn-primary" onclick="userLogin()" id="loginBtn">
                    🔐 登录账户
                </button>
            </div>

            <!-- 连接状态 -->
            <div class="status-indicator disconnected" id="connectionStatus">
                🔴 未连接服务器
            </div>

            <!-- 设备管理卡片 -->
            <div class="card" id="deviceManagementCard" style="display: none;">
                <div class="card-header">
                    <h3>📱 我的设备</h3>
                    <div class="device-actions">
                        <button class="btn btn-small" onclick="refreshDeviceList()">🔄 刷新</button>
                        <button class="btn btn-small" onclick="showAddDeviceDialog()">➕ 添加设备</button>
                    </div>
                </div>

                <div class="device-list" id="deviceList">
                    <div class="loading-indicator" id="deviceLoading">
                        <div class="spinner"></div>
                        <p>加载设备列表...</p>
                    </div>
                </div>

                <div class="device-stats" id="deviceStats">
                    <span class="stat-item">总设备: <span id="totalDevices">0</span></span>
                    <span class="stat-item">在线: <span id="onlineDevices">0</span></span>
                </div>
            </div>

            <!-- 视频通话区域 -->
            <div class="card">
                <h3>📹 视频通话</h3>
                <div class="video-call-container">
                    <video id="remoteVideo" class="main-video" autoplay playsinline muted></video>
                    <video id="localVideo" class="pip-video" autoplay muted playsinline></video>

                    <div class="video-controls">
                        <button class="control-btn video" onclick="toggleVideo()" id="videoBtn">
                            📹
                        </button>
                        <button class="control-btn audio" onclick="toggleAudio()" id="audioBtn">
                            🎤
                        </button>
                        <button class="control-btn hangup" onclick="hangupCall()" id="hangupBtn" disabled>
                            📞
                        </button>
                    </div>
                </div>

                <!-- 通话模式选择 -->
                <div class="call-mode-selector">
                    <button class="mode-btn active" onclick="setCallMode('video')" id="videoModeBtn">
                        📹 视频通话
                    </button>
                    <button class="mode-btn" onclick="setCallMode('audio')" id="audioModeBtn">
                        🎤 语音通话
                    </button>
                </div>

                <button class="btn btn-success" onclick="startCamera()" id="startCameraBtn" disabled>
                    📹 启动摄像头
                </button>
                <button class="btn btn-info" onclick="simulateDeviceResponse()" id="testBtn" style="margin-left: 10px;">
                    🤖 测试视频显示
                </button>
                <button class="btn btn-warning" onclick="diagnoseVideoIssues()" id="diagnoseBtn" style="margin-left: 10px;">
                    🔍 诊断视频问题
                </button>
                <button class="btn btn-danger" onclick="fixGreenScreen()" id="fixGreenBtn" style="margin-left: 10px;">
                    🟢 修复绿屏
                </button>
                <button class="btn btn-secondary" onclick="resetVideoCounters()" id="resetBtn" style="margin-left: 10px;">
                    🔄 重置计数器
                </button>
                <button class="btn btn-primary" onclick="forceCodecRefresh()" id="codecBtn" style="margin-left: 10px;">
                    🎬 强制编解码器刷新
                </button>
                <button class="btn btn-info" onclick="detectDevices()" id="detectBtn" style="margin-left: 10px;">
                    🔍 检测设备
                </button>
                <button class="btn btn-warning" onclick="disableHardwareAcceleration()" id="disableHwBtn" style="margin-left: 10px;">
                    🚫 禁用硬件加速
                </button>
                <button class="btn btn-danger" onclick="forceBasicH264()" id="basicH264Btn" style="margin-left: 10px;">
                    🎬 强制基础H.264
                </button>
                <button class="btn btn-success" onclick="tryWebCodecsDecoding()" id="webCodecsBtn" style="margin-left: 10px;">
                    🔬 WebCodecs解码
                </button>
            </div>

            <!-- 设备管理卡片 -->
            <div class="card" id="deviceManagementCard">
                <div class="card-header">
                    <h3>📱 我的设备</h3>
                    <div class="device-actions">
                        <button class="btn btn-small" onclick="refreshDeviceList()">🔄 刷新</button>
                        <button class="btn btn-small" onclick="showAddDeviceDialog()">➕ 添加设备</button>
                    </div>
                </div>

                <div class="device-list" id="deviceList">
                    <div class="loading-indicator" id="deviceLoading">
                        <div class="spinner"></div>
                        <p>加载设备列表...</p>
                    </div>
                </div>

                <div class="device-stats" id="deviceStats">
                    <span class="stat-item">总设备: <span id="totalDevices">0</span></span>
                    <span class="stat-item">在线: <span id="onlineDevices">0</span></span>
                </div>
            </div>

            <!-- 通话日志 -->
            <div class="card">
                <h3>📋 通话日志</h3>
                <div class="log-container" id="callLog">
                    <div class="log-entry info">[系统] 智能家居App已启动</div>
                    <div class="log-entry info">[系统] 等待用户登录...</div>
                </div>
                <button class="btn btn-warning" onclick="clearLog()" style="margin-top: 10px; font-size: 14px; padding: 8px;">
                    🗑️ 清空日志
                </button>
            </div>
        </div>

        <div class="bottom-nav">
            <div class="nav-item active">
                <div class="icon">🏠</div>
                <div class="label">首页</div>
            </div>
            <div class="nav-item">
                <div class="icon">📹</div>
                <div class="label">通话</div>
            </div>
            <div class="nav-item">
                <div class="icon">⚙️</div>
                <div class="label">设置</div>
            </div>
            <div class="nav-item">
                <div class="icon">👤</div>
                <div class="label">我的</div>
            </div>
        </div>
    </div>

    <script>
        // 服务器配置 - 根据实际部署环境修改
        const SERVER_CONFIG = {
            // HTTP API 基础URL
            API_BASE_URL: 'http://*************:8000',
            // WebSocket 基础URL
            WS_BASE_URL: 'ws://*************:8000'
        };

        // WebRTC配置
        const ICE_SERVERS = [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:*************:3478' },
            {
                urls: 'turn:*************:3478',
                username: 'username1',
                credential: 'password1'
            }
        ];

        // 全局变量
        let userSocket = null;
        let userConnected = false;
        let userPeerConnection = null;
        let userLocalStream = null;
        let userToken = null;
        let userDevices = [];
        let deviceStatusInterval = null;
        let currentCallMode = 'video'; // 'video' 或 'audio'
        let callStartTime = null;
        let callDurationInterval = null;

        // 通话状态
        let callState = 'idle'; // idle, calling, incall
        let videoEnabled = true;
        let audioEnabled = true;
        let currentTargetDevice = 'smart_camera_001';

        function addLog(message, type = 'info') {
            const log = document.getElementById('callLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function updateConnectionStatus(message, status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = message;
            statusElement.className = `status-indicator ${status}`;
        }

        function updateUserInfo(username, status) {
            document.getElementById('userName').textContent = username || '访客用户';
            document.getElementById('userStatus').textContent = status || '未登录';

            if (username) {
                document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
            }
        }

        function updateButtons() {
            const loggedIn = userToken !== null;
            const connected = userConnected;
            const hasMedia = userLocalStream !== null;
            const inCall = callState === 'incall';
            const calling = callState === 'calling';

            // 安全地更新按钮状态
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) loginBtn.disabled = loggedIn;

            const startCameraBtn = document.getElementById('startCameraBtn');
            if (startCameraBtn) startCameraBtn.disabled = !connected || hasMedia;

            const hangupBtn = document.getElementById('hangupBtn');
            if (hangupBtn) hangupBtn.disabled = !calling && !inCall;

            // 更新设备列表中的呼叫按钮状态
            updateDeviceCallButtons(!connected || !hasMedia || calling || inCall);
        }

        function updateDeviceCallButtons(disabled) {
            // 更新设备列表中所有呼叫按钮的状态
            const deviceCallButtons = document.querySelectorAll('.device-actions-btn');
            deviceCallButtons.forEach(button => {
                if (button.textContent.includes('呼叫')) {
                    button.disabled = disabled;
                }
            });
        }

        // 用户登录
        async function userLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                addLog('请输入用户名和密码', 'error');
                return;
            }

            try {
                addLog('正在登录...', 'info');
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    userToken = data.access_token;
                    addLog(`✅ 登录成功，欢迎 ${username}`, 'success');
                    updateUserInfo(username, '已登录');
                    updateButtons();
                    connectToServer();

                    // 自动显示设备管理卡片并加载设备列表
                    document.getElementById('deviceManagementCard').style.display = 'block';
                    setTimeout(() => loadDeviceList(), 1000); // 延迟1秒等待连接建立
                } else {
                    addLog(`❌ 登录失败: ${data.detail}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 登录异常: ${error.message}`, 'error');
            }
        }

        // 连接服务器
        function connectToServer() {
            if (!userToken) {
                addLog('请先登录', 'error');
                return;
            }

            // 🔒 安全修复：使用WebSocket子协议传递token，而不是URL参数
            const wsUrl = `${SERVER_CONFIG.WS_BASE_URL}/api/v1/webrtc/connect?participant_type=user&target_id=${currentTargetDevice}&connection_type=video&features=messaging`;

            addLog('连接到服务器...', 'info');
            updateConnectionStatus('🟡 正在连接...', 'calling');

            // 使用子协议传递token，更安全
            userSocket = new WebSocket(wsUrl, [`Bearer.${userToken}`]);

            userSocket.onopen = function(event) {
                userConnected = true;
                addLog('✅ 服务器连接成功', 'success');
                updateConnectionStatus('🟢 已连接服务器', 'connected');
                updateButtons();
                initUserWebRTC();
            };

            userSocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleUserMessage(data);
            };

            userSocket.onclose = function(event) {
                userConnected = false;
                if (event.code === 1000) {
                    addLog(`📞 通话已结束 (${event.code})`, 'info');
                } else {
                    addLog(`🔌 连接断开 (${event.code})`, 'warning');
                }
                updateConnectionStatus('🔴 连接已断开', 'disconnected');
                updateButtons();
                cleanupUserWebRTC();
            };

            userSocket.onerror = function(error) {
                addLog(`❌ 连接错误`, 'error');
                updateConnectionStatus('🔴 连接错误', 'disconnected');
                updateButtons();
            };
        }

        // WebRTC初始化
        function initUserWebRTC() {
            userPeerConnection = new RTCPeerConnection({ iceServers: ICE_SERVERS });

            // 连接状态监控
            userPeerConnection.onconnectionstatechange = () => {
                addLog(`🔗 连接状态: ${userPeerConnection.connectionState}`, 'info');
            };

            userPeerConnection.oniceconnectionstatechange = () => {
                addLog(`🧊 ICE连接状态: ${userPeerConnection.iceConnectionState}`, 'info');
            };

            userPeerConnection.onicegatheringstatechange = () => {
                addLog(`🧊 ICE收集状态: ${userPeerConnection.iceGatheringState}`, 'info');
            };

            userPeerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    addLog('🧊 发送ICE候选', 'info');
                    sendUserMessage({
                        type: 'candidate',
                        payload: {
                            candidate: event.candidate.candidate,
                            sdpMLineIndex: event.candidate.sdpMLineIndex,
                            sdpMid: event.candidate.sdpMid
                        }
                    });
                } else {
                    addLog('🧊 ICE候选收集完成', 'info');
                }
            };

            userPeerConnection.ontrack = (event) => {
                addLog('📺 收到设备视频流', 'success');
                const remoteVideo = document.getElementById('remoteVideo');
                const stream = event.streams[0];

                if (stream && stream.getTracks().length > 0) {
                    // 清除任何现有的 poster 属性
                    remoteVideo.removeAttribute('poster');

                    // 设置流对象
                    remoteVideo.srcObject = stream;
                    addLog(`📺 设备流包含 ${stream.getVideoTracks().length} 个视频轨道和 ${stream.getAudioTracks().length} 个音频轨道`, 'info');

                    // 详细的轨道信息和健康检查
                    stream.getTracks().forEach((track, index) => {
                        addLog(`📺 轨道 ${index + 1}: ${track.kind} - ${track.enabled ? '启用' : '禁用'} - ${track.readyState}`, 'info');

                        // 检查视频轨道的详细信息
                        if (track.kind === 'video') {
                            const settings = track.getSettings();
                            addLog(`📺 视频设置: ${settings.width || '未知'}x${settings.height || '未知'} @${settings.frameRate || '未知'}fps`, 'info');

                            // 监听轨道状态变化
                            track.onended = () => {
                                addLog('⚠️ 视频轨道已结束', 'warning');
                            };

                            track.onmute = () => {
                                addLog('⚠️ 视频轨道被静音', 'warning');
                            };

                            track.onunmute = () => {
                                addLog('✅ 视频轨道恢复', 'success');
                            };
                        }
                    });

                    // 增强的视频元素事件监听
                    remoteVideo.onloadstart = () => {
                        addLog('📺 开始加载视频数据', 'info');
                    };

                    remoteVideo.onloadedmetadata = () => {
                        addLog('📺 设备视频元数据已加载', 'success');
                        addLog(`📺 视频尺寸: ${remoteVideo.videoWidth}x${remoteVideo.videoHeight}`, 'info');

                        // 检查视频尺寸是否有效
                        if (remoteVideo.videoWidth === 0 || remoteVideo.videoHeight === 0) {
                            addLog('⚠️ 视频尺寸无效，可能是绿屏问题', 'warning');
                        }
                        // 移除自动刷新，避免无限循环
                    };

                    remoteVideo.oncanplay = () => {
                        addLog('📺 视频可以开始播放', 'info');
                    };

                    remoteVideo.onplay = () => {
                        addLog('📺 设备视频开始播放', 'success');
                    };

                    remoteVideo.onplaying = () => {
                        addLog('📺 视频正在播放中', 'success');

                        // 延迟检测绿屏问题
                        setTimeout(() => {
                            detectAndFixGreenScreen(remoteVideo);
                        }, 2000);
                    };

                    remoteVideo.onerror = (error) => {
                        addLog(`❌ 设备视频播放错误: ${error.target.error?.message || error}`, 'error');

                        // 尝试重新设置流
                        setTimeout(() => {
                            addLog('🔄 尝试重新设置视频流', 'info');
                            remoteVideo.srcObject = null;
                            remoteVideo.srcObject = stream;
                        }, 1000);
                    };

                    remoteVideo.onstalled = () => {
                        addLog('⚠️ 视频播放停滞', 'warning');
                    };

                    remoteVideo.onwaiting = () => {
                        addLog('⏳ 视频缓冲中', 'info');
                    };

                    // 强制播放并处理各种错误
                    remoteVideo.play().then(() => {
                        addLog('✅ 视频自动播放成功', 'success');
                    }).catch(e => {
                        addLog(`⚠️ 自动播放失败: ${e.message}`, 'warning');
                        addLog('💡 请手动点击视频区域开始播放', 'info');

                        // 添加点击播放功能
                        remoteVideo.onclick = () => {
                            remoteVideo.play().then(() => {
                                addLog('✅ 手动播放成功', 'success');
                                remoteVideo.onclick = null; // 移除点击事件
                            }).catch(err => {
                                addLog(`❌ 手动播放失败: ${err.message}`, 'error');
                            });
                        };
                    });
                } else {
                    addLog('⚠️ 收到空的设备流', 'warning');
                }
            };

            userPeerConnection.onconnectionstatechange = () => {
                const state = userPeerConnection.connectionState;
                addLog(`🔗 连接状态: ${state}`, 'info');

                if (state === 'connected') {
                    callState = 'incall';
                    updateConnectionStatus('📞 通话中', 'incall');
                    updateButtons();
                } else if (state === 'disconnected' || state === 'failed') {
                    hangupCall();
                }
            };

            userPeerConnection.oniceconnectionstatechange = () => {
                addLog(`🧊 ICE状态: ${userPeerConnection.iceConnectionState}`, 'info');
            };

            updateButtons();
        }

        function cleanupUserWebRTC() {
            if (userPeerConnection) {
                userPeerConnection.close();
                userPeerConnection = null;
            }
            if (userLocalStream) {
                userLocalStream.getTracks().forEach(track => track.stop());
                userLocalStream = null;
            }
            document.getElementById('localVideo').srcObject = null;
            document.getElementById('remoteVideo').srcObject = null;
            callState = 'idle';
            updateButtons();
        }

        // 摄像头控制 (已移动到支持音视频模式的版本)

        // 呼叫设备 (已移动到设备列表相关函数中)

        async function makeCall() {
            if (!userLocalStream) {
                addLog('请先启动摄像头', 'error');
                return;
            }

            try {
                addLog(`📞 呼叫设备 ${currentTargetDevice}...`, 'info');
                callState = 'calling';
                updateConnectionStatus('📞 正在呼叫...', 'calling');
                updateButtons();

                const offer = await userPeerConnection.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });

                // 使用温和的编解码器优化
                const modifiedSdp = prioritizeH264Codec(offer.sdp);
                const modifiedOffer = new RTCSessionDescription({
                    type: 'offer',
                    sdp: modifiedSdp
                });

                await userPeerConnection.setLocalDescription(modifiedOffer);
                addLog('🎬 已优化编解码器顺序，优先H.264', 'info');

                sendUserMessage({
                    type: 'offer',
                    payload: {
                        type: 'offer',
                        sdp: modifiedOffer.sdp
                    }
                });

                addLog('📤 通话邀请已发送', 'success');
            } catch (error) {
                addLog(`❌ 发起通话失败: ${error.message}`, 'error');
                callState = 'idle';
                updateConnectionStatus('🟢 已连接服务器', 'connected');
                updateButtons();
            }
        }

        function hangupCall() {
            addLog('📞 主动挂断通话', 'info');

            sendUserMessage({
                type: 'hangup',
                payload: {}
            });

            callState = 'idle';
            updateConnectionStatus('🟢 已连接服务器', 'connected');

            // 清理WebRTC连接但不重新初始化
            if (userPeerConnection) {
                userPeerConnection.close();
                userPeerConnection = null;
            }

            // 清理本地流
            if (userLocalStream) {
                userLocalStream.getTracks().forEach(track => track.stop());
                userLocalStream = null;
            }

            document.getElementById('remoteVideo').srcObject = null;
            document.getElementById('localVideo').srcObject = null;

            // 清理语音通话界面
            hideAudioCallInterface();

            updateButtons();

            addLog('等待服务器断开连接...', 'info');
        }

        // 媒体控制
        function toggleVideo() {
            if (userLocalStream) {
                const videoTrack = userLocalStream.getVideoTracks()[0];
                if (videoTrack) {
                    videoEnabled = !videoEnabled;
                    videoTrack.enabled = videoEnabled;
                    document.getElementById('videoBtn').textContent = videoEnabled ? '📹' : '📹';
                    document.getElementById('videoBtn').style.opacity = videoEnabled ? '1' : '0.5';
                    addLog(`📹 视频${videoEnabled ? '开启' : '关闭'}`, 'info');
                }
            }
        }

        function toggleAudio() {
            if (userLocalStream) {
                const audioTrack = userLocalStream.getAudioTracks()[0];
                if (audioTrack) {
                    audioEnabled = !audioEnabled;
                    audioTrack.enabled = audioEnabled;
                    document.getElementById('audioBtn').textContent = audioEnabled ? '🎤' : '🔇';
                    document.getElementById('audioBtn').style.opacity = audioEnabled ? '1' : '0.5';
                    addLog(`🎤 音频${audioEnabled ? '开启' : '静音'}`, 'info');
                }
            }
        }

        // 消息处理
        function handleUserMessage(data) {
            const messageType = data.type;
            addLog(`📨 收到: ${messageType}`, 'success');

            if (messageType === 'connection_established') {
                addLog(`   会话ID: ${data.payload.session_id}`, 'info');
            } else if (messageType === 'peer_connected') {
                addLog(`   设备已连接: ${data.payload.peer_type}`, 'success');
            } else if (messageType === 'offer') {
                handleUserOffer(data.payload);
            } else if (messageType === 'answer') {
                handleUserAnswer(data.payload);
            } else if (messageType === 'candidate') {
                handleUserCandidate(data.payload);
            } else if (messageType === 'hangup') {
                handleUserHangup();
            }
        }

        // WebRTC信令处理
        async function handleUserOffer(payload) {
            try {
                addLog('📥 处理Offer...', 'info');
                const offer = new RTCSessionDescription(payload);
                await userPeerConnection.setRemoteDescription(offer);

                const answer = await userPeerConnection.createAnswer();
                await userPeerConnection.setLocalDescription(answer);

                sendUserMessage({
                    type: 'answer',
                    payload: {
                        type: 'answer',
                        sdp: answer.sdp
                    }
                });

                addLog('✅ Answer已发送', 'success');
            } catch (error) {
                addLog(`❌ 处理Offer失败: ${error.message}`, 'error');
            }
        }

        async function handleUserAnswer(payload) {
            try {
                const answer = new RTCSessionDescription(payload);
                await userPeerConnection.setRemoteDescription(answer);
                addLog('✅ Answer处理完成', 'success');
            } catch (error) {
                addLog(`❌ 处理Answer失败: ${error.message}`, 'error');
            }
        }

        async function handleUserCandidate(payload) {
            try {
                const candidate = new RTCIceCandidate(payload);
                await userPeerConnection.addIceCandidate(candidate);
                addLog('🧊 ICE候选已添加', 'info');
            } catch (error) {
                addLog(`❌ 添加ICE候选失败: ${error.message}`, 'error');
            }
        }

        function handleUserHangup() {
            addLog('📞 对方挂断了通话', 'warning');

            callState = 'idle';
            updateConnectionStatus('🟢 已连接服务器', 'connected');

            // 清理WebRTC连接
            if (userPeerConnection) {
                userPeerConnection.close();
                userPeerConnection = null;
            }

            document.getElementById('remoteVideo').srcObject = null;
            updateButtons();

            addLog('通话已被对方结束', 'info');
        }

        // 发送消息
        function sendUserMessage(message) {
            if (userSocket && userConnected) {
                userSocket.send(JSON.stringify(message));
            }
        }

        // 工具函数
        function clearLog() {
            document.getElementById('callLog').innerHTML = '<div class="log-entry info">[系统] 日志已清空</div>';
        }

        // 浏览器兼容性检查
        function checkBrowserSupport() {
            const issues = [];

            if (!navigator.mediaDevices) {
                issues.push('不支持 MediaDevices API');
            }

            if (!navigator.mediaDevices?.getUserMedia) {
                issues.push('不支持 getUserMedia');
            }

            if (!window.RTCPeerConnection) {
                issues.push('不支持 WebRTC');
            }

            if (!window.WebSocket) {
                issues.push('不支持 WebSocket');
            }

            if (issues.length > 0) {
                addLog('⚠️ 浏览器兼容性问题:', 'warning');
                issues.forEach(issue => addLog(`   - ${issue}`, 'warning'));
                addLog('💡 建议使用 Chrome 88+, Firefox 85+, Safari 14+', 'info');

                if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                    addLog('💡 WebRTC需要HTTPS或localhost环境', 'warning');
                }
            } else {
                addLog('✅ 浏览器支持所有必需功能', 'success');
            }
        }

        // ==================== 设备管理功能 ====================

        function toggleDeviceList() {
            const deviceCard = document.getElementById('deviceManagementCard');
            if (deviceCard.style.display === 'none') {
                deviceCard.style.display = 'block';
                loadDeviceList();
            } else {
                deviceCard.style.display = 'none';
            }
        }

        async function loadDeviceList() {
            if (!userToken) {
                addLog('❌ 请先登录', 'error');
                return;
            }

            const deviceList = document.getElementById('deviceList');
            const deviceLoading = document.getElementById('deviceLoading');

            // 显示加载状态
            deviceLoading.style.display = 'block';
            deviceList.innerHTML = '<div class="loading-indicator" id="deviceLoading"><div class="spinner"></div><p>加载设备列表...</p></div>';

            try {
                // 🔒 安全修复：使用Authorization头而不是URL参数传递token
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/devices`, {
                    headers: {
                        'Authorization': `Bearer ${userToken}`
                    }
                });
                const result = await response.json();

                if (result.success) {
                    userDevices = result.data.devices;
                    renderDeviceList(userDevices);
                    updateDeviceStats();

                    // 开始定期更新设备状态
                    startDeviceStatusUpdates();

                    addLog(`📱 加载了 ${userDevices.length} 个设备`, 'success');
                } else {
                    throw new Error(result.message || '获取设备列表失败');
                }
            } catch (error) {
                addLog(`❌ 加载设备列表失败: ${error.message}`, 'error');
                deviceList.innerHTML = '<div class="error-message">加载失败，请重试</div>';
            }
        }

        function renderDeviceList(devices) {
            const deviceList = document.getElementById('deviceList');

            if (devices.length === 0) {
                deviceList.innerHTML = `
                    <div class="empty-state">
                        <p>📱 还没有绑定的设备</p>
                        <button class="btn" onclick="showAddDeviceDialog()">添加第一个设备</button>
                    </div>
                `;
                return;
            }

            const deviceHTML = devices.map(device => `
                <div class="device-item" data-device-id="${device.device_id}">
                    <div class="device-info">
                        <div class="device-icon">${getDeviceIcon(device.device_type)}</div>
                        <div class="device-details">
                            <h4>${device.device_name}</h4>
                            <p>${device.device_location || device.device_id} • ${device.permission_level}</p>
                        </div>
                    </div>
                    <div class="device-status">
                        <div class="status-dot" id="status-${device.device_id}"></div>
                        <button class="device-actions-btn" onclick="callDevice('${device.device_id}', '${device.device_name}')">
                            📞 呼叫
                        </button>
                        <button class="device-actions-btn" onclick="showDeviceMenu('${device.device_id}')">
                            ⋮
                        </button>
                    </div>
                </div>
            `).join('');

            deviceList.innerHTML = deviceHTML;
        }

        function getDeviceIcon(deviceType) {
            const icons = {
                'camera': '📹',
                'doorbell': '🔔',
                'speaker': '🔊',
                'sensor': '📡',
                'light': '💡',
                'lock': '🔒'
            };
            return icons[deviceType] || '📱';
        }

        async function refreshDeviceList() {
            addLog('🔄 刷新设备列表...', 'info');
            await loadDeviceList();
        }

        function updateDeviceStats() {
            const totalDevices = userDevices.length;
            let onlineDevices = 0;

            userDevices.forEach(device => {
                const statusDot = document.getElementById(`status-${device.device_id}`);
                if (statusDot && statusDot.classList.contains('online')) {
                    onlineDevices++;
                }
            });

            document.getElementById('totalDevices').textContent = totalDevices;
            document.getElementById('onlineDevices').textContent = onlineDevices;
        }

        async function startDeviceStatusUpdates() {
            // 清除之前的定时器
            if (deviceStatusInterval) {
                clearInterval(deviceStatusInterval);
            }

            // 立即更新一次
            await updateAllDeviceStatus();

            // 每30秒更新一次设备状态
            deviceStatusInterval = setInterval(updateAllDeviceStatus, 30000);
        }

        async function updateAllDeviceStatus() {
            if (!userToken || userDevices.length === 0) return;

            for (const device of userDevices) {
                await updateDeviceStatus(device.device_id);
            }
            updateDeviceStats();
        }

        async function updateDeviceStatus(deviceId) {
            try {
                // 🔒 安全修复：使用Authorization头而不是URL参数传递token
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/device/${deviceId}/status`, {
                    headers: {
                        'Authorization': `Bearer ${userToken}`
                    }
                });
                const result = await response.json();

                if (result.success) {
                    const statusDot = document.getElementById(`status-${deviceId}`);
                    if (statusDot) {
                        if (result.data.online) {
                            statusDot.classList.add('online');
                        } else {
                            statusDot.classList.remove('online');
                        }
                    }
                }
            } catch (error) {
                console.warn(`更新设备 ${deviceId} 状态失败:`, error);
            }
        }

        function callDevice(deviceId, deviceName) {
            // 使用现有的呼叫功能
            currentTargetDevice = deviceId;
            addLog(`📞 呼叫设备: ${deviceName} (${deviceId})`, 'info');

            // 检查是否有本地媒体流
            if (!userLocalStream) {
                addLog('⚠️ 请先启动摄像头', 'warning');
                return;
            }

            makeCall();
        }

        // 测试函数：模拟设备响应（用于调试）
        function simulateDeviceResponse() {
            addLog('🤖 模拟设备响应（测试模式）', 'info');

            // 模拟收到设备的视频流（使用本地摄像头作为测试）
            setTimeout(() => {
                if (userLocalStream) {
                    const remoteVideo = document.getElementById('remoteVideo');
                    // 克隆本地流作为"远程"流进行测试
                    remoteVideo.srcObject = userLocalStream.clone();
                    addLog('🤖 测试：显示模拟的远程视频流', 'success');
                }
            }, 2000);
        }

        function showDeviceMenu(deviceId) {
            // 显示设备操作菜单
            const device = userDevices.find(d => d.device_id === deviceId);
            if (!device) return;

            const menu = `
                设备操作菜单:
                1. 查看详情
                2. 修改名称
                3. 解绑设备

                设备: ${device.device_name}
                ID: ${device.device_id}
                权限: ${device.permission_level}
            `;

            if (confirm(menu + '\n\n是否要解绑此设备？')) {
                unbindDevice(deviceId);
            }
        }

        async function unbindDevice(deviceId) {
            if (!userToken) {
                addLog('❌ 请先登录', 'error');
                return;
            }

            try {
                // 🔒 安全修复：使用Authorization头而不是URL参数传递token
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/device/${deviceId}/bind`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${userToken}`
                    }
                });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ 设备解绑成功: ${deviceId}`, 'success');
                    await loadDeviceList(); // 重新加载设备列表
                } else {
                    throw new Error(result.message || '解绑失败');
                }
            } catch (error) {
                addLog(`❌ 解绑设备失败: ${error.message}`, 'error');
            }
        }

        function showAddDeviceDialog() {
            // 显示绑定方式选择
            const bindingMethod = confirm(
                '设备绑定方式：\n\n' +
                '确定 - 使用绑定码绑定（推荐）\n' +
                '取消 - 直接输入设备ID绑定'
            );

            if (bindingMethod) {
                // 使用绑定码绑定
                showBindingCodeDialog();
            } else {
                // 直接绑定设备ID
                const deviceId = prompt('请输入要绑定的设备ID:');
                if (deviceId) {
                    const deviceName = prompt('请输入设备显示名称:', deviceId);
                    if (deviceName) {
                        bindDevice(deviceId, deviceName);
                    }
                }
            }
        }

        function showBindingCodeDialog() {
            const deviceId = prompt('请输入设备ID:');
            if (!deviceId) return;

            const bindingCode = prompt('请输入6位绑定码:');
            if (!bindingCode) return;

            if (bindingCode.length !== 6 || !/^\d{6}$/.test(bindingCode)) {
                alert('绑定码必须是6位数字');
                return;
            }

            const deviceName = prompt('请输入设备显示名称:', deviceId);
            if (deviceName) {
                bindDeviceWithCode(deviceId, bindingCode, deviceName);
            }
        }

        async function bindDeviceWithCode(deviceId, bindingCode, deviceName) {
            if (!userToken) {
                addLog('❌ 请先登录', 'error');
                return;
            }

            try {
                addLog(`🔗 正在使用绑定码绑定设备: ${deviceId}`, 'info');

                // 🔒 安全修复：使用Authorization头而不是URL参数传递token
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/device/bind-with-code?device_id=${deviceId}&binding_code=${bindingCode}&device_name=${encodeURIComponent(deviceName)}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${userToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ 设备绑定成功: ${deviceName} (${deviceId})`, 'success');
                    await loadDeviceList(); // 重新加载设备列表
                } else {
                    throw new Error(result.message || '绑定失败');
                }
            } catch (error) {
                addLog(`❌ 绑定设备失败: ${error.message}`, 'error');

                // 提供具体的错误建议
                if (error.message.includes('Invalid binding code')) {
                    addLog('💡 请检查绑定码是否正确或已过期', 'warning');
                } else if (error.message.includes('Device not found')) {
                    addLog('💡 请检查设备ID是否正确', 'warning');
                }
            }
        }

        async function bindDevice(deviceId, deviceName) {
            if (!userToken) {
                addLog('❌ 请先登录', 'error');
                return;
            }

            try {
                // 🔒 安全修复：使用Authorization头而不是URL参数传递token
                const response = await fetch(`${SERVER_CONFIG.API_BASE_URL}/api/v1/webrtc/device/${deviceId}/bind?device_name=${encodeURIComponent(deviceName)}&permission_level=control`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${userToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ 设备绑定成功: ${deviceName} (${deviceId})`, 'success');
                    await loadDeviceList(); // 重新加载设备列表
                } else {
                    throw new Error(result.message || '绑定失败');
                }
            } catch (error) {
                addLog(`❌ 绑定设备失败: ${error.message}`, 'error');
            }
        }

        // ==================== 语音通话优化功能 ====================

        function setCallMode(mode) {
            currentCallMode = mode;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(mode + 'ModeBtn').classList.add('active');

            // 更新摄像头按钮文本
            const cameraBtn = document.getElementById('startCameraBtn');
            if (mode === 'audio') {
                cameraBtn.textContent = '🎤 启动麦克风';
                addLog('🎤 切换到语音通话模式', 'info');
            } else {
                cameraBtn.textContent = '📹 启动摄像头';
                addLog('📹 切换到视频通话模式', 'info');
            }

            // 如果正在通话中，切换界面
            if (userLocalStream) {
                updateCallInterface();
            }
        }

        function updateCallInterface() {
            const localVideo = document.getElementById('localVideo');
            const remoteVideo = document.getElementById('remoteVideo');
            const videoContainer = localVideo.parentElement;

            if (currentCallMode === 'audio') {
                // 切换到语音通话界面
                showAudioCallInterface();
                localVideo.style.display = 'none';
                remoteVideo.style.display = 'none';
            } else {
                // 切换到视频通话界面
                hideAudioCallInterface();
                localVideo.style.display = 'block';
                remoteVideo.style.display = 'block';
            }
        }

        function showAudioCallInterface() {
            const videoContainer = document.querySelector('.video-container');

            // 创建语音通话界面
            const audioInterface = document.createElement('div');
            audioInterface.id = 'audioCallInterface';
            audioInterface.className = 'audio-call-interface';
            audioInterface.innerHTML = `
                <div class="audio-avatar">🎤</div>
                <div class="audio-info">
                    <h3 id="audioCallTitle">语音通话中</h3>
                    <p id="audioCallSubtitle">正在连接...</p>
                </div>
                <div class="call-duration" id="callDuration">00:00</div>
                <div class="audio-visualizer" id="audioVisualizer">
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                    <div class="audio-bar"></div>
                </div>
                <div class="network-quality">
                    <div class="quality-indicator good"></div>
                    <span>网络质量良好</span>
                </div>
            `;

            videoContainer.appendChild(audioInterface);

            // 开始通话计时
            startCallTimer();
        }

        function hideAudioCallInterface() {
            const audioInterface = document.getElementById('audioCallInterface');
            if (audioInterface) {
                audioInterface.remove();
            }

            // 停止通话计时
            stopCallTimer();
        }

        function startCallTimer() {
            callStartTime = Date.now();

            callDurationInterval = setInterval(() => {
                const duration = Math.floor((Date.now() - callStartTime) / 1000);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;

                const durationElement = document.getElementById('callDuration');
                if (durationElement) {
                    durationElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        function stopCallTimer() {
            if (callDurationInterval) {
                clearInterval(callDurationInterval);
                callDurationInterval = null;
            }
            callStartTime = null;
        }

        // 修改摄像头启动函数以支持语音模式
        async function startCamera() {
            try {
                addLog(currentCallMode === 'audio' ? '🎤 启动麦克风...' : '📹 启动摄像头...', 'info');

                // 检查浏览器支持
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('浏览器不支持媒体访问，请使用Chrome/Firefox/Safari等现代浏览器');
                }

                // 首先枚举设备，确保能检测到USB摄像头
                addLog('🔍 正在检测可用设备...', 'info');
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                const audioDevices = devices.filter(device => device.kind === 'audioinput');

                addLog(`📹 检测到 ${videoDevices.length} 个摄像头设备`, 'info');
                addLog(`🎤 检测到 ${audioDevices.length} 个麦克风设备`, 'info');

                // 显示检测到的摄像头
                videoDevices.forEach((device, index) => {
                    const label = device.label || `摄像头 ${index + 1}`;
                    addLog(`  📹 摄像头${index + 1}: ${label}`, 'info');
                });

                // 显示检测到的麦克风
                audioDevices.forEach((device, index) => {
                    const label = device.label || `麦克风 ${index + 1}`;
                    addLog(`  🎤 麦克风${index + 1}: ${label}`, 'info');
                });

                // 检查是否有所需设备
                if (currentCallMode === 'video' && videoDevices.length === 0) {
                    throw new Error('未检测到摄像头设备，请连接USB摄像头或使用OBS虚拟摄像头');
                }

                if (audioDevices.length === 0) {
                    addLog('⚠️ 未检测到麦克风设备', 'warning');
                }

                // 根据通话模式设置约束
                // const constraints = {
                //     video: currentCallMode === 'video' ? {
                //         width: { ideal: 640 },
                //         height: { ideal: 480 },
                //         frameRate: { ideal: 30 },
                //         facingMode: 'user'
                //     } : false,
                //     audio: {
                //         echoCancellation: true,
                //         noiseSuppression: true,
                //         autoGainControl: true
                //     }
                // };

                const constraints = {
                    video: currentCallMode === 'video' ? {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 30 }
                    } : false,
                    audio: audioDevices.length > 0 ? {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    } : false
                };

                addLog(`🎬 媒体约束: 视频=${constraints.video ? '启用' : '禁用'}, 音频=${constraints.audio ? '启用' : '禁用'}`, 'info');

                userLocalStream = await navigator.mediaDevices.getUserMedia(constraints);

                if (currentCallMode === 'video') {
                    document.getElementById('localVideo').srcObject = userLocalStream;
                }

                if (userPeerConnection) {
                    userLocalStream.getTracks().forEach(track => {
                        userPeerConnection.addTrack(track, userLocalStream);
                    });
                }

                addLog(currentCallMode === 'audio' ? '✅ 麦克风启动成功' : '✅ 摄像头启动成功', 'success');
                updateButtons();
                updateCallInterface();

            } catch (error) {
                addLog(`❌ ${currentCallMode === 'audio' ? '麦克风' : '摄像头'}启动失败: ${error.message}`, 'error');

                // 提供解决建议
                if (error.name === 'NotAllowedError') {
                    addLog('💡 请允许浏览器访问摄像头和麦克风', 'warning');
                } else if (error.name === 'NotFoundError') {
                    addLog('💡 未找到摄像头设备，这可能是绿屏问题的原因', 'warning');
                    addLog('🎯 解决方案：使用Chrome命令行参数启动虚拟摄像头', 'info');
                    addLog('📋 命令：chrome.exe --use-fake-device-for-media-stream --use-fake-ui-for-media-stream', 'info');
                    addLog('📋 这将提供虚拟摄像头，解决WebRTC协商问题', 'info');
                } else if (error.name === 'NotSupportedError') {
                    addLog('💡 请使用HTTPS访问或使用localhost', 'warning');
                }
            }
        }

        // 防止重复检测的标志
        let isDetecting = false;
        let detectionCount = 0;
        const MAX_DETECTION_COUNT = 2;

        // 温和的编解码器优化函数（只调整顺序，不删除）
        function prioritizeH264Codec(sdp) {
            addLog('🎬 优化编解码器顺序，优先H.264...', 'info');

            // 查找H.264编解码器
            const h264Regex = /a=rtpmap:(\d+) H264\/90000/;
            const h264Match = sdp.match(h264Regex);

            if (h264Match) {
                const h264PayloadType = h264Match[1];
                addLog(`🎬 找到H.264编解码器，payload type: ${h264PayloadType}`, 'info');

                // 修改m=video行，将H.264放在第一位
                const videoLineRegex = /m=video (\d+) ([\w\/]+) (.+)/;
                const videoMatch = sdp.match(videoLineRegex);

                if (videoMatch) {
                    const port = videoMatch[1];
                    const protocol = videoMatch[2];
                    const payloadTypes = videoMatch[3].split(' ');

                    // 将H.264移到第一位，保留其他编解码器
                    const reorderedTypes = [h264PayloadType, ...payloadTypes.filter(pt => pt !== h264PayloadType)];
                    const newVideoLine = `m=video ${port} ${protocol} ${reorderedTypes.join(' ')}`;
                    sdp = sdp.replace(videoMatch[0], newVideoLine);
                    addLog('🎬 H.264编解码器已设为首选，保留其他编解码器作为备选', 'success');
                }
            } else {
                addLog('⚠️ 未找到H.264编解码器', 'warning');
            }

            return sdp;
        }

        // 强制使用H.264编解码器的SDP修改函数（移动端）
        function forceH264CodecMobile(sdp) {
            addLog('🎬 移动端强制使用H.264编解码器...', 'info');

            // 查找H.264编解码器
            const h264Regex = /a=rtpmap:(\d+) H264\/90000/;
            const h264Match = sdp.match(h264Regex);

            if (h264Match) {
                const h264PayloadType = h264Match[1];
                addLog(`🎬 找到H.264编解码器，payload type: ${h264PayloadType}`, 'info');

                // 修改m=video行，只保留H.264
                const videoLineRegex = /m=video (\d+) ([\w\/]+) (.+)/;
                const videoMatch = sdp.match(videoLineRegex);

                if (videoMatch) {
                    const port = videoMatch[1];
                    const protocol = videoMatch[2];
                    // 只使用H.264 payload type
                    const newVideoLine = `m=video ${port} ${protocol} ${h264PayloadType}`;
                    sdp = sdp.replace(videoMatch[0], newVideoLine);
                    addLog('🎬 视频行已修改为仅支持H.264', 'success');
                }

                // 移除所有非H.264的视频编解码器
                const lines = sdp.split('\n');
                const filteredLines = lines.filter(line => {
                    // 保留H.264相关的行
                    if (line.includes(`a=rtpmap:${h264PayloadType}`) ||
                        line.includes(`a=fmtp:${h264PayloadType}`) ||
                        line.includes(`a=rtcp-fb:${h264PayloadType}`)) {
                        return true;
                    }

                    // 移除其他视频编解码器的rtpmap行
                    if (line.startsWith('a=rtpmap:') && !line.includes(`${h264PayloadType}`)) {
                        const payloadMatch = line.match(/a=rtpmap:(\d+)/);
                        if (payloadMatch && payloadMatch[1] !== h264PayloadType) {
                            // 检查这个payload是否在video行中被引用
                            if (sdp.includes(`m=video`) && sdp.includes(payloadMatch[1])) {
                                addLog(`🎬 移除非H.264编解码器: ${line}`, 'info');
                                return false;
                            }
                        }
                    }

                    // 移除其他视频编解码器的fmtp和rtcp-fb行
                    if ((line.startsWith('a=fmtp:') || line.startsWith('a=rtcp-fb:')) &&
                        !line.includes(`${h264PayloadType}`)) {
                        const payloadMatch = line.match(/a=(?:fmtp|rtcp-fb):(\d+)/);
                        if (payloadMatch && payloadMatch[1] !== h264PayloadType) {
                            addLog(`🎬 移除非H.264属性: ${line}`, 'info');
                            return false;
                        }
                    }

                    return true;
                });

                sdp = filteredLines.join('\n');
                addLog('🎬 SDP已清理，仅保留H.264编解码器', 'success');

            } else {
                addLog('⚠️ 未找到H.264编解码器，尝试添加', 'warning');
                // 如果没有H.264，尝试添加一个基本的H.264配置
                const videoLineRegex = /m=video (\d+) ([\w\/]+) (.+)/;
                const videoMatch = sdp.match(videoLineRegex);
                if (videoMatch) {
                    const h264PayloadType = '96'; // 使用动态payload type
                    const newVideoLine = `m=video ${videoMatch[1]} ${videoMatch[2]} ${h264PayloadType}`;
                    sdp = sdp.replace(videoMatch[0], newVideoLine);

                    // 添加H.264 rtpmap
                    const h264Rtpmap = `a=rtpmap:${h264PayloadType} H264/90000`;
                    const h264Fmtp = `a=fmtp:${h264PayloadType} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f`;

                    sdp = sdp.replace(/m=video[^\n]*\n/, `${newVideoLine}\n${h264Rtpmap}\n${h264Fmtp}\n`);
                    addLog('🎬 已添加H.264编解码器配置', 'success');
                }
            }

            return sdp;
        }

        // 自动检测和修复绿屏
        function detectAndFixGreenScreen(videoElement) {
            if (!videoElement || !videoElement.srcObject) return;

            // 防止重复检测
            if (isDetecting || detectionCount >= MAX_DETECTION_COUNT) {
                addLog('⚠️ 绿屏检测已达到最大次数或正在进行中，跳过', 'warning');
                return;
            }

            isDetecting = true;
            detectionCount++;

            try {
                // 创建canvas来检测视频内容
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = videoElement.videoWidth || 320;
                canvas.height = videoElement.videoHeight || 240;

                // 绘制当前视频帧
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // 获取图像数据
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                // 改进的绿屏检测算法
                let greenPixels = 0;
                let totalPixels = data.length / 4;
                let pureGreenPixels = 0; // 纯绿色像素

                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    const alpha = data[i + 3];

                    // 跳过透明像素
                    if (alpha < 128) continue;

                    // 检测明显的绿色像素 (绿色值明显高于红色和蓝色)
                    if (g > r + 30 && g > b + 30 && g > 80) {
                        greenPixels++;

                        // 检测纯绿色像素 (RGB: 0, 255, 0 附近)
                        if (r < 50 && g > 200 && b < 50) {
                            pureGreenPixels++;
                        }
                    }
                }

                const greenPercentage = (greenPixels / totalPixels) * 100;
                const pureGreenPercentage = (pureGreenPixels / totalPixels) * 100;

                addLog(`🔍 绿色像素占比: ${greenPercentage.toFixed(1)}%`, 'info');
                addLog(`🔍 纯绿色像素占比: ${pureGreenPercentage.toFixed(1)}%`, 'info');

                // 更严格的绿屏判断：纯绿色像素超过50%或总绿色像素超过80%
                if (pureGreenPercentage > 50 || greenPercentage > 80) {
                    addLog('🟢 检测到绿屏问题，自动尝试修复...', 'warning');
                    setTimeout(() => fixGreenScreen(), 100); // 延迟执行避免冲突
                } else if (greenPercentage > 30) {
                    addLog('⚠️ 检测到较多绿色像素，但可能是正常内容', 'warning');
                } else {
                    addLog('✅ 视频显示正常，无绿屏问题', 'success');
                }

            } catch (error) {
                addLog(`⚠️ 绿屏检测失败: ${error.message}`, 'warning');
            } finally {
                // 重置检测状态
                setTimeout(() => {
                    isDetecting = false;
                }, 1000);
            }
        }

        // 防止无限循环的标志
        let isRefreshing = false;
        let refreshCount = 0;
        const MAX_REFRESH_COUNT = 3;

        // 重置所有计数器
        function resetVideoCounters() {
            isRefreshing = false;
            refreshCount = 0;
            isDetecting = false;
            detectionCount = 0;
            addLog('🔄 所有视频处理计数器已重置', 'info');
        }

        // 强制使用基础H.264配置
        async function forceBasicH264() {
            addLog('🎬 强制使用基础H.264配置...', 'info');

            if (!userPeerConnection) {
                addLog('❌ WebRTC连接不存在', 'error');
                return;
            }

            try {
                // 创建新的offer，强制使用最基础的H.264配置
                const offer = await userPeerConnection.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });

                // 修改SDP，强制使用最兼容的H.264配置
                let modifiedSdp = offer.sdp;

                // 查找H.264编解码器
                const h264Regex = /a=rtpmap:(\d+) H264\/90000/g;
                let h264PayloadType = null;
                let match;

                while ((match = h264Regex.exec(modifiedSdp)) !== null) {
                    h264PayloadType = match[1];
                    break; // 使用第一个找到的H.264
                }

                if (h264PayloadType) {
                    addLog(`🎬 找到H.264编解码器: ${h264PayloadType}`, 'info');

                    // 修改m=video行，只保留H.264
                    const videoLineRegex = /m=video (\d+) ([\w\/]+) (.+)/;
                    const videoMatch = modifiedSdp.match(videoLineRegex);

                    if (videoMatch) {
                        const port = videoMatch[1];
                        const protocol = videoMatch[2];
                        const newVideoLine = `m=video ${port} ${protocol} ${h264PayloadType}`;
                        modifiedSdp = modifiedSdp.replace(videoMatch[0], newVideoLine);
                        addLog('🎬 视频行已修改为仅支持H.264', 'info');
                    }

                    // 移除所有非H.264的编解码器行
                    const lines = modifiedSdp.split('\n');
                    const filteredLines = lines.filter(line => {
                        // 保留H.264相关的行
                        if (line.includes(`a=rtpmap:${h264PayloadType}`) ||
                            line.includes(`a=fmtp:${h264PayloadType}`) ||
                            line.includes(`a=rtcp-fb:${h264PayloadType}`)) {
                            return true;
                        }

                        // 移除其他视频编解码器
                        if (line.startsWith('a=rtpmap:') &&
                            (line.includes('VP8') || line.includes('VP9') || line.includes('AV1') || line.includes('H265'))) {
                            addLog(`🎬 移除编解码器: ${line}`, 'info');
                            return false;
                        }

                        // 移除对应的fmtp和rtcp-fb行
                        if (line.startsWith('a=fmtp:') || line.startsWith('a=rtcp-fb:')) {
                            const payloadMatch = line.match(/a=(?:fmtp|rtcp-fb):(\d+)/);
                            if (payloadMatch && payloadMatch[1] !== h264PayloadType) {
                                // 检查这个payload是否是视频相关的
                                const payloadType = payloadMatch[1];
                                const rtpmapLine = lines.find(l => l.includes(`a=rtpmap:${payloadType}`));
                                if (rtpmapLine && (rtpmapLine.includes('VP8') || rtpmapLine.includes('VP9') ||
                                                 rtpmapLine.includes('AV1') || rtpmapLine.includes('H265'))) {
                                    addLog(`🎬 移除属性: ${line}`, 'info');
                                    return false;
                                }
                            }
                        }

                        return true;
                    });

                    modifiedSdp = filteredLines.join('\n');

                    // 强制使用最兼容的H.264配置
                    const fmtpRegex = new RegExp(`a=fmtp:${h264PayloadType} (.+)`);
                    const fmtpMatch = modifiedSdp.match(fmtpRegex);
                    if (fmtpMatch) {
                        // 使用最基础的H.264配置：Baseline Profile, Level 3.0
                        const newFmtp = `a=fmtp:${h264PayloadType} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001e`;
                        modifiedSdp = modifiedSdp.replace(fmtpMatch[0], newFmtp);
                        addLog('🎬 已设置为最兼容的H.264配置 (Baseline Profile)', 'success');
                    }

                } else {
                    addLog('❌ 未找到H.264编解码器', 'error');
                    return;
                }

                // 设置修改后的offer
                const modifiedOffer = new RTCSessionDescription({
                    type: 'offer',
                    sdp: modifiedSdp
                });

                await userPeerConnection.setLocalDescription(modifiedOffer);

                // 发送新的offer
                sendUserMessage({
                    type: 'offer',
                    payload: {
                        type: 'offer',
                        sdp: modifiedOffer.sdp
                    }
                });

                addLog('✅ 基础H.264配置已应用，重新协商中...', 'success');

            } catch (error) {
                addLog(`❌ 强制H.264配置失败: ${error.message}`, 'error');
            }
        }

        // 禁用硬件加速功能
        function disableHardwareAcceleration() {
            addLog('🚫 禁用硬件加速...', 'info');

            const remoteVideo = document.getElementById('remoteVideo');
            const localVideo = document.getElementById('localVideo');

            if (remoteVideo) {
                remoteVideo.style.transform = 'none';
                remoteVideo.style.willChange = 'auto';
                remoteVideo.style.webkitTransform = 'none';
                remoteVideo.style.webkitWillChange = 'auto';
                remoteVideo.style.filter = 'none';
                remoteVideo.style.webkitFilter = 'none';
                remoteVideo.style.imageRendering = 'auto';
                remoteVideo.style.webkitImageRendering = 'auto';
                addLog('🚫 远程视频硬件加速已禁用', 'info');
            }

            if (localVideo) {
                localVideo.style.transform = 'none';
                localVideo.style.willChange = 'auto';
                localVideo.style.webkitTransform = 'none';
                localVideo.style.webkitWillChange = 'auto';
                addLog('🚫 本地视频硬件加速已禁用', 'info');
            }

            // 强制重绘
            if (remoteVideo) {
                remoteVideo.style.display = 'none';
                remoteVideo.offsetHeight; // 触发重排
                remoteVideo.style.display = 'block';
            }

            addLog('✅ 硬件加速禁用完成，请检查视频显示', 'success');
        }

        // 设备检测功能
        async function detectDevices() {
            addLog('🔍 开始检测设备...', 'info');

            try {
                // 检查浏览器支持
                if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
                    addLog('❌ 浏览器不支持设备枚举', 'error');
                    return;
                }

                // 枚举设备
                const devices = await navigator.mediaDevices.enumerateDevices();
                addLog(`📱 总共检测到 ${devices.length} 个媒体设备`, 'info');

                // 分类设备
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                const audioInputDevices = devices.filter(device => device.kind === 'audioinput');
                const audioOutputDevices = devices.filter(device => device.kind === 'audiooutput');

                addLog(`📹 摄像头设备: ${videoDevices.length} 个`, 'info');
                addLog(`🎤 麦克风设备: ${audioInputDevices.length} 个`, 'info');
                addLog(`🔊 扬声器设备: ${audioOutputDevices.length} 个`, 'info');

                // 详细显示摄像头设备
                if (videoDevices.length > 0) {
                    addLog('📹 === 摄像头设备详情 ===', 'info');
                    videoDevices.forEach((device, index) => {
                        const label = device.label || `摄像头 ${index + 1}`;
                        const deviceId = device.deviceId ? device.deviceId.substring(0, 20) + '...' : '未知';
                        addLog(`  ${index + 1}. ${label}`, 'success');
                        addLog(`     设备ID: ${deviceId}`, 'info');
                    });

                    // 检查是否有USB摄像头
                    const usbCamera = videoDevices.find(device =>
                        device.label && (
                            device.label.toLowerCase().includes('usb') ||
                            device.label.toLowerCase().includes('camera')
                        )
                    );

                    if (usbCamera) {
                        addLog(`✅ 找到USB摄像头: ${usbCamera.label}`, 'success');
                    } else {
                        addLog('⚠️ 未找到明确的USB摄像头标识', 'warning');
                    }
                } else {
                    addLog('❌ 未检测到任何摄像头设备', 'error');
                    addLog('💡 请检查USB摄像头是否正确连接', 'info');
                    addLog('💡 或尝试刷新页面重新检测', 'info');
                }

                // 详细显示麦克风设备
                if (audioInputDevices.length > 0) {
                    addLog('🎤 === 麦克风设备详情 ===', 'info');
                    audioInputDevices.forEach((device, index) => {
                        const label = device.label || `麦克风 ${index + 1}`;
                        addLog(`  ${index + 1}. ${label}`, 'success');
                    });
                } else {
                    addLog('❌ 未检测到任何麦克风设备', 'error');
                }

                addLog('✅ 设备检测完成', 'success');

            } catch (error) {
                addLog(`❌ 设备检测失败: ${error.message}`, 'error');
            }
        }

        // 强制编解码器刷新 - 重新协商WebRTC连接
        async function forceCodecRefresh() {
            addLog('🎬 开始强制编解码器刷新...', 'info');

            if (!userPeerConnection) {
                addLog('❌ WebRTC连接不存在', 'error');
                return;
            }

            try {
                // 获取当前的接收器
                const receivers = userPeerConnection.getReceivers();
                addLog(`🎬 当前接收器数量: ${receivers.length}`, 'info');

                // 检查支持的编解码器
                const capabilities = RTCRtpReceiver.getCapabilities('video');
                if (capabilities && capabilities.codecs) {
                    addLog('🎬 支持的视频编解码器:', 'info');
                    capabilities.codecs.forEach((codec, index) => {
                        addLog(`  ${index + 1}. ${codec.mimeType} - ${codec.clockRate}`, 'info');
                    });
                }

                // 尝试重新协商连接
                addLog('🎬 重新协商WebRTC连接...', 'info');

                // 创建新的offer来重新协商
                const offer = await userPeerConnection.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });

                await userPeerConnection.setLocalDescription(offer);
                addLog('🎬 本地描述已设置，等待远程响应...', 'info');

                // 发送重新协商的offer
                if (userSocket && userSocket.readyState === WebSocket.OPEN) {
                    userSocket.send(JSON.stringify({
                        type: 'offer',
                        data: offer,
                        target: currentDeviceId
                    }));
                    addLog('🎬 重新协商offer已发送', 'success');
                } else {
                    addLog('❌ WebSocket连接不可用', 'error');
                }

            } catch (error) {
                addLog(`❌ 编解码器刷新失败: ${error.message}`, 'error');
            }
        }

        // 强制视频刷新以解决绿屏问题
        function forceVideoRefresh(videoElement) {
            // 防止无限循环
            if (isRefreshing || refreshCount >= MAX_REFRESH_COUNT) {
                addLog('⚠️ 视频刷新已达到最大次数或正在进行中，跳过', 'warning');
                return;
            }

            isRefreshing = true;
            refreshCount++;
            addLog(`🔄 强制刷新视频渲染... (第${refreshCount}次)`, 'info');

            // 方法1: 强制重绘
            videoElement.style.display = 'none';
            videoElement.offsetHeight; // 触发重排
            videoElement.style.display = 'block';
            addLog('✅ 视频显示已刷新', 'info');

            // 方法2: 切换硬件加速
            setTimeout(() => {
                videoElement.style.transform = 'none';
                setTimeout(() => {
                    videoElement.style.transform = 'translateZ(0)';
                    addLog('✅ 硬件加速已切换', 'info');
                }, 100);
            }, 200);

            // 重置刷新状态
            setTimeout(() => {
                isRefreshing = false;
                addLog('✅ 视频刷新完成', 'success');
            }, 500);
        }

        // 增强的绿屏修复功能
        function fixGreenScreen() {
            addLog('🟢 开始修复绿屏问题...', 'info');

            const remoteVideo = document.getElementById('remoteVideo');
            if (!remoteVideo) {
                addLog('❌ 找不到远程视频元素', 'error');
                return;
            }

            if (!remoteVideo.srcObject) {
                addLog('❌ 视频流未设置，无法修复', 'error');
                return;
            }

            // 方法1: 强制软件渲染
            addLog('🔧 尝试修复方法1: 强制软件渲染', 'info');
            remoteVideo.style.transform = 'none';
            remoteVideo.style.willChange = 'auto';
            remoteVideo.style.filter = 'none';
            remoteVideo.style.webkitFilter = 'none';

            setTimeout(() => {
                // 方法2: 重新创建video元素
                addLog('🔧 尝试修复方法2: 重新创建视频元素', 'info');
                const parent = remoteVideo.parentNode;
                const newVideo = document.createElement('video');

                // 复制所有属性
                newVideo.id = remoteVideo.id;
                newVideo.className = remoteVideo.className;
                newVideo.autoplay = true;
                newVideo.playsinline = true;
                newVideo.muted = true;
                newVideo.controls = false;

                // 设置流
                newVideo.srcObject = remoteVideo.srcObject;

                // 替换元素
                parent.replaceChild(newVideo, remoteVideo);

                // 强制播放
                newVideo.play().catch(e => {
                    addLog(`⚠️ 新视频元素播放失败: ${e.message}`, 'warning');
                });

            }, 500);

            setTimeout(() => {
                // 方法3: 尝试不同的CSS渲染模式
                addLog('🔧 尝试修复方法3: 调整CSS渲染', 'info');
                const video = document.getElementById('remoteVideo');
                if (video) {
                    video.style.imageRendering = 'pixelated';
                    video.style.webkitImageRendering = 'pixelated';
                    video.style.msImageRendering = 'pixelated';

                    setTimeout(() => {
                        video.style.imageRendering = 'auto';
                        video.style.webkitImageRendering = 'auto';
                        video.style.msImageRendering = 'auto';
                    }, 200);
                }
            }, 1000);

            setTimeout(() => {
                // 方法4: 强制重新协商WebRTC
                addLog('🔧 尝试修复方法4: 重新协商WebRTC连接', 'info');
                if (userPeerConnection && userPeerConnection.connectionState === 'connected') {
                    // 触发重新协商
                    userPeerConnection.createOffer().then(offer => {
                        return userPeerConnection.setLocalDescription(offer);
                    }).then(() => {
                        sendUserMessage({
                            type: 'offer',
                            payload: {
                                type: 'offer',
                                sdp: userPeerConnection.localDescription.sdp
                            }
                        });
                        addLog('🔧 重新协商请求已发送', 'info');
                    }).catch(e => {
                        addLog(`⚠️ 重新协商失败: ${e.message}`, 'warning');
                    });
                }
            }, 1500);

            setTimeout(() => {
                // 方法5: Canvas重绘方法
                addLog('🔧 尝试修复方法5: Canvas重绘', 'info');
                tryCanvasRedraw();
            }, 2000);

            setTimeout(() => {
                addLog('✅ 绿屏修复尝试完成，请检查视频显示', 'success');
            }, 2500);
        }

        // Canvas重绘方法
        function tryCanvasRedraw() {
            const remoteVideo = document.getElementById('remoteVideo');
            if (!remoteVideo || !remoteVideo.srcObject) return;

            try {
                // 创建canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = remoteVideo.videoWidth || 640;
                canvas.height = remoteVideo.videoHeight || 480;

                // 绘制视频帧
                ctx.drawImage(remoteVideo, 0, 0, canvas.width, canvas.height);

                // 获取图像数据并检查
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                // 检查是否有非绿色像素
                let hasNonGreenPixels = false;
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];

                    if (!(g > r + 30 && g > b + 30)) {
                        hasNonGreenPixels = true;
                        break;
                    }
                }

                if (hasNonGreenPixels) {
                    addLog('✅ Canvas检测到正常视频内容，可能是渲染问题', 'success');

                    // 尝试用canvas替换video
                    canvas.style.width = '100%';
                    canvas.style.height = '250px';
                    canvas.style.objectFit = 'contain';
                    canvas.style.background = '#000000';
                    canvas.style.borderRadius = '15px';

                    const parent = remoteVideo.parentNode;
                    parent.insertBefore(canvas, remoteVideo);
                    remoteVideo.style.display = 'none';

                    addLog('🎨 已切换到Canvas渲染模式', 'info');
                } else {
                    addLog('❌ Canvas也显示绿色，可能是流本身的问题', 'error');
                }

            } catch (error) {
                addLog(`❌ Canvas重绘失败: ${error.message}`, 'error');
            }
        }

        // 视频问题诊断功能
        function diagnoseVideoIssues() {
            addLog('🔍 开始视频问题诊断...', 'info');

            const remoteVideo = document.getElementById('remoteVideo');
            const localVideo = document.getElementById('localVideo');

            // 检查远程视频状态
            addLog('📺 === 远程视频诊断 ===', 'info');
            addLog(`📺 视频元素存在: ${remoteVideo ? '是' : '否'}`, 'info');

            if (remoteVideo) {
                addLog(`📺 srcObject: ${remoteVideo.srcObject ? '已设置' : '未设置'}`, 'info');
                addLog(`📺 videoWidth: ${remoteVideo.videoWidth}`, 'info');
                addLog(`📺 videoHeight: ${remoteVideo.videoHeight}`, 'info');
                addLog(`📺 readyState: ${remoteVideo.readyState}`, 'info');
                addLog(`📺 paused: ${remoteVideo.paused}`, 'info');
                addLog(`📺 ended: ${remoteVideo.ended}`, 'info');
                addLog(`📺 muted: ${remoteVideo.muted}`, 'info');
                addLog(`📺 currentTime: ${remoteVideo.currentTime}`, 'info');

                if (remoteVideo.srcObject) {
                    const stream = remoteVideo.srcObject;
                    addLog(`📺 流轨道数量: ${stream.getTracks().length}`, 'info');

                    stream.getTracks().forEach((track, index) => {
                        addLog(`📺 轨道${index}: ${track.kind} - ${track.enabled ? '启用' : '禁用'} - ${track.readyState}`, 'info');

                        if (track.kind === 'video') {
                            const settings = track.getSettings();
                            addLog(`📺 视频轨道设置: ${JSON.stringify(settings)}`, 'info');

                            // 检查是否是绿屏的常见原因
                            if (settings.width === 0 || settings.height === 0) {
                                addLog('❌ 发现问题: 视频轨道尺寸为0，这可能导致绿屏', 'error');
                            }

                            if (track.readyState === 'ended') {
                                addLog('❌ 发现问题: 视频轨道已结束', 'error');
                            }

                            if (!track.enabled) {
                                addLog('⚠️ 发现问题: 视频轨道被禁用', 'warning');
                            }
                        }
                    });
                } else {
                    addLog('❌ 远程视频流未设置', 'error');
                }
            }

            // 检查WebRTC连接状态
            addLog('🔗 === WebRTC连接诊断 ===', 'info');
            if (userPeerConnection) {
                addLog(`🔗 连接状态: ${userPeerConnection.connectionState}`, 'info');
                addLog(`🔗 ICE连接状态: ${userPeerConnection.iceConnectionState}`, 'info');
                addLog(`🔗 ICE收集状态: ${userPeerConnection.iceGatheringState}`, 'info');
                addLog(`🔗 信令状态: ${userPeerConnection.signalingState}`, 'info');

                // 检查接收器
                const receivers = userPeerConnection.getReceivers();
                addLog(`🔗 接收器数量: ${receivers.length}`, 'info');
                receivers.forEach((receiver, index) => {
                    if (receiver.track) {
                        addLog(`🔗 接收器${index}: ${receiver.track.kind} - ${receiver.track.readyState}`, 'info');
                    }
                });
            } else {
                addLog('❌ WebRTC连接未建立', 'error');
            }

            // 提供解决建议
            addLog('💡 === 解决建议 ===', 'info');
            addLog('💡 1. 确保设备端摄像头已启动', 'info');
            addLog('💡 2. 检查网络连接是否稳定', 'info');
            addLog('💡 3. 尝试刷新页面重新连接', 'info');
            addLog('💡 4. 检查浏览器是否支持WebRTC', 'info');
            addLog('💡 5. 如果是绿屏，可能是编解码器问题', 'info');
        }

        // 初始化函数
        function initializeApp() {
            checkBrowserSupport();
            updateButtons();
            addLog('智能家居App初始化完成', 'success');
            addLog('请先登录以开始使用', 'info');
        }

        // 等待DOM加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
    </script>
</body>
</html>
