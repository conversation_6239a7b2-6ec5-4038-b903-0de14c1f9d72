<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>音视频设备测试工具 (已修复)</title>
    <style>
        /* 样式与之前版本相同 */
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; margin: 0; padding: 2em; background-color: #f0f2f5; color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 2em; }
        h1, h2 { color: #0056b3; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        video { width: 100%; max-width: 720px; height: auto; border: 2px solid #ccc; background-color: #000; border-radius: 4px; }
        canvas { width: 100%; height: 100px; background-color: #2c3e50; border-radius: 4px; }
        .controls { margin: 15px 0; display: flex; align-items: center; flex-wrap: wrap; }
        select { padding: 8px; font-size: 1em; border-radius: 4px; margin-right: 10px; border: 1px solid #ccc; }
        button { padding: 8px 15px; font-size: 1em; color: #fff; background-color: #007bff; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.2s; }
        button:hover { background-color: #0056b3; }
        .status { margin-top: 15px; padding: 10px; border-radius: 4px; background-color: #e9ecef; }
        .error { color: #D8000C; background-color: #FFD2D2; }
    </style>
</head>
<body>

<div class="container">
    <h1>音视频设备测试工具 (已修复)</h1>
    <p>页面加载后会自动选择默认设备并请求权限。请在浏览器弹窗中点击“允许”。</p>

    <!-- 摄像头测试区 -->
    <div class="test-section">
        <h2>📷 摄像头测试</h2>
        <div class="controls">
            <label for="cameraSelect">选择摄像头:</label>
            <select id="cameraSelect"></select>
            <button id="testCameraButton">开始测试</button>
        </div>
        <video id="videoElement" autoplay playsinline></video>
        <div id="cameraStatus" class="status">状态：等待操作。</div>
    </div>

    <!-- 麦克风测试区 -->
    <div class="test-section">
        <h2>🎤 麦克风测试</h2>
        <div class="controls">
            <label for="micSelect">选择麦克风:</label>
            <select id="micSelect"></select>
            <button id="testMicButton">开始测试</button>
        </div>
        <canvas id="micVisualizer"></canvas>
        <div id="micStatus" class="status">状态：等待操作。</div>
    </div>
</div>

<script>
    // --- 配置项 ---
    const AUTO_START_TESTS = true;

    // --- 公共元素 ---
    const cameraSelect = document.getElementById('cameraSelect');
    const testCameraButton = document.getElementById('testCameraButton');
    const videoElement = document.getElementById('videoElement');
    const cameraStatus = document.getElementById('cameraStatus');
    const micSelect = document.getElementById('micSelect');
    const testMicButton = document.getElementById('testMicButton');
    const micVisualizer = document.getElementById('micVisualizer');
    const micStatus = document.getElementById('micStatus');

    // --- 状态变量 ---
    let currentCameraStream;
    let currentMicStream;
    let audioContext;
    let analyser;
    let visualizationId;
    // --- 新增一个标志位，防止无限循环 ---
    let isInitialized = false;

    // --- 设备发现与列表填充 ---
    async function populateDeviceLists() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();

            // 保存当前选择的值，以便刷新后恢复
            const currentCamId = cameraSelect.value;
            const currentMicId = micSelect.value;

            cameraSelect.innerHTML = '';
            micSelect.innerHTML = '';

            const videoDevices = devices.filter(d => d.kind === 'videoinput');
            const audioDevices = devices.filter(d => d.kind === 'audioinput');

            if (videoDevices.length > 0) populateSelect(cameraSelect, videoDevices);
            if (audioDevices.length > 0) populateSelect(micSelect, audioDevices);

            // 恢复之前的选择
            if (currentCamId) cameraSelect.value = currentCamId;
            if (currentMicId) micSelect.value = currentMicId;

            // --- 核心修复：只在第一次加载时执行自动设置和自动开始 ---
            if (!isInitialized) {
                isInitialized = true; // 立刻设置标志位，防止重入

                setDefaultDevices(); // 设置默认设备

                if (AUTO_START_TESTS) {
                    if (cameraSelect.options.length > 0) {
                        console.log("自动开始摄像头测试...");
                        testCameraButton.click();
                    }
                    if (micSelect.options.length > 0) {
                        console.log("自动开始麦克风测试...");
                        testMicButton.click();
                    }
                }
            }

        } catch (err) {
            handleError(err, cameraStatus);
            handleError(err, micStatus);
        }
    }

    // --- 设置默认设备的函数 (无改动) ---
    function setDefaultDevices() {
        if (cameraSelect.options.length > 0) {
            cameraSelect.selectedIndex = 0;
        }
        if (micSelect.options.length > 0) {
            let preferredMicIndex = Array.from(micSelect.options).findIndex(opt => opt.text.includes('HK USB'));
            micSelect.selectedIndex = preferredMicIndex !== -1 ? preferredMicIndex : 0;
        }
    }

    // --- populateSelect 辅助函数 (无改动) ---
    function populateSelect(selectElement, devices) {
        // ... (此处代码与上一版完全相同，为简洁省略)
        const labelCounts = {};
        devices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            let label = device.label || `${selectElement.id === 'cameraSelect' ? '摄像头' : '麦克风'} ${selectElement.options.length + 1}`;
            if (labelCounts[label]) {
                labelCounts[label]++;
                option.text = `${label} #${labelCounts[label]}`;
            } else {
                labelCounts[label] = 1;
                option.text = label;
            }
            selectElement.appendChild(option);
        });
    }

    // --- 核心功能：摄像头测试 (无改动) ---
    async function startCamera() {
        if (currentCameraStream) {
            currentCameraStream.getTracks().forEach(track => track.stop());
        }
        const deviceId = cameraSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { video: { deviceId: { exact: deviceId } }, audio: false };
        updateStatus(cameraStatus, '正在请求摄像头权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            videoElement.srcObject = stream;
            currentCameraStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(cameraStatus, `成功！摄像头 "${cameraSelect.options[cameraSelect.selectedIndex].text}" 已激活。`);
        } catch (err) {
            handleError(err, cameraStatus);
        }
    }

    // --- 核心功能：麦克风测试 (无改动) ---
    async function startMicrophone() {
        if (currentMicStream) {
            currentMicStream.getTracks().forEach(track => track.stop());
            if (visualizationId) cancelAnimationFrame(visualizationId);
        }
        const deviceId = micSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { audio: { deviceId: { exact: deviceId } }, video: false };
        updateStatus(micStatus, '正在请求麦克风权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            currentMicStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(micStatus, `成功！麦克风 "${micSelect.options[micSelect.selectedIndex].text}" 已激活。请对着麦克风说话。`);
            visualizeMicrophone(stream);
        } catch (err) {
            handleError(err, micStatus);
        }
    }

    // --- 其他辅助函数 (visualizeMicrophone, updateStatus, handleError) 与之前相同，此处省略以保持简洁 ---
    function visualizeMicrophone(stream) { if (!audioContext) { audioContext = new (window.AudioContext || window.webkitAudioContext)(); } analyser = audioContext.createAnalyser(); const source = audioContext.createMediaStreamSource(stream); source.connect(analyser); analyser.fftSize = 2048; const bufferLength = analyser.frequencyBinCount; const dataArray = new Uint8Array(bufferLength); const canvasCtx = micVisualizer.getContext('2d'); function draw() { visualizationId = requestAnimationFrame(draw); analyser.getByteTimeDomainData(dataArray); canvasCtx.fillStyle = '#2c3e50'; canvasCtx.fillRect(0, 0, micVisualizer.width, micVisualizer.height); canvasCtx.lineWidth = 2; canvasCtx.strokeStyle = '#3498db'; canvasCtx.beginPath(); const sliceWidth = micVisualizer.width * 1.0 / bufferLength; let x = 0; for (let i = 0; i < bufferLength; i++) { const v = dataArray[i] / 128.0; const y = v * micVisualizer.height / 2; if (i === 0) canvasCtx.moveTo(x, y); else canvasCtx.lineTo(x, y); x += sliceWidth; } canvasCtx.lineTo(micVisualizer.width, micVisualizer.height / 2); canvasCtx.stroke(); } draw(); }
    function updateStatus(element, message, isError = false) { element.textContent = `状态：${message}`; element.className = isError ? 'status error' : 'status'; }
    function handleError(err, statusElement) { let message = `错误: ${err.name}`; if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') { message = '错误: 您拒绝了浏览器访问设备的权限。'; } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') { message = '错误: 找不到指定的设备。'; } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') { message = '错误: 无法读取设备。可能已被其他程序占用或存在硬件问题。'; } updateStatus(statusElement, message, true); console.error('getUserMedia error:', err); }

    // --- 事件绑定和初始化 ---
    window.addEventListener('load', populateDeviceLists);
    testCameraButton.addEventListener('click', startCamera);
    testMicButton.addEventListener('click', startMicrophone);

</script>
</body>
</html><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>音视频设备测试工具 (已修复)</title>
    <style>
        /* 样式与之前版本相同 */
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; margin: 0; padding: 2em; background-color: #f0f2f5; color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 2em; }
        h1, h2 { color: #0056b3; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        video { width: 100%; max-width: 720px; height: auto; border: 2px solid #ccc; background-color: #000; border-radius: 4px; }
        canvas { width: 100%; height: 100px; background-color: #2c3e50; border-radius: 4px; }
        .controls { margin: 15px 0; display: flex; align-items: center; flex-wrap: wrap; }
        select { padding: 8px; font-size: 1em; border-radius: 4px; margin-right: 10px; border: 1px solid #ccc; }
        button { padding: 8px 15px; font-size: 1em; color: #fff; background-color: #007bff; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.2s; }
        button:hover { background-color: #0056b3; }
        .status { margin-top: 15px; padding: 10px; border-radius: 4px; background-color: #e9ecef; }
        .error { color: #D8000C; background-color: #FFD2D2; }
    </style>
</head>
<body>

<div class="container">
    <h1>音视频设备测试工具 (已修复)</h1>
    <p>页面加载后会自动选择默认设备并请求权限。请在浏览器弹窗中点击“允许”。</p>

    <!-- 摄像头测试区 -->
    <div class="test-section">
        <h2>📷 摄像头测试</h2>
        <div class="controls">
            <label for="cameraSelect">选择摄像头:</label>
            <select id="cameraSelect"></select>
            <button id="testCameraButton">开始测试</button>
        </div>
        <video id="videoElement" autoplay playsinline></video>
        <div id="cameraStatus" class="status">状态：等待操作。</div>
    </div>

    <!-- 麦克风测试区 -->
    <div class="test-section">
        <h2>🎤 麦克风测试</h2>
        <div class="controls">
            <label for="micSelect">选择麦克风:</label>
            <select id="micSelect"></select>
            <button id="testMicButton">开始测试</button>
        </div>
        <canvas id="micVisualizer"></canvas>
        <div id="micStatus" class="status">状态：等待操作。</div>
    </div>
</div>

<script>
    // --- 配置项 ---
    const AUTO_START_TESTS = true;

    // --- 公共元素 ---
    const cameraSelect = document.getElementById('cameraSelect');
    const testCameraButton = document.getElementById('testCameraButton');
    const videoElement = document.getElementById('videoElement');
    const cameraStatus = document.getElementById('cameraStatus');
    const micSelect = document.getElementById('micSelect');
    const testMicButton = document.getElementById('testMicButton');
    const micVisualizer = document.getElementById('micVisualizer');
    const micStatus = document.getElementById('micStatus');

    // --- 状态变量 ---
    let currentCameraStream;
    let currentMicStream;
    let audioContext;
    let analyser;
    let visualizationId;
    // --- 新增一个标志位，防止无限循环 ---
    let isInitialized = false;

    // --- 设备发现与列表填充 ---
    async function populateDeviceLists() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();

            // 保存当前选择的值，以便刷新后恢复
            const currentCamId = cameraSelect.value;
            const currentMicId = micSelect.value;

            cameraSelect.innerHTML = '';
            micSelect.innerHTML = '';

            const videoDevices = devices.filter(d => d.kind === 'videoinput');
            const audioDevices = devices.filter(d => d.kind === 'audioinput');

            if (videoDevices.length > 0) populateSelect(cameraSelect, videoDevices);
            if (audioDevices.length > 0) populateSelect(micSelect, audioDevices);

            // 恢复之前的选择
            if (currentCamId) cameraSelect.value = currentCamId;
            if (currentMicId) micSelect.value = currentMicId;

            // --- 核心修复：只在第一次加载时执行自动设置和自动开始 ---
            if (!isInitialized) {
                isInitialized = true; // 立刻设置标志位，防止重入

                setDefaultDevices(); // 设置默认设备

                if (AUTO_START_TESTS) {
                    if (cameraSelect.options.length > 0) {
                        console.log("自动开始摄像头测试...");
                        testCameraButton.click();
                    }
                    if (micSelect.options.length > 0) {
                        console.log("自动开始麦克风测试...");
                        testMicButton.click();
                    }
                }
            }

        } catch (err) {
            handleError(err, cameraStatus);
            handleError(err, micStatus);
        }
    }

    // --- 设置默认设备的函数 (无改动) ---
    function setDefaultDevices() {
        if (cameraSelect.options.length > 0) {
            cameraSelect.selectedIndex = 0;
        }
        if (micSelect.options.length > 0) {
            let preferredMicIndex = Array.from(micSelect.options).findIndex(opt => opt.text.includes('HK USB'));
            micSelect.selectedIndex = preferredMicIndex !== -1 ? preferredMicIndex : 0;
        }
    }

    // --- populateSelect 辅助函数 (无改动) ---
    function populateSelect(selectElement, devices) {
        // ... (此处代码与上一版完全相同，为简洁省略)
        const labelCounts = {};
        devices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            let label = device.label || `${selectElement.id === 'cameraSelect' ? '摄像头' : '麦克风'} ${selectElement.options.length + 1}`;
            if (labelCounts[label]) {
                labelCounts[label]++;
                option.text = `${label} #${labelCounts[label]}`;
            } else {
                labelCounts[label] = 1;
                option.text = label;
            }
            selectElement.appendChild(option);
        });
    }

    // --- 核心功能：摄像头测试 (无改动) ---
    async function startCamera() {
        if (currentCameraStream) {
            currentCameraStream.getTracks().forEach(track => track.stop());
        }
        const deviceId = cameraSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { video: { deviceId: { exact: deviceId } }, audio: false };
        updateStatus(cameraStatus, '正在请求摄像头权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            videoElement.srcObject = stream;
            currentCameraStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(cameraStatus, `成功！摄像头 "${cameraSelect.options[cameraSelect.selectedIndex].text}" 已激活。`);
        } catch (err) {
            handleError(err, cameraStatus);
        }
    }

    // --- 核心功能：麦克风测试 (无改动) ---
    async function startMicrophone() {
        if (currentMicStream) {
            currentMicStream.getTracks().forEach(track => track.stop());
            if (visualizationId) cancelAnimationFrame(visualizationId);
        }
        const deviceId = micSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { audio: { deviceId: { exact: deviceId } }, video: false };
        updateStatus(micStatus, '正在请求麦克风权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            currentMicStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(micStatus, `成功！麦克风 "${micSelect.options[micSelect.selectedIndex].text}" 已激活。请对着麦克风说话。`);
            visualizeMicrophone(stream);
        } catch (err) {
            handleError(err, micStatus);
        }
    }

    // --- 其他辅助函数 (visualizeMicrophone, updateStatus, handleError) 与之前相同，此处省略以保持简洁 ---
    function visualizeMicrophone(stream) { if (!audioContext) { audioContext = new (window.AudioContext || window.webkitAudioContext)(); } analyser = audioContext.createAnalyser(); const source = audioContext.createMediaStreamSource(stream); source.connect(analyser); analyser.fftSize = 2048; const bufferLength = analyser.frequencyBinCount; const dataArray = new Uint8Array(bufferLength); const canvasCtx = micVisualizer.getContext('2d'); function draw() { visualizationId = requestAnimationFrame(draw); analyser.getByteTimeDomainData(dataArray); canvasCtx.fillStyle = '#2c3e50'; canvasCtx.fillRect(0, 0, micVisualizer.width, micVisualizer.height); canvasCtx.lineWidth = 2; canvasCtx.strokeStyle = '#3498db'; canvasCtx.beginPath(); const sliceWidth = micVisualizer.width * 1.0 / bufferLength; let x = 0; for (let i = 0; i < bufferLength; i++) { const v = dataArray[i] / 128.0; const y = v * micVisualizer.height / 2; if (i === 0) canvasCtx.moveTo(x, y); else canvasCtx.lineTo(x, y); x += sliceWidth; } canvasCtx.lineTo(micVisualizer.width, micVisualizer.height / 2); canvasCtx.stroke(); } draw(); }
    function updateStatus(element, message, isError = false) { element.textContent = `状态：${message}`; element.className = isError ? 'status error' : 'status'; }
    function handleError(err, statusElement) { let message = `错误: ${err.name}`; if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') { message = '错误: 您拒绝了浏览器访问设备的权限。'; } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') { message = '错误: 找不到指定的设备。'; } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') { message = '错误: 无法读取设备。可能已被其他程序占用或存在硬件问题。'; } updateStatus(statusElement, message, true); console.error('getUserMedia error:', err); }

    // --- 事件绑定和初始化 ---
    window.addEventListener('load', populateDeviceLists);
    testCameraButton.addEventListener('click', startCamera);
    testMicButton.addEventListener('click', startMicrophone);

</script>
</body>
</html><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>音视频设备测试工具 (已修复)</title>
    <style>
        /* 样式与之前版本相同 */
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; margin: 0; padding: 2em; background-color: #f0f2f5; color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 2em; }
        h1, h2 { color: #0056b3; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        video { width: 100%; max-width: 720px; height: auto; border: 2px solid #ccc; background-color: #000; border-radius: 4px; }
        canvas { width: 100%; height: 100px; background-color: #2c3e50; border-radius: 4px; }
        .controls { margin: 15px 0; display: flex; align-items: center; flex-wrap: wrap; }
        select { padding: 8px; font-size: 1em; border-radius: 4px; margin-right: 10px; border: 1px solid #ccc; }
        button { padding: 8px 15px; font-size: 1em; color: #fff; background-color: #007bff; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.2s; }
        button:hover { background-color: #0056b3; }
        .status { margin-top: 15px; padding: 10px; border-radius: 4px; background-color: #e9ecef; }
        .error { color: #D8000C; background-color: #FFD2D2; }
    </style>
</head>
<body>

<div class="container">
    <h1>音视频设备测试工具 (已修复)</h1>
    <p>页面加载后会自动选择默认设备并请求权限。请在浏览器弹窗中点击“允许”。</p>

    <!-- 摄像头测试区 -->
    <div class="test-section">
        <h2>📷 摄像头测试</h2>
        <div class="controls">
            <label for="cameraSelect">选择摄像头:</label>
            <select id="cameraSelect"></select>
            <button id="testCameraButton">开始测试</button>
        </div>
        <video id="videoElement" autoplay playsinline></video>
        <div id="cameraStatus" class="status">状态：等待操作。</div>
    </div>

    <!-- 麦克风测试区 -->
    <div class="test-section">
        <h2>🎤 麦克风测试</h2>
        <div class="controls">
            <label for="micSelect">选择麦克风:</label>
            <select id="micSelect"></select>
            <button id="testMicButton">开始测试</button>
        </div>
        <canvas id="micVisualizer"></canvas>
        <div id="micStatus" class="status">状态：等待操作。</div>
    </div>
</div>

<script>
    // --- 配置项 ---
    const AUTO_START_TESTS = true;

    // --- 公共元素 ---
    const cameraSelect = document.getElementById('cameraSelect');
    const testCameraButton = document.getElementById('testCameraButton');
    const videoElement = document.getElementById('videoElement');
    const cameraStatus = document.getElementById('cameraStatus');
    const micSelect = document.getElementById('micSelect');
    const testMicButton = document.getElementById('testMicButton');
    const micVisualizer = document.getElementById('micVisualizer');
    const micStatus = document.getElementById('micStatus');

    // --- 状态变量 ---
    let currentCameraStream;
    let currentMicStream;
    let audioContext;
    let analyser;
    let visualizationId;
    // --- 新增一个标志位，防止无限循环 ---
    let isInitialized = false;

    // --- 设备发现与列表填充 ---
    async function populateDeviceLists() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();

            // 保存当前选择的值，以便刷新后恢复
            const currentCamId = cameraSelect.value;
            const currentMicId = micSelect.value;

            cameraSelect.innerHTML = '';
            micSelect.innerHTML = '';

            const videoDevices = devices.filter(d => d.kind === 'videoinput');
            const audioDevices = devices.filter(d => d.kind === 'audioinput');

            if (videoDevices.length > 0) populateSelect(cameraSelect, videoDevices);
            if (audioDevices.length > 0) populateSelect(micSelect, audioDevices);

            // 恢复之前的选择
            if (currentCamId) cameraSelect.value = currentCamId;
            if (currentMicId) micSelect.value = currentMicId;

            // --- 核心修复：只在第一次加载时执行自动设置和自动开始 ---
            if (!isInitialized) {
                isInitialized = true; // 立刻设置标志位，防止重入

                setDefaultDevices(); // 设置默认设备

                if (AUTO_START_TESTS) {
                    if (cameraSelect.options.length > 0) {
                        console.log("自动开始摄像头测试...");
                        testCameraButton.click();
                    }
                    if (micSelect.options.length > 0) {
                        console.log("自动开始麦克风测试...");
                        testMicButton.click();
                    }
                }
            }

        } catch (err) {
            handleError(err, cameraStatus);
            handleError(err, micStatus);
        }
    }

    // --- 设置默认设备的函数 (无改动) ---
    function setDefaultDevices() {
        if (cameraSelect.options.length > 0) {
            cameraSelect.selectedIndex = 0;
        }
        if (micSelect.options.length > 0) {
            let preferredMicIndex = Array.from(micSelect.options).findIndex(opt => opt.text.includes('HK USB'));
            micSelect.selectedIndex = preferredMicIndex !== -1 ? preferredMicIndex : 0;
        }
    }

    // --- populateSelect 辅助函数 (无改动) ---
    function populateSelect(selectElement, devices) {
        // ... (此处代码与上一版完全相同，为简洁省略)
        const labelCounts = {};
        devices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            let label = device.label || `${selectElement.id === 'cameraSelect' ? '摄像头' : '麦克风'} ${selectElement.options.length + 1}`;
            if (labelCounts[label]) {
                labelCounts[label]++;
                option.text = `${label} #${labelCounts[label]}`;
            } else {
                labelCounts[label] = 1;
                option.text = label;
            }
            selectElement.appendChild(option);
        });
    }

    // --- 核心功能：摄像头测试 (无改动) ---
    async function startCamera() {
        if (currentCameraStream) {
            currentCameraStream.getTracks().forEach(track => track.stop());
        }
        const deviceId = cameraSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { video: { deviceId: { exact: deviceId } }, audio: false };
        updateStatus(cameraStatus, '正在请求摄像头权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            videoElement.srcObject = stream;
            currentCameraStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(cameraStatus, `成功！摄像头 "${cameraSelect.options[cameraSelect.selectedIndex].text}" 已激活。`);
        } catch (err) {
            handleError(err, cameraStatus);
        }
    }

    // --- 核心功能：麦克风测试 (无改动) ---
    async function startMicrophone() {
        if (currentMicStream) {
            currentMicStream.getTracks().forEach(track => track.stop());
            if (visualizationId) cancelAnimationFrame(visualizationId);
        }
        const deviceId = micSelect.value;
        if (!deviceId) return; // 防止列表为空时出错
        const constraints = { audio: { deviceId: { exact: deviceId } }, video: false };
        updateStatus(micStatus, '正在请求麦克风权限...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            currentMicStream = stream;
            await populateDeviceLists(); // 刷新列表以获取完整标签
            updateStatus(micStatus, `成功！麦克风 "${micSelect.options[micSelect.selectedIndex].text}" 已激活。请对着麦克风说话。`);
            visualizeMicrophone(stream);
        } catch (err) {
            handleError(err, micStatus);
        }
    }

    // --- 其他辅助函数 (visualizeMicrophone, updateStatus, handleError) 与之前相同，此处省略以保持简洁 ---
    function visualizeMicrophone(stream) { if (!audioContext) { audioContext = new (window.AudioContext || window.webkitAudioContext)(); } analyser = audioContext.createAnalyser(); const source = audioContext.createMediaStreamSource(stream); source.connect(analyser); analyser.fftSize = 2048; const bufferLength = analyser.frequencyBinCount; const dataArray = new Uint8Array(bufferLength); const canvasCtx = micVisualizer.getContext('2d'); function draw() { visualizationId = requestAnimationFrame(draw); analyser.getByteTimeDomainData(dataArray); canvasCtx.fillStyle = '#2c3e50'; canvasCtx.fillRect(0, 0, micVisualizer.width, micVisualizer.height); canvasCtx.lineWidth = 2; canvasCtx.strokeStyle = '#3498db'; canvasCtx.beginPath(); const sliceWidth = micVisualizer.width * 1.0 / bufferLength; let x = 0; for (let i = 0; i < bufferLength; i++) { const v = dataArray[i] / 128.0; const y = v * micVisualizer.height / 2; if (i === 0) canvasCtx.moveTo(x, y); else canvasCtx.lineTo(x, y); x += sliceWidth; } canvasCtx.lineTo(micVisualizer.width, micVisualizer.height / 2); canvasCtx.stroke(); } draw(); }
    function updateStatus(element, message, isError = false) { element.textContent = `状态：${message}`; element.className = isError ? 'status error' : 'status'; }
    function handleError(err, statusElement) { let message = `错误: ${err.name}`; if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') { message = '错误: 您拒绝了浏览器访问设备的权限。'; } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') { message = '错误: 找不到指定的设备。'; } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') { message = '错误: 无法读取设备。可能已被其他程序占用或存在硬件问题。'; } updateStatus(statusElement, message, true); console.error('getUserMedia error:', err); }

    // --- 事件绑定和初始化 ---
    window.addEventListener('load', populateDeviceLists);
    testCameraButton.addEventListener('click', startCamera);
    testMicButton.addEventListener('click', startMicrophone);

</script>
</body>
</html>