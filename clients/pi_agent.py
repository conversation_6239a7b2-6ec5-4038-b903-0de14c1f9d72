# -*- coding: utf-8 -*-
import sys
import os
import json
import requests
from PySide6.QtWidgets import (
    QApplication,
    QSystemTrayIcon,
    QMenu,
)
from PySide6.QtGui import QIcon, QAction
from PySide6.QtCore import QSize, Qt, QCoreApplication, Signal, QObject, QTimer, QThread, Slot
from PySide6.QtQml import QQmlApplicationEngine, qmlRegisterType
from webrtc_agent import WebRTC_Agent

# 模拟一个全局配置，其中包含服务器地址
CONFIG = {
    "SERVER_BASE_URL": "http://192.168.1.212:8000"
}


# 定义一个API客户端类，用于与后端服务交互
class PiAgentAPI(QObject):
    # 信号用于向QML发送日志和状态更新
    log_signal = Signal(str)
    network_status_signal = Signal(str)

    def __init__(self, base_url):
        super().__init__()
        self.base_url = base_url
        self.device_id = "smart_camera_001"
        self.device_token = None
        self.device_connected = False
        self.heartbeat_timer = QTimer(self)
        self.heartbeat_timer.timeout.connect(self.send_heartbeat)

    def _log_info(self, message):
        self.log_signal.emit(f"[API] {message}")

    def _log_error(self, message):
        self.log_signal.emit(f"[API] ERROR: {message}")

    @Slot(str, str)
    def register_device(self, device_name, device_location):
        """注册设备"""
        self._log_info("正在注册设备...")
        url = f"{self.base_url}/api/v1/device/register"
        data = {
            "device_id": self.device_id,
            "device_name": device_name,
            "device_location": device_location,
            "device_type": "camera"
        }
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            result = response.json()
            self._log_info(f"设备注册成功: {result}")
            return result
        except requests.exceptions.RequestException as e:
            self._log_error(f"设备注册失败: {e}")
            return None

    @Slot()
    def connect_device(self):
        """连接设备按钮的槽函数"""
        if not self.device_connected:
            self.log_signal.emit("[系统] 正在连接服务器...")
            # 模拟连接成功
            self.device_connected = True
            self.network_status_signal.emit("在线")
            self.log_signal.emit("[系统] 服务器连接成功。")
            self.heartbeat_timer.start(30000)  # 每30秒发送一次心跳
        else:
            self.log_signal.emit("[系统] 设备已连接。")

    @Slot()
    def disconnect_device(self):
        """断开连接按钮的槽函数"""
        if self.device_connected:
            self.log_signal.emit("[系统] 正在断开服务器连接...")
            self.device_connected = False
            self.network_status_signal.emit("离线")
            self.log_signal.emit("[系统] 服务器连接已断开。")
            self.heartbeat_timer.stop()
        else:
            self.log_signal.emit("[系统] 设备已离线。")

    def send_heartbeat(self):
        """发送心跳的槽函数"""
        # 在这里调用 API 发送心跳
        self._log_info("正在发送心跳...")
        # self.api.send_heartbeat(status="connected") # 假设这个方法已在后端实现

    @Slot()
    def test_payment_flow(self):
        """支付测试流程"""
        self.log_signal.emit("[支付] 正在执行支付测试流程...")
        # ... 支付逻辑


def main():
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)

    # 创建系统托盘图标
    tray_icon = QSystemTrayIcon(QIcon("monitoring.svg"), app)
    tray_icon.setToolTip("智能设备代理")

    # 创建托盘菜单
    menu = QMenu()
    show_action = QAction("显示主窗口", app)
    quit_action = QAction("退出", app)
    menu.addAction(show_action)
    menu.addAction(quit_action)
    tray_icon.setContextMenu(menu)
    tray_icon.show()

    # 创建QML引擎
    engine = QQmlApplicationEngine()

    # 注册PiAgentAPI类型，供QML使用
    qmlRegisterType(PiAgentAPI, "com.example", 1, 0, "PiAgentAPI")

    # 创建后端API实例和WebRTC实例
    api_backend = PiAgentAPI(CONFIG["SERVER_BASE_URL"])
    webrtc_backend = WebRTC_Agent()

    # 将实例暴露给QML
    engine.rootContext().setContextProperty("api", api_backend)
    engine.rootContext().setContextProperty("webrtc", webrtc_backend)

    # 连接WebRTC的日志信号到主程序的日志信号
    webrtc_backend.webrtc_log_signal.connect(api_backend.log_signal)

    def handle_tray_click(reason):
        if reason == QSystemTrayIcon.Trigger:
            root_objects = engine.rootObjects()
            if root_objects:
                window = root_objects[0]
                window.show()

    tray_icon.activated.connect(handle_tray_click)
    show_action.triggered.connect(lambda: engine.rootObjects()[0].show())
    quit_action.triggered.connect(app.quit)

    engine.load(os.path.join(os.path.dirname(__file__), "main.qml"))

    if not engine.rootObjects():
        sys.exit(-1)

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
