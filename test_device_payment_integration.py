#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试设备端支付集成
验证设备客户端页面的支付功能是否正常工作
"""

import requests
import json
import time
from typing import Dict, Any

# 配置
BASE_URL = "http://localhost:8000"
DEVICE_ID = "smart_camera_001"

def test_device_payment_integration():
    """测试设备端支付集成"""
    print("🤖 测试设备端支付集成...")
    print("=" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False
    
    # 测试1: 模拟设备端创建订单
    print(f"\n📱 测试1: 模拟设备端创建订单")
    order_data = {
        "device_id": DEVICE_ID,
        "items": [
            {
                "product_id": "smart_service_001",
                "quantity": 1,
                "price": 0.01
            }
        ],
        "total_amount": 0.01
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/",
            json=order_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            order = response.json()
            order_id = order["id"]
            print(f"✅ 设备端订单创建成功")
            print(f"   订单ID: {order_id}")
            print(f"   设备ID: {order['device_id']}")
            print(f"   订单金额: ¥{order['total_amount']}")
            print(f"   订单状态: {order['status']}")
        else:
            print(f"❌ 订单创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 订单创建异常: {str(e)}")
        return False
    
    # 测试2: 模拟设备端发起支付
    print(f"\n💳 测试2: 模拟设备端发起支付")
    payment_data = {
        "payment_method": "alipay"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/{order_id}/pay",
            json=payment_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            payment_result = response.json()
            payment_url = payment_result.get('payment_url')
            print(f"✅ 设备端支付发起成功")
            print(f"   支付方式: {payment_result['payment_method']}")
            print(f"   支付链接长度: {len(payment_url)} 字符")
            print(f"   支付链接域名: {payment_url.split('/')[2] if payment_url else 'N/A'}")
            
            # 验证支付链接格式
            if payment_url and 'alipay' in payment_url:
                print(f"✅ 支付链接格式正确（包含支付宝域名）")
            else:
                print(f"⚠️ 支付链接格式可能有问题")
                
        else:
            print(f"❌ 支付发起失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 支付发起异常: {str(e)}")
        return False
    
    # 测试3: 模拟设备端查询支付状态
    print(f"\n🔍 测试3: 模拟设备端查询支付状态")
    
    for i in range(3):
        try:
            response = requests.get(
                f"{BASE_URL}/api/v1/orders/{order_id}/payment-status"
            )
            
            if response.status_code == 200:
                status_result = response.json()
                print(f"✅ 设备端状态查询成功 (第{i+1}次)")
                print(f"   订单状态: {status_result['status']}")
                print(f"   订单金额: ¥{status_result['total_amount']}")
                
                if status_result['status'] == 'paid':
                    print(f"🎉 检测到支付成功！")
                    break
                    
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
        
        if i < 2:
            print(f"   等待2秒后再次查询...")
            time.sleep(2)
    
    # 测试4: 验证设备客户端页面可访问性
    print(f"\n🌐 测试4: 验证设备客户端页面")
    
    try:
        response = requests.get(f"{BASE_URL}/clients/device_webrtc_client.html")
        
        if response.status_code == 200:
            html_content = response.text
            print(f"✅ 设备客户端页面可访问")
            print(f"   页面大小: {len(html_content)} 字符")
            
            # 检查支付相关功能是否存在
            payment_features = [
                'testOrderFlow',
                'testQuickPay', 
                'checkPaymentStatus',
                'openPaymentPage',
                '支付系统测试'
            ]
            
            missing_features = []
            for feature in payment_features:
                if feature in html_content:
                    print(f"   ✅ 包含功能: {feature}")
                else:
                    missing_features.append(feature)
                    print(f"   ❌ 缺失功能: {feature}")
            
            if not missing_features:
                print(f"✅ 所有支付功能都已集成到设备客户端")
            else:
                print(f"⚠️ 有 {len(missing_features)} 个功能可能缺失")
                
        else:
            print(f"❌ 设备客户端页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面访问异常: {str(e)}")
        return False
    
    return True


def generate_device_usage_guide():
    """生成设备使用指南"""
    print(f"\n📋 设备端支付使用指南")
    print("=" * 60)
    
    guide = """
🤖 设备端支付流程使用说明:

1. 📱 打开设备客户端页面:
   http://localhost:8000/clients/device_webrtc_client.html

2. 🔧 设备配置:
   - 输入设备ID (例如: smart_camera_001)
   - 输入设备名称 (例如: 客厅智能摄像头)
   - 点击"注册设备"

3. 💳 支付系统测试:
   - 点击"测试完整订单流程" - 完整的订单创建和支付流程
   - 点击"快速支付测试" - 快速创建订单并准备支付
   - 点击"查询支付状态" - 手动查询当前订单状态
   - 点击"打开支付页面" - 在新窗口打开支付宝支付页面

4. 🔄 自动化功能:
   - 系统会自动轮询支付状态（每5秒检查一次）
   - 支付成功后会自动停止轮询并显示成功消息
   - 支付信息面板会根据状态动态变化颜色

5. 💡 使用技巧:
   - 测试金额设置为 ¥0.01，方便测试
   - 支付页面会在新窗口打开，关闭窗口后自动检查状态
   - 所有操作都会在支付日志中显示详细信息

6. 🎯 实际部署建议:
   - 在生产环境中，将支付金额改为实际金额
   - 配置真实的支付宝应用参数
   - 添加更多的错误处理和用户提示
   - 可以根据设备类型定制不同的商品和价格
"""
    
    print(guide)


def main():
    """主函数"""
    print("🔧 设备端支付集成测试工具")
    print("=" * 60)
    
    # 运行集成测试
    success = test_device_payment_integration()
    
    if success:
        print(f"\n🎉 设备端支付集成测试完成！")
        print(f"✅ 所有功能正常工作")
        
        # 生成使用指南
        generate_device_usage_guide()
        
        print(f"\n🚀 现在可以打开设备客户端页面进行实际测试:")
        print(f"   http://localhost:8000/clients/device_webrtc_client.html")
        
    else:
        print(f"\n❌ 设备端支付集成测试失败")
        print(f"请检查服务器状态和API接口")


if __name__ == "__main__":
    main()
