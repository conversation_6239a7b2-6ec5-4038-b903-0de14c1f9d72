#!/usr/bin/env python3
"""
测试路由标签是否已经修复
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_openapi_tags():
    """测试OpenAPI文档中的标签"""
    print("🧪 测试OpenAPI文档标签")
    
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        
        if response.status_code == 200:
            openapi_spec = response.json()
            
            # 提取所有标签
            tags = set()
            
            # 从paths中提取标签
            for path, methods in openapi_spec.get("paths", {}).items():
                for method, spec in methods.items():
                    if "tags" in spec:
                        tags.update(spec["tags"])
            
            # 从tags定义中提取
            if "tags" in openapi_spec:
                for tag_def in openapi_spec["tags"]:
                    tags.add(tag_def["name"])
            
            print(f"✅ 成功获取OpenAPI文档")
            print(f"发现的标签: {sorted(list(tags))}")
            
            # 检查是否还有重复标签
            expected_tags = {
                "basic", "system",  # 基础和系统标签
                "Authentication", "Orders", "Staff", "Payment",  # 核心功能
                "WebRTC", "Device", "LLM", "Admin"  # 扩展功能
            }
            
            unexpected_tags = tags - expected_tags
            missing_tags = expected_tags - tags
            
            if unexpected_tags:
                print(f"⚠️  意外的标签: {unexpected_tags}")
            
            if missing_tags:
                print(f"⚠️  缺少的标签: {missing_tags}")
            
            if not unexpected_tags and not missing_tags:
                print("✅ 所有标签都符合预期")
            
            return tags
            
        else:
            print(f"❌ 获取OpenAPI文档失败: {response.status_code}")
            return set()
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return set()

def test_routes_info():
    """测试路由信息端点"""
    print("\n🧪 测试路由信息端点")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/system/routes")
        
        if response.status_code == 200:
            routes_info = response.json()
            
            print(f"✅ 成功获取路由信息")
            print(f"总模块数: {routes_info.get('total_modules')}")
            print(f"已注册模块数: {routes_info.get('registered_modules')}")
            
            print("\n注册的模块:")
            for module_name, module_info in routes_info.get("modules", {}).items():
                if module_info.get("registered"):
                    print(f"  ✅ {module_name}: {module_info.get('prefix')} (标签: {module_info.get('tags')})")
                else:
                    print(f"  ❌ {module_name}: 未注册")
            
            return routes_info
            
        else:
            print(f"❌ 获取路由信息失败: {response.status_code}")
            return {}
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return {}

def analyze_tag_consistency(openapi_tags, routes_info):
    """分析标签一致性"""
    print("\n📊 分析标签一致性")
    
    # 从路由信息中提取标签
    route_tags = set()
    for module_info in routes_info.get("modules", {}).values():
        if module_info.get("registered") and module_info.get("tags"):
            route_tags.update(module_info.get("tags"))
    
    print(f"OpenAPI标签: {sorted(list(openapi_tags))}")
    print(f"路由管理器标签: {sorted(list(route_tags))}")
    
    # 检查一致性
    only_in_openapi = openapi_tags - route_tags
    only_in_routes = route_tags - openapi_tags
    common_tags = openapi_tags & route_tags
    
    if only_in_openapi:
        print(f"⚠️  仅在OpenAPI中的标签: {only_in_openapi}")
    
    if only_in_routes:
        print(f"⚠️  仅在路由管理器中的标签: {only_in_routes}")
    
    print(f"✅ 共同标签: {sorted(list(common_tags))}")
    
    if not only_in_openapi and not only_in_routes:
        print("🎉 标签完全一致！")
        return True
    else:
        print("⚠️  标签存在不一致")
        return False

def main():
    """主测试函数"""
    print("🚀 开始路由标签测试")
    print("="*60)
    
    # 测试OpenAPI标签
    openapi_tags = test_openapi_tags()
    
    # 测试路由信息
    routes_info = test_routes_info()
    
    # 分析一致性
    if openapi_tags and routes_info:
        is_consistent = analyze_tag_consistency(openapi_tags, routes_info)
        
        if is_consistent:
            print("\n🎉 路由标签修复成功！")
            print("✅ 不再有重复的标签")
            print("✅ 标签命名统一规范")
            print("✅ 文档结构清晰")
        else:
            print("\n⚠️  标签仍需进一步调整")
    
    print(f"\n📋 建议访问以下链接查看修复结果:")
    print(f"  - Swagger文档: {BASE_URL}/docs")
    print(f"  - ReDoc文档: {BASE_URL}/redoc")
    print(f"  - 路由信息: {BASE_URL}/api/v1/system/routes")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
