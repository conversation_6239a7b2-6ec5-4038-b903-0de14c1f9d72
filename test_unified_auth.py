#!/usr/bin/env python3
"""
测试统一认证设计
验证所有角色都能使用统一登录接口
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_unified_login():
    """测试统一登录接口"""
    print("🧪 测试统一登录接口")
    
    # 测试现有用户登录
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 统一登录成功!")
            print(f"用户名: {result.get('username')}")
            print(f"角色: {result.get('role')}")
            print(f"用户ID: {result.get('user_id')}")
            print(f"令牌类型: {result.get('token_type')}")
            print(f"过期时间: {result.get('expires_in')}秒")
            return result.get("access_token"), result.get("role")
        else:
            print(f"❌ 登录失败: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None, None

def test_admin_login_removed():
    """测试admin专用登录接口是否已删除"""
    print("\n🧪 测试admin专用登录接口是否已删除")
    
    login_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/login", json=login_data)
        print(f"admin登录状态码: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ admin专用登录接口已正确删除")
            return True
        else:
            print(f"⚠️  admin登录接口仍然存在: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def create_test_users():
    """创建不同角色的测试用户"""
    print("\n🧪 创建不同角色的测试用户")
    
    test_users = [
        {"username": "staff_user", "password": "staffpass", "role": "staff"},
        {"username": "admin_user", "password": "adminpass", "role": "admin"},
        {"username": "app_user", "password": "apppass", "role": "webrtc_app_user"}
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            response = requests.post(f"{BASE_URL}/auth/register", json=user_data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 创建{user_data['role']}用户成功: {result.get('username')}")
                created_users.append(user_data)
            else:
                if "already registered" in response.text or "already exists" in response.text:
                    print(f"ℹ️  {user_data['role']}用户已存在: {user_data['username']}")
                    created_users.append(user_data)  # 即使已存在也添加到列表中用于测试
                else:
                    print(f"❌ 创建{user_data['role']}用户失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 创建用户请求失败: {str(e)}")
    
    return created_users

def test_role_based_login(users):
    """测试不同角色的登录"""
    print("\n🧪 测试不同角色的统一登录")
    
    login_results = {}
    
    for user_data in users:
        print(f"\n--- 测试{user_data['role']}登录 ---")
        
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        
        try:
            response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {user_data['role']}登录成功")
                print(f"  用户名: {result.get('username')}")
                print(f"  角色: {result.get('role')}")
                print(f"  令牌: {result.get('access_token', '')[:20]}...")
                
                login_results[user_data['role']] = {
                    "success": True,
                    "token": result.get('access_token'),
                    "user_info": result
                }
            else:
                print(f"❌ {user_data['role']}登录失败: {response.text}")
                login_results[user_data['role']] = {"success": False}
                
        except Exception as e:
            print(f"❌ {user_data['role']}登录请求失败: {str(e)}")
            login_results[user_data['role']] = {"success": False}
    
    return login_results

def test_role_based_access(login_results):
    """测试基于角色的访问控制"""
    print("\n🧪 测试基于角色的访问控制")
    
    # 测试员工接口访问
    print("\n--- 测试员工接口访问 ---")
    
    for role, result in login_results.items():
        if not result.get("success"):
            continue
            
        token = result.get("token")
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            # 测试员工活动订单接口
            response = requests.get(f"{BASE_URL}/staff/orders/active", headers=headers)
            
            if role in ["staff", "admin"]:
                if response.status_code in [200, 404]:  # 200=有数据, 404=无数据但有权限
                    print(f"✅ {role}可以访问员工接口")
                else:
                    print(f"❌ {role}访问员工接口失败: {response.status_code}")
            else:
                if response.status_code == 403:
                    print(f"✅ {role}正确被拒绝访问员工接口")
                else:
                    print(f"⚠️  {role}应该被拒绝但返回: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ {role}访问测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 开始统一认证设计测试")
    print("="*60)
    
    # 1. 测试统一登录接口
    token, role = test_unified_login()
    
    # 2. 测试admin专用登录接口是否已删除
    admin_removed = test_admin_login_removed()
    
    # 3. 创建不同角色的测试用户
    test_users = create_test_users()
    
    # 4. 测试不同角色的登录
    login_results = test_role_based_login(test_users)
    
    # 5. 测试基于角色的访问控制
    test_role_based_access(login_results)
    
    # 总结
    print("\n" + "="*60)
    print("📊 统一认证设计测试总结:")
    
    success_count = 0
    total_tests = 4
    
    if token:
        print("✅ 统一登录接口正常工作")
        success_count += 1
    else:
        print("❌ 统一登录接口有问题")
    
    if admin_removed:
        print("✅ admin专用登录接口已正确删除")
        success_count += 1
    else:
        print("❌ admin专用登录接口仍然存在")
    
    successful_logins = sum(1 for result in login_results.values() if result.get("success"))
    if successful_logins >= 2:
        print(f"✅ 多角色登录正常 ({successful_logins}个角色)")
        success_count += 1
    else:
        print(f"❌ 多角色登录有问题 (仅{successful_logins}个角色成功)")
    
    if len(test_users) >= 2:
        print("✅ 角色权限控制测试完成")
        success_count += 1
    else:
        print("❌ 角色权限控制测试不完整")
    
    print(f"\n总计: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("\n🎉 统一认证设计实施成功!")
        print("✅ 符合KISS原则")
        print("✅ 消除了接口重复")
        print("✅ 统一了认证流程")
        print("✅ 简化了维护工作")
    else:
        print(f"\n⚠️  还有 {total_tests - success_count} 个问题需要解决")
    
    print("\n📋 设计文档: docs/AUTHENTICATION_DESIGN.md")

if __name__ == "__main__":
    main()
