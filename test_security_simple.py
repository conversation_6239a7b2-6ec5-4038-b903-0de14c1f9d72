#!/usr/bin/env python3
"""
简化的安全修复验证测试脚本
使用内置的urllib和websocket-client库
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import sys
import time

# 测试配置
BASE_URL = "http://localhost:8000"

def test_user_login():
    """测试用户登录"""
    print("\n📋 测试1: 用户登录认证")
    try:
        data = json.dumps({"username": "testuser", "password": "testpass"}).encode('utf-8')
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/auth/login",
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            if response.status == 200:
                result = json.loads(response.read().decode('utf-8'))
                token = result.get("access_token")
                print("✅ 用户登录成功")
                return token
            else:
                print(f"❌ 用户登录失败: {response.status}")
                return None
    except Exception as e:
        print(f"❌ 用户登录异常: {e}")
        return None

def test_device_register():
    """测试设备注册"""
    print("\n📋 测试2: 设备注册")
    try:
        data = json.dumps({
            "device_id": "test_device_001",
            "device_name": "测试设备",
            "device_type": "smart_camera"
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/device/register",
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            if response.status == 200:
                result = json.loads(response.read().decode('utf-8'))
                token = result.get("device_token")
                print("✅ 设备注册成功")
                return token
            else:
                print(f"❌ 设备注册失败: {response.status}")
                return None
    except Exception as e:
        print(f"❌ 设备注册异常: {e}")
        return None

def test_secure_http_endpoint(token):
    """测试HTTP接口的安全认证"""
    print("\n📋 测试3: HTTP接口安全认证")
    
    if not token:
        print("❌ 缺少用户token，跳过测试")
        return False
        
    try:
        req = urllib.request.Request(
            f"{BASE_URL}/api/v1/webrtc/devices",
            headers={'Authorization': f'Bearer {token}'}
        )
        
        with urllib.request.urlopen(req) as response:
            if response.status in [200, 404]:  # 404也是正常的，说明认证通过了
                print("✅ /api/v1/webrtc/devices - 认证成功")
                return True
            else:
                print(f"❌ /api/v1/webrtc/devices - 认证失败: {response.status}")
                return False
    except urllib.error.HTTPError as e:
        if e.code in [200, 404]:
            print("✅ /api/v1/webrtc/devices - 认证成功")
            return True
        else:
            print(f"❌ /api/v1/webrtc/devices - 认证失败: {e.code}")
            return False
    except Exception as e:
        print(f"❌ /api/v1/webrtc/devices - 请求异常: {e}")
        return False

def test_insecure_http_endpoint():
    """测试不安全的HTTP请求是否被拒绝"""
    print("\n📋 测试4: 验证不安全请求被拒绝")
    
    try:
        req = urllib.request.Request(f"{BASE_URL}/api/v1/webrtc/devices")
        
        with urllib.request.urlopen(req) as response:
            print(f"❌ /api/v1/webrtc/devices - 未正确拒绝未授权请求: {response.status}")
            return False
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 应该返回401未授权
            print("✅ /api/v1/webrtc/devices - 正确拒绝未授权请求")
            return True
        else:
            print(f"❌ /api/v1/webrtc/devices - 错误的状态码: {e.code}")
            return False
    except Exception as e:
        print(f"❌ /api/v1/webrtc/devices - 请求异常: {e}")
        return False

def test_basic_endpoints():
    """测试基础端点"""
    print("\n📋 测试5: 基础端点测试")
    
    try:
        # 测试根端点
        with urllib.request.urlopen(f"{BASE_URL}/") as response:
            if response.status == 200:
                print("✅ 根端点正常")
                return True
            else:
                print(f"❌ 根端点异常: {response.status}")
                return False
    except Exception as e:
        print(f"❌ 根端点异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始安全修复验证测试")
    print("=" * 60)
    
    results = {}
    
    # 基础测试
    results["basic_endpoints"] = test_basic_endpoints()
    
    # 认证测试
    user_token = test_user_login()
    results["user_login"] = user_token is not None
    
    device_token = test_device_register()
    results["device_register"] = device_token is not None
    
    # HTTP接口安全测试
    results["secure_http"] = test_secure_http_endpoint(user_token)
    results["insecure_http"] = test_insecure_http_endpoint()
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} - {status}")
        
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 基础安全修复测试通过！")
        print("\n💡 注意：WebSocket测试需要websocket-client库，可以手动测试客户端页面")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
