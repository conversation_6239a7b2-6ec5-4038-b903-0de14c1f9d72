"""
路由管理器
提供模块化的路由注册和管理功能
"""

from typing import List, Dict, Any, Optional
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from config import settings
from utils.logger import get_logger
from utils.performance import timing_decorator, performance_monitor

logger = get_logger(__name__)


class RouterModule:
    """路由模块定义"""
    
    def __init__(
        self,
        name: str,
        router: APIRouter,
        prefix: str = "",
        tags: Optional[List[str]] = None,
        dependencies: Optional[List] = None,
        enabled: bool = True,
        description: str = ""
    ):
        self.name = name
        self.router = router
        self.prefix = prefix
        self.tags = tags or []
        self.dependencies = dependencies or []
        self.enabled = enabled
        self.description = description


class RouterManager:
    """路由管理器"""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.modules: Dict[str, RouterModule] = {}
        self.registered_modules: List[str] = []
        
    def register_module(self, module: RouterModule):
        """注册路由模块"""
        if not module.enabled:
            logger.info(f"路由模块 {module.name} 已禁用，跳过注册")
            return
        
        self.modules[module.name] = module
        logger.info(f"注册路由模块: {module.name} ({module.description})")
    
    def register_all_modules(self):
        """注册所有路由模块"""
        # 核心功能模块（始终启用）
        core_modules = self._get_core_modules()
        
        # 扩展功能模块（根据配置启用）
        extension_modules = self._get_extension_modules()
        
        # 管理功能模块（根据配置启用）
        admin_modules = self._get_admin_modules()
        
        all_modules = core_modules + extension_modules + admin_modules
        
        for module in all_modules:
            self.register_module(module)
        
        # 实际注册到FastAPI应用
        self._register_to_app()
    
    def _get_core_modules(self) -> List[RouterModule]:
        """获取核心功能模块"""
        modules = []
        
        # 订单系统（核心）
        if settings.is_feature_enabled("order_system"):
            from routers.order import router as order_router
            modules.append(RouterModule(
                name="order",
                router=order_router,
                prefix="/api/v1/orders",
                tags=["Orders"],
                description="订单管理系统"
            ))

        # 员工操作（核心）
        if settings.is_feature_enabled("order_system"):
            from routers.staff import router as staff_router
            modules.append(RouterModule(
                name="staff",
                router=staff_router,
                prefix="/api/v1/staff",
                tags=["Staff"],
                description="员工操作接口"
            ))

        # 支付系统（核心）
        if settings.is_feature_enabled("payment_system"):
            from routers.shop import router as shop_router
            modules.append(RouterModule(
                name="shop",
                router=shop_router,
                prefix="/api/v1/shop",
                tags=["Payment"],
                description="支付处理系统"
            ))

        # 用户认证（核心）
        from routers.auth import router as auth_router
        modules.append(RouterModule(
            name="auth",
            router=auth_router,
            prefix="/api/v1/auth",
            tags=["Authentication"],
            description="用户认证系统"
        ))
        
        return modules
    
    def _get_extension_modules(self) -> List[RouterModule]:
        """获取扩展功能模块"""
        modules = []
        
        # WebRTC功能
        if settings.is_feature_enabled("webrtc"):
            try:
                from routers.webrtc import router as webrtc_router
                modules.append(RouterModule(
                    name="webrtc",
                    router=webrtc_router,
                    prefix="/api/v1/webrtc",
                    tags=["WebRTC"],
                    description="WebRTC视频通话系统"
                ))
            except ImportError as e:
                logger.warning(f"WebRTC模块导入失败: {e}")

        # 设备管理
        try:
            from routers.device import router as device_router
            modules.append(RouterModule(
                name="device",
                router=device_router,
                prefix="/api/v1/device",
                tags=["Device"],
                description="设备管理系统"
            ))
        except ImportError as e:
            logger.warning(f"设备管理模块导入失败: {e}")

        # LLM集成
        if settings.is_feature_enabled("llm_integration"):
            try:
                from routers.llm import router as llm_router
                modules.append(RouterModule(
                    name="llm",
                    router=llm_router,
                    prefix="/api/v1/llm",
                    tags=["LLM"],
                    description="LLM集成接口"
                ))
            except ImportError as e:
                logger.warning(f"LLM模块导入失败: {e}")
        
        return modules
    
    def _get_admin_modules(self) -> List[RouterModule]:
        """获取管理功能模块"""
        modules = []
        
        # 管理员功能
        if settings.is_feature_enabled("admin_panel"):
            try:
                from routers.admin import router as admin_router
                modules.append(RouterModule(
                    name="admin",
                    router=admin_router,
                    prefix="/api/v1/admin",
                    tags=["Admin"],
                    description="管理员操作面板"
                ))
            except ImportError as e:
                logger.warning(f"管理员模块导入失败: {e}")
        
        return modules
    
    def _register_to_app(self):
        """将模块注册到FastAPI应用"""
        for name, module in self.modules.items():
            try:
                self.app.include_router(
                    module.router,
                    prefix=module.prefix,
                    tags=module.tags,
                    dependencies=module.dependencies
                )
                self.registered_modules.append(name)
                logger.info(f"✅ 路由模块注册成功: {name} -> {module.prefix}")
                
                # 记录性能指标
                performance_monitor.record_metric("router.registered", 1, {"module": name})
                
            except Exception as e:
                logger.error(f"❌ 路由模块注册失败: {name}, 错误: {str(e)}")
    
    def get_registered_modules(self) -> List[str]:
        """获取已注册的模块列表"""
        return self.registered_modules.copy()
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            "total_modules": len(self.modules),
            "registered_modules": len(self.registered_modules),
            "modules": {
                name: {
                    "prefix": module.prefix,
                    "tags": module.tags,
                    "description": module.description,
                    "enabled": module.enabled,
                    "registered": name in self.registered_modules
                }
                for name, module in self.modules.items()
            }
        }


def setup_middleware(app: FastAPI):
    """设置中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 可信主机中间件（生产环境建议启用）
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
        )
    
    logger.info("中间件设置完成")


def setup_basic_routes(app: FastAPI):
    """设置基础路由"""
    
    @app.get("/", tags=["basic"])
    @timing_decorator("basic.root")
    async def root():
        """根路径"""
        return {
            "message": f"欢迎使用 {settings.app_name}",
            "version": settings.app_version,
            "status": "running"
        }
    
    @app.get("/health", tags=["basic"])
    @timing_decorator("basic.health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "timestamp": "2025-08-04T00:00:00Z",
            "version": settings.app_version
        }
    
    @app.get("/ping", tags=["basic"])
    @timing_decorator("basic.ping")
    async def ping():
        """简单ping测试"""
        return {"message": "pong"}
    
    @app.get("/info", tags=["basic"])
    @timing_decorator("basic.info")
    async def app_info():
        """应用信息"""
        return {
            "app_name": settings.app_name,
            "version": settings.app_version,
            "debug": settings.debug,
            "features": {
                "order_system": settings.is_feature_enabled("order_system"),
                "payment_system": settings.is_feature_enabled("payment_system"),
                "webrtc": settings.is_feature_enabled("webrtc"),
                "llm_integration": settings.is_feature_enabled("llm_integration"),
                "admin_panel": settings.is_feature_enabled("admin_panel"),
            }
        }
    
    logger.info("基础路由设置完成")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="AI订单履约系统 - 从订单接收到原料处理完成的全过程管理",
        debug=settings.debug,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None
    )
    
    # 设置中间件
    setup_middleware(app)
    
    # 设置基础路由
    setup_basic_routes(app)
    
    # 创建路由管理器并注册所有模块
    router_manager = RouterManager(app)
    router_manager.register_all_modules()
    
    # 添加路由信息端点
    @app.get("/api/v1/system/routes", tags=["system"])
    async def get_routes_info():
        """获取路由信息"""
        return router_manager.get_module_info()
    
    logger.info(f"FastAPI应用创建完成，已注册模块: {router_manager.get_registered_modules()}")
    
    return app
