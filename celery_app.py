"""
Celery应用配置
用于异步任务处理，特别是订单履约流程中的通知任务
"""

from celery import Celery
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

# 创建Celery应用实例
celery_app = Celery(
    "robot_order_system",
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=[
        'tasks.notification_tasks',
        'tasks.order_tasks'
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 任务路由
    task_routes={
        'tasks.notification_tasks.*': {'queue': 'notifications'},
        'tasks.order_tasks.*': {'queue': 'orders'},
    },
    
    # 任务结果过期时间
    result_expires=3600,
    
    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 任务时间限制
    task_time_limit=300,  # 5分钟硬限制
    task_soft_time_limit=240,  # 4分钟软限制
    
    # 任务压缩
    task_compression='gzip',
    result_compression='gzip',
    
    # 监控
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 任务发现
celery_app.autodiscover_tasks()

@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    logger.info("Celery debug task executed successfully")
    return "Debug task completed"

if __name__ == '__main__':
    celery_app.start()
