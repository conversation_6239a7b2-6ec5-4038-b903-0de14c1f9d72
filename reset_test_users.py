#!/usr/bin/env python3
"""
重置测试用户
"""

from sqlmodel import Session, select, create_engine
from models.user import User
from utils.security import get_password_hash
from config import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def reset_test_users():
    """重置所有测试用户"""
    print("🔧 重置测试用户")
    
    test_users = [
        {"username": "testuser", "password": "testpass", "role": "webrtc_app_user"},
        {"username": "staff_user", "password": "staffpass", "role": "staff"},
        {"username": "admin_user", "password": "adminpass", "role": "admin"},
        {"username": "app_user", "password": "apppass", "role": "webrtc_app_user"}
    ]
    
    try:
        engine = create_engine(settings.database_url)
        
        with Session(engine) as session:
            for user_data in test_users:
                username = user_data["username"]
                password = user_data["password"]
                role = user_data["role"]
                
                # 查找现有用户
                statement = select(User).where(User.username == username)
                existing_user = session.exec(statement).first()
                
                if existing_user:
                    print(f"更新现有用户: {username}")
                    existing_user.password_hash = get_password_hash(password)
                    existing_user.role = role
                    existing_user.is_active = True
                else:
                    print(f"创建新用户: {username}")
                    import uuid
                    new_user = User(
                        id=str(uuid.uuid4()),
                        username=username,
                        password_hash=get_password_hash(password),
                        role=role,
                        is_active=True
                    )
                    session.add(new_user)
                
                session.commit()
                print(f"✅ 用户 {username} (角色: {role}) 已就绪")
            
            print("\n✅ 所有测试用户已重置完成")
            
    except Exception as e:
        print(f"❌ 重置用户失败: {str(e)}")
        logger.error(f"Reset users error: {str(e)}")

def list_all_users():
    """列出所有用户"""
    print("\n📋 数据库中的所有用户:")
    
    try:
        engine = create_engine(settings.database_url)
        
        with Session(engine) as session:
            statement = select(User)
            users = session.exec(statement).all()
            
            if users:
                for user in users:
                    print(f"  - {user.username} (角色: {user.role}, 活跃: {user.is_active})")
            else:
                print("  没有找到任何用户")
                
    except Exception as e:
        print(f"❌ 查询用户失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始重置测试用户")
    print("="*50)
    
    # 列出现有用户
    list_all_users()
    
    # 重置测试用户
    reset_test_users()
    
    # 再次列出用户
    list_all_users()
    
    print("\n✅ 重置完成，现在可以使用以下账号测试:")
    print("  - testuser/testpass (webrtc_app_user)")
    print("  - staff_user/staffpass (staff)")
    print("  - admin_user/adminpass (admin)")
    print("  - app_user/apppass (webrtc_app_user)")

if __name__ == "__main__":
    main()
