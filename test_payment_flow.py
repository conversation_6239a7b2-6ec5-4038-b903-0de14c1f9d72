#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
支付流程测试脚本
测试支付宝支付的完整流程
"""

import requests
import json
import time
from typing import Dict, Any

# 配置
BASE_URL = "http://localhost:8000"
DEVICE_ID = "test_device_001"

def test_payment_flow():
    """测试完整的支付流程"""
    print("🚀 开始测试支付流程...")
    
    # 步骤1: 创建订单
    print("\n📝 步骤1: 创建订单")
    order_data = {
        "device_id": DEVICE_ID,
        "items": [
            {
                "product_id": "test_product_001",
                "quantity": 2,
                "price": 10.0
            }
        ],
        "total_amount": 20.0
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/",
            json=order_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            order = response.json()
            order_id = order["id"]
            print(f"✅ 订单创建成功: {order_id}")
            print(f"   订单状态: {order['status']}")
            print(f"   订单金额: {order['total_amount']}")
        else:
            print(f"❌ 订单创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 订单创建异常: {str(e)}")
        return
    
    # 步骤2: 发起支付
    print(f"\n💳 步骤2: 发起支付 (订单ID: {order_id})")
    payment_data = {
        "payment_method": "alipay"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/orders/{order_id}/pay",
            json=payment_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            payment_result = response.json()
            print(f"✅ 支付发起成功")
            print(f"   支付方式: {payment_result['payment_method']}")
            print(f"   支付链接: {payment_result.get('payment_url', 'N/A')}")
            print(f"   二维码: {payment_result.get('qr_code', 'N/A')}")
        else:
            print(f"❌ 支付发起失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 支付发起异常: {str(e)}")
        return
    
    # 步骤3: 查询支付状态
    print(f"\n🔍 步骤3: 查询支付状态")
    
    for i in range(3):  # 模拟轮询3次
        try:
            response = requests.get(
                f"{BASE_URL}/api/v1/orders/{order_id}/payment-status"
            )
            
            if response.status_code == 200:
                status_result = response.json()
                print(f"✅ 查询成功 (第{i+1}次)")
                print(f"   订单状态: {status_result['status']}")
                print(f"   订单金额: {status_result['total_amount']}")
                print(f"   创建时间: {status_result['created_at']}")
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 状态查询异常: {str(e)}")
        
        if i < 2:  # 不是最后一次
            print("   等待2秒后再次查询...")
            time.sleep(2)
    
    # 步骤4: 获取订单详情
    print(f"\n📋 步骤4: 获取订单详情")
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/orders/{order_id}"
        )
        
        if response.status_code == 200:
            order_details = response.json()
            print(f"✅ 订单详情获取成功")
            print(f"   订单ID: {order_details['id']}")
            print(f"   设备ID: {order_details['device_id']}")
            print(f"   订单状态: {order_details['status']}")
            print(f"   订单金额: {order_details['total_amount']}")
        else:
            print(f"❌ 订单详情获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 订单详情获取异常: {str(e)}")
    
    print("\n🎉 支付流程测试完成!")


def test_error_cases():
    """测试错误情况"""
    print("\n🧪 测试错误情况...")
    
    # 测试不存在的订单
    print("\n❌ 测试1: 查询不存在的订单")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/orders/non_existent_order")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 404:
            print("   ✅ 正确返回404错误")
        else:
            print(f"   ❌ 期望404，实际返回: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    # 测试无效的支付方式
    print("\n❌ 测试2: 使用无效的支付方式")
    order_data = {
        "device_id": DEVICE_ID,
        "items": [{"product_id": "test", "quantity": 1, "price": 1.0}],
        "total_amount": 1.0
    }
    
    try:
        # 先创建订单
        response = requests.post(f"{BASE_URL}/api/v1/orders/", json=order_data)
        if response.status_code == 200:
            order_id = response.json()["id"]
            
            # 使用无效支付方式
            payment_data = {"payment_method": "invalid_method"}
            response = requests.post(
                f"{BASE_URL}/api/v1/orders/{order_id}/pay",
                json=payment_data
            )
            print(f"   状态码: {response.status_code}")
            if response.status_code == 400:
                print("   ✅ 正确返回400错误")
            else:
                print(f"   ❌ 期望400，实际返回: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")


if __name__ == "__main__":
    print("🔧 支付宝支付流程测试工具")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            exit(1)
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print("请确保FastAPI服务器正在运行 (python main.py)")
        exit(1)
    
    # 运行测试
    test_payment_flow()
    test_error_cases()
    
    print("\n" + "=" * 50)
    print("📝 测试说明:")
    print("1. 此测试验证了订单创建、支付发起、状态查询的完整流程")
    print("2. 支付宝支付需要正确的配置才能生成真实的支付链接")
    print("3. 在生产环境中，需要配置真实的支付宝应用参数")
    print("4. 客户可以参考这个流程添加微信支付实现")
