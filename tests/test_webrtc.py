"""
WebRTC模块测试
测试WebRTC连接、会话管理和消息处理功能
"""

import pytest
import json
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import WebSocket


class TestWebRTCAPI:
    """WebRTC API端点测试"""
    
    def test_health_check(self, client: TestClient):
        """测试健康检查端点"""
        response = client.get("/api/v1/webrtc/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "process_id" in data
    
    def test_get_features(self, client: TestClient):
        """测试获取功能列表"""
        response = client.get("/api/v1/webrtc/features")
        assert response.status_code == 200
        
        data = response.json()
        assert "connection_types" in data
        assert "feature_modules" in data
        assert len(data["connection_types"]) > 0
        assert len(data["feature_modules"]) > 0
    
    def test_get_stats(self, client: TestClient):
        """测试获取统计信息"""
        response = client.get("/api/v1/webrtc/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "total_connections" in data
        assert "total_sessions" in data
        assert "timestamp" in data
    
    def test_get_connections(self, client: TestClient):
        """测试获取连接列表"""
        response = client.get("/api/v1/webrtc/connections")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "total_connections" in data
        assert "connections" in data
        assert isinstance(data["connections"], dict)


class TestWebRTCConnectionManager:
    """WebRTC连接管理器测试"""
    
    @pytest.fixture
    def connection_manager(self, mock_redis_service):
        """创建连接管理器实例"""
        from routers.webrtc import get_webrtc_redis
        
        # 模拟Redis服务
        with patch('routers.webrtc.get_webrtc_redis', return_value=mock_redis_service):
            yield mock_redis_service
    
    @pytest.mark.asyncio
    async def test_add_connection(self, connection_manager):
        """测试添加连接"""
        connection_id = "test_connection_001"
        connection_info = {
            "participant_type": "device",
            "target_id": "test_user",
            "connection_type": "video",
            "session_id": "test_session"
        }
        
        await connection_manager.add_connection(connection_id, connection_info)
        
        # 验证连接已添加
        retrieved_info = await connection_manager.get_connection(connection_id)
        assert retrieved_info == connection_info
    
    @pytest.mark.asyncio
    async def test_remove_connection(self, connection_manager):
        """测试移除连接"""
        connection_id = "test_connection_002"
        connection_info = {
            "participant_type": "user",
            "target_id": "test_device",
            "connection_type": "audio"
        }
        
        # 添加连接
        await connection_manager.add_connection(connection_id, connection_info)
        
        # 移除连接
        await connection_manager.remove_connection(connection_id)
        
        # 验证连接已移除
        retrieved_info = await connection_manager.get_connection(connection_id)
        assert retrieved_info is None
    
    @pytest.mark.asyncio
    async def test_get_all_connections(self, connection_manager):
        """测试获取所有连接"""
        # 添加多个连接
        connections = {
            "conn1": {"type": "video"},
            "conn2": {"type": "audio"},
            "conn3": {"type": "data"}
        }
        
        for conn_id, conn_info in connections.items():
            await connection_manager.add_connection(conn_id, conn_info)
        
        # 获取所有连接
        all_connections = await connection_manager.get_all_connections()
        
        assert len(all_connections) >= 3
        for conn_id in connections:
            assert conn_id in all_connections


class TestWebRTCAuthentication:
    """WebRTC认证测试"""
    
    @pytest.mark.asyncio
    async def test_authenticate_valid_user_token(self):
        """测试验证有效用户token"""
        from routers.webrtc import authenticate_websocket
        from utils.security import create_access_token
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 创建有效用户token
        user_payload = {
            "sub": "user123",
            "username": "testuser",
            "role": "webrtc_app_user"
        }
        token = create_access_token(user_payload)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is not None
        assert result["sub"] == "user123"
        assert result["username"] == "testuser"
    
    @pytest.mark.asyncio
    async def test_authenticate_valid_device_token(self):
        """测试验证有效设备token"""
        from routers.webrtc import authenticate_websocket
        from utils.security import create_access_token
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 创建有效设备token
        device_payload = {
            "device_id": "device123",
            "type": "device"
        }
        token = create_access_token(device_payload)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is not None
        assert result["device_id"] == "device123"
        assert result["type"] == "device"
    
    @pytest.mark.asyncio
    async def test_authenticate_invalid_token(self):
        """测试验证无效token"""
        from routers.webrtc import authenticate_websocket
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试无效token
        result = await authenticate_websocket(mock_websocket, "invalid_token")
        
        assert result is None
        # 验证WebSocket被关闭
        mock_websocket.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_missing_device_id(self):
        """测试缺少device_id的设备token"""
        from routers.webrtc import authenticate_websocket
        from utils.security import create_access_token
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 创建缺少device_id的设备token
        device_payload = {
            "type": "device"
            # 缺少device_id
        }
        token = create_access_token(device_payload)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is None
        # 验证WebSocket被关闭
        mock_websocket.close.assert_called_once()


class TestWebRTCSessionManagement:
    """WebRTC会话管理测试"""
    
    @pytest.mark.asyncio
    async def test_session_creation(self, connection_manager):
        """测试会话创建"""
        session_id = "test_session_001"
        session_info = {
            "session_id": session_id,
            "connection_type": "video",
            "participants": ["user1", "device1"],
            "state": "active"
        }
        
        await connection_manager.add_session(session_id, session_info)
        
        # 验证会话已创建
        retrieved_session = await connection_manager.get_session(session_id)
        assert retrieved_session == session_info
    
    @pytest.mark.asyncio
    async def test_session_cleanup(self, connection_manager):
        """测试会话清理"""
        session_id = "test_session_002"
        session_info = {
            "session_id": session_id,
            "connection_type": "audio",
            "participants": ["user2", "device2"],
            "state": "active"
        }
        
        # 创建会话
        await connection_manager.add_session(session_id, session_info)
        
        # 清理会话
        await connection_manager.remove_session(session_id)
        
        # 验证会话已清理
        retrieved_session = await connection_manager.get_session(session_id)
        assert retrieved_session is None
