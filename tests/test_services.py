"""
服务层测试
测试各种服务类的功能
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from sqlmodel import Session
from services.auth_service import AuthService
from services.device_service import DeviceService
from services.dependency_container import WebRTCConnectionManager
from models.user import User
from models.user_device_binding import Device
from models.user_device_binding import UserDeviceBinding, PermissionLevel


class TestAuthService:
    """认证服务测试"""
    
    def test_create_user_success(self, test_session: Session):
        """测试成功创建用户"""
        auth_service = AuthService(test_session)
        
        user = auth_service.create_user(
            username="testuser",
            password="testpass123",
            role="webrtc_app_user"
        )
        
        assert user is not None
        assert user.username == "testuser"
        assert user.role == "webrtc_app_user"
        assert user.is_active is True
        assert user.password_hash != "testpass123"  # 密码应该被哈希
    
    def test_create_duplicate_user(self, test_session: Session):
        """测试创建重复用户"""
        auth_service = AuthService(test_session)
        
        # 创建第一个用户
        user1 = auth_service.create_user(
            username="duplicate",
            password="pass123",
            role="webrtc_app_user"
        )
        assert user1 is not None
        
        # 尝试创建重复用户
        user2 = auth_service.create_user(
            username="duplicate",
            password="pass456",
            role="webrtc_app_user"
        )
        assert user2 is None  # 应该返回None
    
    def test_authenticate_user_success(self, test_session: Session):
        """测试成功认证用户"""
        auth_service = AuthService(test_session)
        
        # 先创建用户
        created_user = auth_service.create_user(
            username="authtest",
            password="authpass123",
            role="webrtc_app_user"
        )
        assert created_user is not None
        
        # 认证用户
        authenticated_user = auth_service.authenticate_user("authtest", "authpass123")
        assert authenticated_user is not None
        assert authenticated_user.username == "authtest"
    
    def test_authenticate_user_wrong_password(self, test_session: Session):
        """测试错误密码认证"""
        auth_service = AuthService(test_session)
        
        # 先创建用户
        created_user = auth_service.create_user(
            username="wrongpass",
            password="correctpass",
            role="webrtc_app_user"
        )
        assert created_user is not None
        
        # 使用错误密码认证
        authenticated_user = auth_service.authenticate_user("wrongpass", "wrongpassword")
        assert authenticated_user is None
    
    def test_authenticate_nonexistent_user(self, test_session: Session):
        """测试认证不存在的用户"""
        auth_service = AuthService(test_session)
        
        authenticated_user = auth_service.authenticate_user("nonexistent", "password")
        assert authenticated_user is None
    
    def test_get_user_by_username(self, test_session: Session):
        """测试通过用户名获取用户"""
        auth_service = AuthService(test_session)
        
        # 创建用户
        created_user = auth_service.create_user(
            username="gettest",
            password="pass123",
            role="webrtc_app_user"
        )
        assert created_user is not None
        
        # 获取用户
        retrieved_user = auth_service.get_user_by_username("gettest")
        assert retrieved_user is not None
        assert retrieved_user.username == "gettest"
        
        # 获取不存在的用户
        nonexistent_user = auth_service.get_user_by_username("nonexistent")
        assert nonexistent_user is None


class TestDeviceService:
    """设备服务测试"""

    def test_create_device_success(self, test_session: Session):
        """测试成功创建设备"""
        device_service = DeviceService(test_session)
        
        device = binding_service.create_device(
            device_id="test_device_001",
            device_name="测试摄像头",
            device_type="camera",
            location="测试位置"
        )
        
        assert device is not None
        assert device.device_id == "test_device_001"
        assert device.device_name == "测试摄像头"
        assert device.device_type == "camera"
        assert device.location == "测试位置"
        assert device.is_active is True
    
    def test_create_duplicate_device(self, test_session: Session):
        """测试创建重复设备"""
        binding_service = DeviceBindingService(test_session)
        
        # 创建第一个设备
        device1 = binding_service.create_device(
            device_id="duplicate_device",
            device_name="设备1",
            device_type="camera"
        )
        assert device1 is not None
        
        # 尝试创建重复设备
        device2 = binding_service.create_device(
            device_id="duplicate_device",
            device_name="设备2",
            device_type="sensor"
        )
        assert device2 is None
    
    def test_get_device_by_id(self, test_session: Session):
        """测试通过ID获取设备"""
        binding_service = DeviceBindingService(test_session)
        
        # 创建设备
        created_device = binding_service.create_device(
            device_id="get_test_device",
            device_name="获取测试设备",
            device_type="camera"
        )
        assert created_device is not None
        
        # 获取设备
        retrieved_device = binding_service.get_device_by_id("get_test_device")
        assert retrieved_device is not None
        assert retrieved_device.device_id == "get_test_device"
        
        # 获取不存在的设备
        nonexistent_device = binding_service.get_device_by_id("nonexistent")
        assert nonexistent_device is None
    
    def test_bind_device_to_user(self, test_session: Session):
        """测试绑定设备到用户"""
        # 创建用户
        auth_service = AuthService(test_session)
        user = auth_service.create_user(
            username="binduser",
            password="pass123",
            role="webrtc_app_user"
        )
        assert user is not None
        
        # 创建设备
        binding_service = DeviceBindingService(test_session)
        device = binding_service.create_device(
            device_id="bind_device",
            device_name="绑定测试设备",
            device_type="camera"
        )
        assert device is not None
        
        # 绑定设备到用户
        binding = binding_service.bind_device_to_user(
            user_id=user.id,
            device_id="bind_device",
            permission_level=PermissionLevel.CONTROL,
            device_name="自定义设备名"
        )
        
        assert binding is not None
        assert binding.user_id == user.id
        assert binding.device_id == "bind_device"
        assert binding.permission_level == PermissionLevel.CONTROL
        assert binding.device_name == "自定义设备名"
    
    def test_get_user_devices(self, test_session: Session):
        """测试获取用户设备列表"""
        # 创建用户
        auth_service = AuthService(test_session)
        user = auth_service.create_user(
            username="deviceuser",
            password="pass123",
            role="webrtc_app_user"
        )
        assert user is not None
        
        # 创建设备绑定服务
        binding_service = DeviceBindingService(test_session)
        
        # 创建多个设备
        device1 = binding_service.create_device(
            device_id="user_device_1",
            device_name="设备1",
            device_type="camera"
        )
        device2 = binding_service.create_device(
            device_id="user_device_2",
            device_name="设备2",
            device_type="sensor"
        )
        
        # 绑定设备到用户
        binding_service.bind_device_to_user(
            user_id=user.id,
            device_id="user_device_1",
            permission_level=PermissionLevel.VIEW
        )
        binding_service.bind_device_to_user(
            user_id=user.id,
            device_id="user_device_2",
            permission_level=PermissionLevel.CONTROL
        )
        
        # 获取用户设备列表
        user_devices = binding_service.get_user_devices(user.id)
        assert len(user_devices) == 2
        
        device_ids = [binding.device_id for binding in user_devices]
        assert "user_device_1" in device_ids
        assert "user_device_2" in device_ids


class TestWebRTCConnectionManager:
    """WebRTC连接管理器测试"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_management(self, connection_manager):
        """测试WebSocket连接管理"""
        from unittest.mock import AsyncMock
        from fastapi import WebSocket
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        connection_id = "test_ws_001"
        
        # 添加WebSocket连接
        await connection_manager.add_websocket_connection(connection_id, mock_websocket)
        
        # 获取WebSocket连接
        retrieved_ws = await connection_manager.get_websocket_connection(connection_id)
        assert retrieved_ws == mock_websocket
        
        # 移除WebSocket连接
        await connection_manager.remove_websocket_connection(connection_id)
        
        # 验证连接已移除
        removed_ws = await connection_manager.get_websocket_connection(connection_id)
        assert removed_ws is None
    
    @pytest.mark.asyncio
    async def test_connection_info_management(self, connection_manager):
        """测试连接信息管理"""
        connection_id = "test_info_001"
        connection_info = {
            "participant_type": "device",
            "target_id": "test_user",
            "connection_type": "video",
            "features": ["messaging", "media"]
        }
        
        # 添加连接信息
        await connection_manager.add_connection_info(connection_id, connection_info)
        
        # 验证连接信息包含进程ID
        retrieved_info = await connection_manager.get_connection_info(connection_id)
        assert retrieved_info is not None
        assert retrieved_info["participant_type"] == "device"
        assert retrieved_info["target_id"] == "test_user"
        assert "process_id" in retrieved_info
        assert "is_active" in retrieved_info
        
        # 移除连接信息
        await connection_manager.remove_connection_info(connection_id)
        
        # 验证信息已移除
        removed_info = await connection_manager.get_connection_info(connection_id)
        assert removed_info is None
    
    @pytest.mark.asyncio
    async def test_complete_connection_cleanup(self, connection_manager):
        """测试完整连接清理"""
        from unittest.mock import AsyncMock
        from fastapi import WebSocket
        
        connection_id = "test_cleanup_001"
        mock_websocket = AsyncMock(spec=WebSocket)
        connection_info = {
            "participant_type": "user",
            "target_id": "test_device",
            "connection_type": "audio"
        }
        
        # 添加WebSocket和连接信息
        await connection_manager.add_websocket_connection(connection_id, mock_websocket)
        await connection_manager.add_connection_info(connection_id, connection_info)
        
        # 验证都已添加
        assert await connection_manager.get_websocket_connection(connection_id) is not None
        assert await connection_manager.get_connection_info(connection_id) is not None
        
        # 完整清理
        await connection_manager.cleanup_connection(connection_id)
        
        # 验证都已清理
        assert await connection_manager.get_websocket_connection(connection_id) is None
        assert await connection_manager.get_connection_info(connection_id) is None
    
    @pytest.mark.asyncio
    async def test_local_connections_count(self, connection_manager):
        """测试本地连接计数"""
        from unittest.mock import AsyncMock
        from fastapi import WebSocket
        
        # 初始计数应为0
        initial_count = await connection_manager.get_local_connections_count()
        assert initial_count == 0
        
        # 添加几个连接
        for i in range(3):
            mock_ws = AsyncMock(spec=WebSocket)
            await connection_manager.add_websocket_connection(f"test_count_{i}", mock_ws)
        
        # 验证计数
        count_after_add = await connection_manager.get_local_connections_count()
        assert count_after_add == 3
        
        # 移除一个连接
        await connection_manager.remove_websocket_connection("test_count_0")
        
        # 验证计数减少
        count_after_remove = await connection_manager.get_local_connections_count()
        assert count_after_remove == 2
