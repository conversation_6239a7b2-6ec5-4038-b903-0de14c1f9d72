"""
错误处理模块测试
测试统一错误处理功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from fastapi import HTTPException, WebSocket
from fastapi.responses import JSONResponse
from utils.error_handler import (
    WebRTCError, AuthenticationError, AuthorizationError, 
    ConnectionError, SessionError, ValidationError,
    create_error_response, handle_websocket_error, 
    handle_api_exception, get_error_details
)


class TestWebRTCExceptions:
    """WebRTC异常类测试"""
    
    def test_webrtc_error_base(self):
        """测试WebRTC基础错误"""
        error = WebRTCError("测试错误", "TEST_ERROR", {"detail": "测试详情"})
        
        assert str(error) == "测试错误"
        assert error.error_code == "TEST_ERROR"
        assert error.details == {"detail": "测试详情"}
    
    def test_authentication_error(self):
        """测试认证错误"""
        error = AuthenticationError("认证失败", {"reason": "无效token"})
        
        assert str(error) == "认证失败"
        assert error.error_code == "AUTH_ERROR"
        assert error.details == {"reason": "无效token"}
    
    def test_authorization_error(self):
        """测试授权错误"""
        error = AuthorizationError("权限不足", {"required": "admin"})
        
        assert str(error) == "权限不足"
        assert error.error_code == "AUTHORIZATION_ERROR"
        assert error.details == {"required": "admin"}
    
    def test_connection_error(self):
        """测试连接错误"""
        error = ConnectionError("连接失败", {"host": "localhost"})
        
        assert str(error) == "连接失败"
        assert error.error_code == "CONNECTION_ERROR"
        assert error.details == {"host": "localhost"}
    
    def test_session_error(self):
        """测试会话错误"""
        error = SessionError("会话过期", {"session_id": "123"})
        
        assert str(error) == "会话过期"
        assert error.error_code == "SESSION_ERROR"
        assert error.details == {"session_id": "123"}
    
    def test_validation_error(self):
        """测试验证错误"""
        error = ValidationError("数据无效", {"field": "username"})
        
        assert str(error) == "数据无效"
        assert error.error_code == "VALIDATION_ERROR"
        assert error.details == {"field": "username"}


class TestErrorResponseCreation:
    """错误响应创建测试"""
    
    def test_create_error_response_basic(self):
        """测试基础错误响应创建"""
        error = Exception("基础错误")
        response = create_error_response(error, 500)
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 500
        
        # 检查响应内容
        content = response.body.decode()
        assert "success" in content
        assert "error" in content
        assert "基础错误" in content
    
    def test_create_error_response_webrtc_error(self):
        """测试WebRTC错误响应创建"""
        error = AuthenticationError("认证失败", {"token": "invalid"})
        response = create_error_response(error, 401)
        
        assert response.status_code == 401
        
        # 检查响应内容包含错误代码和详情
        content = response.body.decode()
        assert "AUTH_ERROR" in content
        assert "认证失败" in content
        assert "invalid" in content
    
    def test_create_error_response_with_traceback(self):
        """测试包含堆栈跟踪的错误响应"""
        error = ValueError("值错误")
        response = create_error_response(error, 400, include_traceback=True)
        
        assert response.status_code == 400
        
        # 检查响应包含堆栈跟踪
        content = response.body.decode()
        assert "traceback" in content


class TestWebSocketErrorHandling:
    """WebSocket错误处理测试"""
    
    @pytest.mark.asyncio
    async def test_handle_websocket_error_basic(self):
        """测试基础WebSocket错误处理"""
        mock_websocket = AsyncMock(spec=WebSocket)
        error = Exception("WebSocket错误")
        
        await handle_websocket_error(mock_websocket, error, 1011)
        
        # 验证发送了错误消息
        mock_websocket.send_json.assert_called_once()
        sent_message = mock_websocket.send_json.call_args[0][0]
        
        assert sent_message["type"] == "error"
        assert "WebSocket错误" in sent_message["payload"]["message"]
        
        # 验证关闭了连接
        mock_websocket.close.assert_called_once_with(code=1011, reason="WebSocket错误")
    
    @pytest.mark.asyncio
    async def test_handle_websocket_error_webrtc_error(self):
        """测试WebRTC专用错误的WebSocket处理"""
        mock_websocket = AsyncMock(spec=WebSocket)
        error = ConnectionError("连接超时", {"timeout": 30})
        
        await handle_websocket_error(mock_websocket, error, 1011)
        
        # 验证发送了包含错误代码的消息
        sent_message = mock_websocket.send_json.call_args[0][0]
        
        assert sent_message["payload"]["code"] == "CONNECTION_ERROR"
        assert sent_message["payload"]["details"] == {"timeout": 30}
    
    @pytest.mark.asyncio
    async def test_handle_websocket_error_send_failure(self):
        """测试WebSocket发送失败的处理"""
        mock_websocket = AsyncMock(spec=WebSocket)
        mock_websocket.send_json.side_effect = Exception("发送失败")
        
        error = Exception("原始错误")
        
        # 不应该抛出异常
        await handle_websocket_error(mock_websocket, error, 1011)
        
        # 仍然应该尝试关闭连接
        mock_websocket.close.assert_called()


class TestAPIExceptionHandling:
    """API异常处理测试"""
    
    def test_handle_authentication_error(self):
        """测试认证错误转换"""
        error = AuthenticationError("认证失败")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 401
        assert http_exception.detail == "认证失败"
    
    def test_handle_authorization_error(self):
        """测试授权错误转换"""
        error = AuthorizationError("权限不足")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 403
        assert http_exception.detail == "权限不足"
    
    def test_handle_validation_error(self):
        """测试验证错误转换"""
        error = ValidationError("数据无效")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 400
        assert http_exception.detail == "数据无效"
    
    def test_handle_connection_error(self):
        """测试连接错误转换"""
        error = ConnectionError("连接失败")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 409
        assert http_exception.detail == "连接失败"
    
    def test_handle_session_error(self):
        """测试会话错误转换"""
        error = SessionError("会话错误")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 409
        assert http_exception.detail == "会话错误"
    
    def test_handle_generic_webrtc_error(self):
        """测试通用WebRTC错误转换"""
        error = WebRTCError("通用错误")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 500
        assert http_exception.detail == "通用错误"
    
    def test_handle_unknown_error(self):
        """测试未知错误转换"""
        error = ValueError("未知错误")
        http_exception = handle_api_exception(error)
        
        assert isinstance(http_exception, HTTPException)
        assert http_exception.status_code == 500
        assert http_exception.detail == "内部服务器错误"


class TestErrorDetails:
    """错误详情测试"""
    
    def test_get_error_details_basic(self):
        """测试基础错误详情获取"""
        error = Exception("基础错误")
        details = get_error_details(error)
        
        assert details["error_type"] == "Exception"
        assert details["message"] == "基础错误"
        assert "module" in details
    
    def test_get_error_details_webrtc_error(self):
        """测试WebRTC错误详情获取"""
        error = AuthenticationError("认证失败", {"user": "test"})
        details = get_error_details(error)
        
        assert details["error_type"] == "AuthenticationError"
        assert details["message"] == "认证失败"
        assert details["error_code"] == "AUTH_ERROR"
        assert details["custom_details"] == {"user": "test"}
    
    def test_get_error_details_with_module(self):
        """测试包含模块信息的错误详情"""
        error = ValueError("值错误")
        error.__module__ = "test_module"
        
        details = get_error_details(error)
        
        assert details["module"] == "test_module"


class TestErrorHandlerDecorators:
    """错误处理装饰器测试"""
    
    @pytest.mark.asyncio
    async def test_api_error_handler_decorator(self):
        """测试API错误处理装饰器"""
        from utils.error_handler import ErrorHandler
        
        @ErrorHandler.api_error_handler()
        async def test_function():
            raise AuthenticationError("装饰器测试")
        
        with pytest.raises(HTTPException) as exc_info:
            await test_function()
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "装饰器测试"
    
    @pytest.mark.asyncio
    async def test_api_error_handler_success(self):
        """测试API错误处理装饰器成功情况"""
        from utils.error_handler import ErrorHandler
        
        @ErrorHandler.api_error_handler()
        async def test_function():
            return {"success": True}
        
        result = await test_function()
        assert result == {"success": True}
    
    @pytest.mark.asyncio
    async def test_websocket_error_handler_decorator(self):
        """测试WebSocket错误处理装饰器"""
        from utils.error_handler import ErrorHandler
        
        mock_websocket = AsyncMock(spec=WebSocket)
        
        @ErrorHandler.websocket_error_handler(1008)
        async def test_function(websocket):
            raise ConnectionError("装饰器WebSocket测试")
        
        with pytest.raises(ConnectionError):
            await test_function(mock_websocket)
        
        # 验证WebSocket错误处理被调用
        mock_websocket.send_json.assert_called_once()
        mock_websocket.close.assert_called_once_with(code=1008, reason="装饰器WebSocket测试")
