"""
认证模块测试
测试用户认证、token生成和验证功能
"""

import pytest
from fastapi.testclient import TestClient
from utils.security import verify_password, get_password_hash, create_access_token, verify_token


class TestPasswordHashing:
    """密码哈希测试"""
    
    def test_password_hashing(self):
        """测试密码哈希和验证"""
        password = "测试密码123"
        hashed = get_password_hash(password)
        
        # 验证哈希后的密码不等于原密码
        assert hashed != password
        
        # 验证密码验证功能
        assert verify_password(password, hashed) is True
        assert verify_password("错误密码", hashed) is False
    
    def test_different_passwords_different_hashes(self):
        """测试不同密码产生不同哈希"""
        password1 = "密码1"
        password2 = "密码2"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2


class TestJWTTokens:
    """JWT Token测试"""
    
    def test_create_user_token(self):
        """测试创建用户token"""
        user_data = {
            "sub": "user123",
            "username": "测试用户",
            "role": "webrtc_app_user"
        }
        
        token = create_access_token(user_data)
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_create_device_token(self):
        """测试创建设备token"""
        device_data = {
            "device_id": "device123",
            "type": "device"
        }
        
        token = create_access_token(device_data)
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_valid_token(self):
        """测试验证有效token"""
        user_data = {
            "sub": "user123",
            "username": "测试用户",
            "role": "webrtc_app_user"
        }
        
        token = create_access_token(user_data)
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "user123"
        assert payload["username"] == "测试用户"
        assert payload["role"] == "webrtc_app_user"
    
    def test_verify_invalid_token(self):
        """测试验证无效token"""
        invalid_token = "invalid.token.here"
        payload = verify_token(invalid_token)
        
        assert payload is None


class TestAuthAPI:
    """认证API测试"""
    
    def test_login_success(self, client: TestClient, test_session):
        """测试成功登录"""
        # 首先创建用户
        user_data = {
            "username": "testuser",
            "password": "testpass123"
        }

        # 注册用户
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code in [200, 201]

        # 登录 - 使用JSON格式
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }

        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200

        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user_id" in data
        assert "expires_in" in data
    
    def test_login_invalid_credentials(self, client: TestClient):
        """测试无效凭据登录"""
        login_data = {
            "username": "nonexistent",
            "password": "wrongpassword"
        }

        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
    
    def test_register_new_user(self, client: TestClient):
        """测试注册新用户"""
        user_data = {
            "username": "newuser",
            "password": "newpass123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code in [200, 201]
        
        data = response.json()
        assert data["username"] == "newuser"
        assert "password" not in data  # 确保密码不在响应中
    
    def test_register_duplicate_user(self, client: TestClient):
        """测试注册重复用户"""
        user_data = {
            "username": "duplicateuser",
            "password": "pass123"
        }
        
        # 第一次注册
        response1 = client.post("/api/v1/auth/register", json=user_data)
        assert response1.status_code in [200, 201]
        
        # 第二次注册相同用户名
        response2 = client.post("/api/v1/auth/register", json=user_data)
        assert response2.status_code == 400  # 应该返回错误
    
    def test_protected_endpoint_with_valid_token(self, client: TestClient, test_user_token):
        """测试使用有效token访问受保护端点"""
        headers = {"Authorization": f"Bearer {test_user_token}"}
        
        # 假设有一个受保护的端点
        response = client.get("/api/v1/webrtc/health", headers=headers)
        # 健康检查端点通常不需要认证，这里只是示例
        assert response.status_code == 200
    
    def test_protected_endpoint_without_token(self, client: TestClient):
        """测试不带token访问受保护端点"""
        # 这里需要一个真正需要认证的端点来测试
        response = client.get("/api/v1/webrtc/connections")
        # 根据实际API设计，可能返回401或403
        assert response.status_code in [401, 403, 200]  # 200表示该端点可能不需要认证
