"""
测试配置文件
提供测试夹具和共享配置
"""

import pytest
import asyncio
from typing import As<PERSON><PERSON>enerator, Generator
from fastapi.testclient import TestClient
from sqlmodel import SQLModel, create_engine, Session
from sqlmodel.pool import StaticPool

from main import app
from database import get_session
from config import settings
from services.webrtc_redis_service import WebRTCRedisService
from utils.security import create_access_token


# 测试数据库配置
TEST_DATABASE_URL = "sqlite:///:memory:"

# 移除自定义event_loop夹具，使用pytest-asyncio的默认实现


@pytest.fixture(scope="function")
def test_engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    SQLModel.metadata.create_all(engine)
    return engine


@pytest.fixture(scope="function")
def test_session(test_engine) -> Generator[Session, None, None]:
    """创建测试数据库会话"""
    with Session(test_engine) as session:
        yield session


@pytest.fixture(scope="function")
def client(test_session: Session) -> Generator[TestClient, None, None]:
    """创建测试客户端"""
    def get_test_session():
        return test_session

    app.dependency_overrides[get_session] = get_test_session
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def mock_redis_service():
    """模拟Redis服务"""
    class MockRedisService:
        def __init__(self):
            self.connections = {}
            self.sessions = {}
            self.redis_available = False  # 模拟Redis不可用，使用内存存储

        async def add_connection(self, connection_id: str, connection_info: dict):
            self.connections[connection_id] = connection_info

        async def get_connection(self, connection_id: str):
            return self.connections.get(connection_id)

        async def remove_connection(self, connection_id: str):
            self.connections.pop(connection_id, None)

        async def get_all_connections(self):
            return self.connections

        async def add_session(self, session_id: str, session_info: dict):
            self.sessions[session_id] = session_info

        async def get_session(self, session_id: str):
            return self.sessions.get(session_id)

        async def remove_session(self, session_id: str):
            self.sessions.pop(session_id, None)

        async def get_all_sessions(self):
            return self.sessions

    return MockRedisService()


@pytest.fixture
def connection_manager(mock_redis_service):
    """创建WebRTC连接管理器实例"""
    from services.dependency_container import WebRTCConnectionManager
    return WebRTCConnectionManager(redis_service=mock_redis_service)


@pytest.fixture
def test_user_token():
    """创建测试用户token"""
    payload = {
        "sub": "test-user-id",
        "username": "testuser",
        "role": "webrtc_app_user"
    }
    return create_access_token(payload)


@pytest.fixture
def test_device_token():
    """创建测试设备token"""
    payload = {
        "device_id": "test_device_001",
        "type": "device"
    }
    return create_access_token(payload)


@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "username": "testuser",
        "password": "testpassword123",
        "role": "webrtc_app_user"
    }


@pytest.fixture
def sample_device_data():
    """示例设备数据"""
    return {
        "device_id": "test_device_001",
        "device_name": "测试摄像头",
        "device_type": "camera",
        "location": "测试位置"
    }


@pytest.fixture
def websocket_test_url():
    """WebSocket测试URL"""
    return "ws://testserver/api/v1/webrtc/connect"


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 测试后清理逻辑可以在这里添加
