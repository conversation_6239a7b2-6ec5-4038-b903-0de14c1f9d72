"""
安全配置模块测试
测试安全配置和密码验证功能
"""

import pytest
import os
from unittest.mock import patch
from pydantic import ValidationError
from config import (
    validate_password_strength,
    generate_secure_key, is_secure_origin
)


class TestConfigSettings:
    """配置设置测试"""

    def test_config_import(self):
        """测试配置导入"""
        from config import settings

        assert settings is not None
        assert hasattr(settings, 'app_name')
        assert hasattr(settings, 'secret_key')
        assert hasattr(settings, 'allowed_origins')


class TestPasswordStrengthValidation:
    """密码强度验证测试"""
    
    def test_valid_password(self):
        """测试有效密码"""
        is_valid, errors = validate_password_strength("Password123")
        assert is_valid is True
        assert len(errors) == 0
    
    def test_password_too_short(self):
        """测试密码过短"""
        is_valid, errors = validate_password_strength("Pass")
        assert is_valid is False
        assert any("长度至少需要8个字符" in error for error in errors)
    
    def test_password_missing_uppercase(self):
        """测试缺少大写字母"""
        is_valid, errors = validate_password_strength("password123")
        assert is_valid is False
        assert any("大写字母" in error for error in errors)

    def test_password_missing_lowercase(self):
        """测试缺少小写字母"""
        is_valid, errors = validate_password_strength("PASSWORD123")
        assert is_valid is False
        assert any("小写字母" in error for error in errors)

    def test_password_missing_numbers(self):
        """测试缺少数字"""
        is_valid, errors = validate_password_strength("Password")
        assert is_valid is False
        assert any("数字" in error for error in errors)
    
    def test_weak_password_detection(self):
        """测试弱密码检测"""
        weak_passwords = ["password", "123456", "qwerty", "admin"]

        for weak_pass in weak_passwords:
            is_valid, errors = validate_password_strength(weak_pass)
            assert is_valid is False
            assert any("常见的弱密码" in error for error in errors)

    def test_multiple_password_errors(self):
        """测试多个密码错误"""
        is_valid, errors = validate_password_strength("pass")
        assert is_valid is False
        assert len(errors) >= 3  # 长度、大写、数字


class TestSecurityUtilities:
    """安全工具函数测试"""
    
    def test_generate_secure_key(self):
        """测试生成安全密钥"""
        key1 = generate_secure_key()
        key2 = generate_secure_key()
        
        # 密钥应该不同
        assert key1 != key2
        
        # 密钥长度应该足够
        assert len(key1) >= 32
        assert len(key2) >= 32
    
    def test_is_secure_origin_https(self):
        """测试HTTPS源安全性检查"""
        assert is_secure_origin("https://example.com") is True
        assert is_secure_origin("https://api.example.com") is True
    
    def test_is_secure_origin_http_production(self):
        """测试生产环境HTTP源安全性检查"""
        with patch.dict(os.environ, {"ENVIRONMENT": "production"}):
            assert is_secure_origin("http://example.com") is False
    
    def test_is_secure_origin_localhost_development(self):
        """测试开发环境本地源安全性检查"""
        with patch.dict(os.environ, {"ENVIRONMENT": "development"}):
            assert is_secure_origin("http://localhost:3000") is True
            assert is_secure_origin("http://127.0.0.1:8080") is True
            assert is_secure_origin("http://*************:3000") is True
    
    def test_is_secure_origin_localhost_production(self):
        """测试生产环境本地源安全性检查"""
        with patch.dict(os.environ, {"ENVIRONMENT": "production"}):
            assert is_secure_origin("http://localhost:3000") is False
            assert is_secure_origin("http://127.0.0.1:8080") is False
            assert is_secure_origin("http://*************:3000") is False


class TestGetSecuritySettings:
    """获取安全配置测试"""
    
    @patch.dict(os.environ, {"ENVIRONMENT": "development"})
    def test_get_development_settings(self):
        """测试获取开发环境配置"""
        settings = get_security_settings()
        assert isinstance(settings, SecuritySettings)
        assert not isinstance(settings, ProductionSecuritySettings)
    
    @patch.dict(os.environ, {"ENVIRONMENT": "production"})
    def test_get_production_settings(self):
        """测试获取生产环境配置"""
        # 需要设置有效的密钥和CORS
        with patch.dict(os.environ, {
            "SECRET_KEY": "production-secure-key-32-characters-long",
            "SECURITY_ALLOWED_ORIGINS": '["https://example.com"]'
        }):
            settings = get_security_settings()
            assert isinstance(settings, ProductionSecuritySettings)
    
    @patch.dict(os.environ, {"ENVIRONMENT": "testing"})
    def test_get_testing_settings(self):
        """测试获取测试环境配置"""
        settings = get_security_settings()
        assert isinstance(settings, SecuritySettings)
        assert not isinstance(settings, ProductionSecuritySettings)


class TestSecurityIntegration:
    """安全配置集成测试"""
    
    def test_security_settings_consistency(self):
        """测试安全配置一致性"""
        dev_settings = SecuritySettings()
        prod_settings = ProductionSecuritySettings()
        
        # 生产环境应该比开发环境更严格
        assert prod_settings.password_min_length >= dev_settings.password_min_length
        assert prod_settings.max_login_attempts <= dev_settings.max_login_attempts
        assert prod_settings.login_lockout_duration >= dev_settings.login_lockout_duration
    
    def test_all_security_features_covered(self):
        """测试所有安全特性都被覆盖"""
        settings = SecuritySettings()
        
        # 确保所有重要的安全配置都有默认值
        assert hasattr(settings, 'password_min_length')
        assert hasattr(settings, 'max_login_attempts')
        assert hasattr(settings, 'login_lockout_duration')
        assert hasattr(settings, 'rate_limit_requests')
        assert hasattr(settings, 'rate_limit_window')
        assert hasattr(settings, 'webrtc_max_connections_per_user')
        assert hasattr(settings, 'allowed_origins')
        assert hasattr(settings, 'secret_key')
