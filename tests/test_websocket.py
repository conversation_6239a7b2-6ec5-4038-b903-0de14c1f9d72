"""
WebSocket连接测试
测试WebSocket连接和消息处理功能
"""

import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import WebSocket
from utils.security import create_access_token


class TestWebSocketConnection:
    """WebSocket连接测试"""
    
    def test_websocket_connection_endpoint_exists(self, client: TestClient):
        """测试WebSocket连接端点存在"""
        # 这个测试验证端点是否正确注册
        # 实际的WebSocket测试需要特殊的测试客户端
        
        # 检查路由是否存在
        routes = [route.path for route in client.app.routes]
        websocket_routes = [path for path in routes if "webrtc" in path and "connect" in path]
        
        assert len(websocket_routes) > 0, "WebSocket连接端点应该存在"
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_valid_user_token(self):
        """测试WebSocket用户token认证"""
        from routers.webrtc import authenticate_websocket
        
        # 创建有效用户token
        user_payload = {
            "sub": "user123",
            "username": "testuser",
            "role": "webrtc_app_user"
        }
        token = create_access_token(user_payload)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is not None
        assert result["sub"] == "user123"
        assert result["username"] == "testuser"
        assert result["role"] == "webrtc_app_user"
        
        # 验证WebSocket没有被关闭
        mock_websocket.close.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_valid_device_token(self):
        """测试WebSocket设备token认证"""
        from routers.webrtc import authenticate_websocket
        
        # 创建有效设备token
        device_payload = {
            "device_id": "device123",
            "type": "device"
        }
        token = create_access_token(device_payload)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is not None
        assert result["device_id"] == "device123"
        assert result["type"] == "device"
        
        # 验证WebSocket没有被关闭
        mock_websocket.close.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_invalid_token(self):
        """测试WebSocket无效token认证"""
        from routers.webrtc import authenticate_websocket
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试无效token
        result = await authenticate_websocket(mock_websocket, "invalid_token")
        
        assert result is None
        
        # 验证WebSocket被关闭
        mock_websocket.close.assert_called_once_with(code=1008, reason="Invalid token")
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_missing_device_id(self):
        """测试WebSocket缺少device_id的设备token"""
        from routers.webrtc import authenticate_websocket
        
        # 创建缺少device_id的设备token
        device_payload = {
            "type": "device"
            # 缺少device_id
        }
        token = create_access_token(device_payload)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is None
        
        # 验证WebSocket被关闭，并且有正确的错误信息
        mock_websocket.close.assert_called_once_with(
            code=1008, 
            reason="Invalid device token: missing device_id"
        )
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_missing_user_sub(self):
        """测试WebSocket缺少sub的用户token"""
        from routers.webrtc import authenticate_websocket
        
        # 创建缺少sub的用户token
        user_payload = {
            "username": "testuser",
            "role": "webrtc_app_user"
            # 缺少sub
        }
        token = create_access_token(user_payload)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is None
        
        # 验证WebSocket被关闭
        mock_websocket.close.assert_called_once_with(
            code=1008, 
            reason="Invalid user token: missing sub"
        )
    
    @pytest.mark.asyncio
    async def test_websocket_authentication_unknown_token_type(self):
        """测试WebSocket未知类型token"""
        from routers.webrtc import authenticate_websocket
        
        # 创建未知类型token
        unknown_payload = {
            "some_field": "some_value"
            # 既没有type=device，也没有sub字段
        }
        token = create_access_token(unknown_payload)
        
        # 创建模拟WebSocket
        mock_websocket = AsyncMock(spec=WebSocket)
        
        # 测试认证
        result = await authenticate_websocket(mock_websocket, token)
        
        assert result is None
        
        # 验证WebSocket被关闭
        mock_websocket.close.assert_called_once_with(
            code=1008, 
            reason="Invalid token: unknown type"
        )


class TestWebSocketMessageHandling:
    """WebSocket消息处理测试"""
    
    @pytest.mark.asyncio
    async def test_find_peer_connection(self):
        """测试查找对等连接"""
        from routers.webrtc import find_peer_connection
        
        # 这个测试需要模拟Redis服务
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟会话数据
            mock_session = {
                "participants": {
                    "user": "user_conn_123",
                    "device": "device_conn_456"
                }
            }
            mock_redis.get_session.return_value = mock_session
            
            # 测试查找对等连接
            peer_id = await find_peer_connection("session_001", "user_conn_123")
            
            assert peer_id == "device_conn_456"
            
            # 测试反向查找
            peer_id = await find_peer_connection("session_001", "device_conn_456")
            
            assert peer_id == "user_conn_123"
    
    @pytest.mark.asyncio
    async def test_find_peer_connection_no_session(self):
        """测试查找不存在会话的对等连接"""
        from routers.webrtc import find_peer_connection
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟会话不存在
            mock_redis.get_session.return_value = None
            
            # 测试查找对等连接
            peer_id = await find_peer_connection("nonexistent_session", "conn_123")
            
            assert peer_id is None
    
    @pytest.mark.asyncio
    async def test_find_peer_connection_single_participant(self):
        """测试查找单参与者会话的对等连接"""
        from routers.webrtc import find_peer_connection
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟只有一个参与者的会话
            mock_session = {
                "participants": {
                    "user": "user_conn_123"
                }
            }
            mock_redis.get_session.return_value = mock_session
            
            # 测试查找对等连接
            peer_id = await find_peer_connection("session_001", "user_conn_123")
            
            assert peer_id is None
    
    @pytest.mark.asyncio
    async def test_relay_message_to_peer(self):
        """测试消息中继到对等方"""
        from routers.webrtc import relay_message_to_peer
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis, \
             patch('routers.webrtc.local_websockets') as mock_local_ws:
            
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟目标连接信息
            mock_target_info = {
                "participant_type": "device",
                "is_active": True
            }
            mock_redis.get_connection.return_value = mock_target_info
            
            # 模拟本地WebSocket
            mock_websocket = AsyncMock(spec=WebSocket)
            mock_local_ws.__getitem__ = MagicMock(return_value=mock_websocket)
            mock_local_ws.get = MagicMock(return_value=mock_websocket)
            
            # 测试消息
            test_message = {
                "type": "offer",
                "payload": {"sdp": "test_sdp"}
            }
            
            # 执行消息中继
            await relay_message_to_peer("target_conn_123", test_message)
            
            # 验证消息被发送
            mock_websocket.send_text.assert_called_once()
            sent_data = mock_websocket.send_text.call_args[0][0]
            sent_message = json.loads(sent_data)
            
            assert sent_message["type"] == "offer"
            assert sent_message["payload"]["sdp"] == "test_sdp"
    
    @pytest.mark.asyncio
    async def test_relay_message_to_inactive_peer(self):
        """测试消息中继到非活跃对等方"""
        from routers.webrtc import relay_message_to_peer
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟非活跃连接
            mock_target_info = {
                "participant_type": "device",
                "is_active": False
            }
            mock_redis.get_connection.return_value = mock_target_info
            
            # 测试消息
            test_message = {"type": "test"}
            
            # 执行消息中继（应该不会发送消息）
            await relay_message_to_peer("inactive_conn_123", test_message)
            
            # 验证没有尝试发送消息（因为连接非活跃）
            # 这个测试主要验证函数不会崩溃
    
    @pytest.mark.asyncio
    async def test_relay_message_to_nonexistent_peer(self):
        """测试消息中继到不存在的对等方"""
        from routers.webrtc import relay_message_to_peer
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟连接不存在
            mock_redis.get_connection.return_value = None
            
            # 测试消息
            test_message = {"type": "test"}
            
            # 执行消息中继（应该不会发送消息）
            await relay_message_to_peer("nonexistent_conn_123", test_message)
            
            # 验证没有尝试发送消息（因为连接不存在）
            # 这个测试主要验证函数不会崩溃


class TestWebSocketCleanup:
    """WebSocket清理测试"""
    
    @pytest.mark.asyncio
    async def test_cleanup_connection(self):
        """测试连接清理"""
        from routers.webrtc import cleanup_connection
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis, \
             patch('routers.webrtc.local_websockets') as mock_local_ws:
            
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟连接信息
            mock_connection_info = {
                "session_id": "test_session",
                "participant_type": "user"
            }
            mock_redis.get_connection.return_value = mock_connection_info
            
            # 模拟会话信息
            mock_session = {
                "participants": {
                    "user": "test_conn_123",
                    "device": "device_conn_456"
                }
            }
            mock_redis.get_session.return_value = mock_session
            
            # 模拟本地WebSocket字典
            mock_local_ws.__contains__ = MagicMock(return_value=True)
            mock_local_ws.__delitem__ = MagicMock()
            
            # 执行清理
            await cleanup_connection("test_conn_123")
            
            # 验证Redis操作被调用
            mock_redis.remove_connection.assert_called_once_with("test_conn_123")
            
            # 验证本地WebSocket被清理
            mock_local_ws.__delitem__.assert_called_once_with("test_conn_123")
    
    @pytest.mark.asyncio
    async def test_cleanup_nonexistent_connection(self):
        """测试清理不存在的连接"""
        from routers.webrtc import cleanup_connection
        
        with patch('routers.webrtc.get_webrtc_redis') as mock_get_redis:
            mock_redis = AsyncMock()
            mock_get_redis.return_value = mock_redis
            
            # 模拟连接不存在
            mock_redis.get_connection.return_value = None
            
            # 执行清理（应该不会崩溃）
            await cleanup_connection("nonexistent_conn")
            
            # 验证仍然尝试从Redis移除
            mock_redis.remove_connection.assert_called_once_with("nonexistent_conn")
