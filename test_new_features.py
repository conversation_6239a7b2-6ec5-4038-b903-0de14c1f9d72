#!/usr/bin/env python3
"""
测试新实现的订单履约功能
验证第一阶段开发的功能是否正常工作
"""

import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_api_endpoint(method: str, endpoint: str, data: Dict[Any, Any] = None, headers: Dict[str, str] = None) -> Dict[Any, Any]:
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        print(f"\n{'='*60}")
        print(f"测试: {method} {endpoint}")
        print(f"状态码: {response.status_code}")
        
        try:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        except:
            print(f"响应: {response.text}")
            return {"error": "Invalid JSON response"}
            
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return {"error": str(e)}

def main():
    """主测试流程"""
    print("🚀 开始测试新实现的订单履约功能")
    
    # 1. 测试基础健康检查
    print("\n📋 1. 测试基础功能")
    test_api_endpoint("GET", "/health")
    test_api_endpoint("GET", "/ping")
    
    # 2. 测试订单创建（使用现有功能）
    print("\n📋 2. 测试订单创建")
    order_data = {
        "device_id": "test-device-001",
        "items": [
            {"product_id": "prod-001", "quantity": 2, "price": 10.0},
            {"product_id": "prod-002", "quantity": 1, "price": 15.0}
        ],
        "total_amount": 35.0
    }
    
    order_result = test_api_endpoint("POST", "/api/v1/orders/", order_data)
    
    if "id" in order_result:
        order_id = order_result["id"]
        print(f"✅ 订单创建成功，订单ID: {order_id}")
        
        # 3. 测试订单详情查询
        print("\n📋 3. 测试订单详情查询")
        test_api_endpoint("GET", f"/api/v1/orders/{order_id}")
        
        # 4. 测试支付确认接口（新功能）
        print("\n📋 4. 测试支付确认接口（新功能）")
        payment_confirm_data = {
            "payment_transaction_id": "txn_test_123456",
            "payment_method": "alipay",
            "verification_data": {"mock": "data"}
        }
        
        confirm_result = test_api_endpoint("POST", f"/api/v1/orders/{order_id}/confirm_payment", payment_confirm_data)
        
        if confirm_result.get("success"):
            print("✅ 支付确认成功")
            
            # 5. 测试员工活动任务列表（新功能）
            print("\n📋 5. 测试员工活动任务列表（新功能）")
            # 注意：这需要认证，我们先测试不需要认证的端点
            test_api_endpoint("GET", "/api/v1/staff/orders/active")
            
            # 6. 测试状态机验证
            print("\n📋 6. 测试状态机验证")
            # 尝试无效的状态转换
            invalid_status_data = {
                "status": "ready_for_pickup",  # 不能从paid直接跳到ready_for_pickup
                "video_stream_id": "video_123"
            }
            
            test_api_endpoint("PUT", f"/api/v1/staff/orders/{order_id}/kitchen_status", invalid_status_data)
            
            # 7. 测试有效的状态转换
            print("\n📋 7. 测试有效的状态转换")
            valid_status_data = {
                "status": "processing",  # 从paid到processing是有效的
                "video_stream_id": "video_stream_001"
            }
            
            test_api_endpoint("PUT", f"/api/v1/staff/orders/{order_id}/kitchen_status", valid_status_data)
            
            # 8. 再次查询订单状态
            print("\n📋 8. 验证状态更新结果")
            test_api_endpoint("GET", f"/api/v1/orders/{order_id}")
            
        else:
            print("❌ 支付确认失败")
    else:
        print("❌ 订单创建失败")
    
    # 9. 测试状态机辅助功能
    print("\n📋 9. 测试OrderStatus枚举")
    try:
        from models.order import OrderStatus
        from services.order_state_machine import OrderStateMachine, OrderWorkflowHelper
        
        print("✅ OrderStatus枚举导入成功")
        print(f"所有状态: {[status.value for status in OrderStatus]}")
        
        # 测试状态转换验证
        print("\n状态转换测试:")
        test_transitions = [
            ("pending_payment", "paid"),
            ("paid", "processing"),
            ("processing", "ready_for_pickup"),
            ("ready_for_pickup", "completed"),
            ("paid", "ready_for_pickup"),  # 无效转换
        ]
        
        for from_status, to_status in test_transitions:
            is_valid = OrderStateMachine.is_valid_transition(from_status, to_status)
            status_icon = "✅" if is_valid else "❌"
            print(f"{status_icon} {from_status} -> {to_status}: {is_valid}")
        
        # 测试工作流助手
        print("\n工作流助手测试:")
        for status in OrderStatus:
            display_name = OrderWorkflowHelper.get_status_display_name(status.value)
            is_active = OrderWorkflowHelper.is_active_for_staff(status.value)
            print(f"状态: {status.value} -> 显示: {display_name}, 员工活动任务: {is_active}")
            
    except Exception as e:
        print(f"❌ 状态机测试失败: {str(e)}")
    
    print("\n🎉 测试完成！")
    print("\n📊 第一阶段功能实现总结:")
    print("✅ 1. OrderStatus枚举类 - 包含READY_FOR_PICKUP状态")
    print("✅ 2. 支付确认接口 - POST /api/v1/orders/{order_id}/confirm_payment")
    print("✅ 3. 视频录制支持 - video_stream_id字段")
    print("✅ 4. 状态转换验证 - 严格的状态机逻辑")
    print("✅ 5. 员工活动任务接口 - GET /api/v1/staff/orders/active")
    print("✅ 6. 备料状态更新接口 - PUT /api/v1/staff/orders/{order_id}/kitchen_status")

if __name__ == "__main__":
    main()
