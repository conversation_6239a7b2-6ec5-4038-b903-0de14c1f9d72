#!/usr/bin/env python3
"""
代码质量检查工具
分析代码结构、重复代码、复杂度等
"""

import os
import ast
import re
from typing import Dict, List, Any, Set
from pathlib import Path
from collections import defaultdict, Counter

class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.python_files = []
        self.analysis_results = {}
        
    def scan_python_files(self):
        """扫描Python文件"""
        self.python_files = []
        
        for file_path in self.project_root.rglob("*.py"):
            # 跳过虚拟环境和缓存目录
            if any(part in str(file_path) for part in ["venv", "__pycache__", ".git", "node_modules"]):
                continue
            self.python_files.append(file_path)
        
        print(f"📁 发现 {len(self.python_files)} 个Python文件")
        return self.python_files
    
    def analyze_file_structure(self) -> Dict[str, Any]:
        """分析文件结构"""
        structure = {
            "total_files": len(self.python_files),
            "directories": defaultdict(int),
            "file_sizes": {},
            "large_files": [],
            "empty_files": []
        }
        
        for file_path in self.python_files:
            # 统计目录
            directory = str(file_path.parent.relative_to(self.project_root))
            structure["directories"][directory] += 1
            
            # 统计文件大小
            try:
                size = file_path.stat().st_size
                structure["file_sizes"][str(file_path.relative_to(self.project_root))] = size
                
                if size > 10000:  # 大于10KB的文件
                    structure["large_files"].append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "size": size
                    })
                
                if size == 0:
                    structure["empty_files"].append(str(file_path.relative_to(self.project_root)))
                    
            except Exception as e:
                print(f"⚠️  无法读取文件大小: {file_path}, 错误: {e}")
        
        return structure
    
    def analyze_imports(self) -> Dict[str, Any]:
        """分析导入依赖"""
        imports = {
            "internal_imports": defaultdict(set),
            "external_imports": defaultdict(set),
            "unused_imports": [],
            "circular_imports": [],
            "import_graph": defaultdict(set)
        }
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                file_name = str(file_path.relative_to(self.project_root))
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            module_name = alias.name
                            if self._is_internal_module(module_name):
                                imports["internal_imports"][file_name].add(module_name)
                            else:
                                imports["external_imports"][file_name].add(module_name)
                            imports["import_graph"][file_name].add(module_name)
                    
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            module_name = node.module
                            if self._is_internal_module(module_name):
                                imports["internal_imports"][file_name].add(module_name)
                            else:
                                imports["external_imports"][file_name].add(module_name)
                            imports["import_graph"][file_name].add(module_name)
                            
            except Exception as e:
                print(f"⚠️  分析导入失败: {file_path}, 错误: {e}")
        
        return imports
    
    def _is_internal_module(self, module_name: str) -> bool:
        """判断是否为内部模块"""
        internal_prefixes = [
            "models", "services", "routers", "utils", "config", 
            "schemas", "tasks", "core", "dependencies"
        ]
        return any(module_name.startswith(prefix) for prefix in internal_prefixes)
    
    def analyze_code_complexity(self) -> Dict[str, Any]:
        """分析代码复杂度"""
        complexity = {
            "functions": [],
            "classes": [],
            "high_complexity_functions": [],
            "long_functions": [],
            "total_lines": 0,
            "total_functions": 0,
            "total_classes": 0
        }
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                complexity["total_lines"] += len(lines)
                
                tree = ast.parse(content)
                file_name = str(file_path.relative_to(self.project_root))
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        complexity["total_functions"] += 1
                        
                        # 计算函数复杂度（简单的圈复杂度）
                        cyclomatic_complexity = self._calculate_complexity(node)
                        function_lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0
                        
                        func_info = {
                            "file": file_name,
                            "name": node.name,
                            "complexity": cyclomatic_complexity,
                            "lines": function_lines,
                            "start_line": node.lineno
                        }
                        
                        complexity["functions"].append(func_info)
                        
                        if cyclomatic_complexity > 10:
                            complexity["high_complexity_functions"].append(func_info)
                        
                        if function_lines > 50:
                            complexity["long_functions"].append(func_info)
                    
                    elif isinstance(node, ast.ClassDef):
                        complexity["total_classes"] += 1
                        
                        class_lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0
                        methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                        
                        complexity["classes"].append({
                            "file": file_name,
                            "name": node.name,
                            "lines": class_lines,
                            "methods": len(methods),
                            "start_line": node.lineno
                        })
                        
            except Exception as e:
                print(f"⚠️  分析复杂度失败: {file_path}, 错误: {e}")
        
        return complexity
    
    def _calculate_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数的圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def find_duplicate_code(self) -> Dict[str, Any]:
        """查找重复代码"""
        duplicates = {
            "duplicate_functions": [],
            "similar_code_blocks": [],
            "duplicate_imports": []
        }
        
        function_signatures = defaultdict(list)
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                file_name = str(file_path.relative_to(self.project_root))
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # 生成函数签名
                        args = [arg.arg for arg in node.args.args]
                        signature = f"{node.name}({', '.join(args)})"
                        
                        function_signatures[signature].append({
                            "file": file_name,
                            "line": node.lineno,
                            "name": node.name
                        })
                        
            except Exception as e:
                print(f"⚠️  查找重复代码失败: {file_path}, 错误: {e}")
        
        # 找出重复的函数签名
        for signature, locations in function_signatures.items():
            if len(locations) > 1:
                duplicates["duplicate_functions"].append({
                    "signature": signature,
                    "locations": locations,
                    "count": len(locations)
                })
        
        return duplicates
    
    def analyze_naming_conventions(self) -> Dict[str, Any]:
        """分析命名规范"""
        naming = {
            "snake_case_violations": [],
            "camel_case_violations": [],
            "constant_violations": [],
            "class_naming_violations": []
        }
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                file_name = str(file_path.relative_to(self.project_root))
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        if not re.match(r'^[a-z_][a-z0-9_]*$', node.name):
                            naming["snake_case_violations"].append({
                                "file": file_name,
                                "type": "function",
                                "name": node.name,
                                "line": node.lineno
                            })
                    
                    elif isinstance(node, ast.ClassDef):
                        if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                            naming["class_naming_violations"].append({
                                "file": file_name,
                                "type": "class",
                                "name": node.name,
                                "line": node.lineno
                            })
                    
                    elif isinstance(node, ast.Assign):
                        for target in node.targets:
                            if isinstance(target, ast.Name):
                                name = target.id
                                # 检查常量命名（全大写）
                                if name.isupper() and len(name) > 1:
                                    if not re.match(r'^[A-Z_][A-Z0-9_]*$', name):
                                        naming["constant_violations"].append({
                                            "file": file_name,
                                            "type": "constant",
                                            "name": name,
                                            "line": node.lineno
                                        })
                                
            except Exception as e:
                print(f"⚠️  分析命名规范失败: {file_path}, 错误: {e}")
        
        return naming
    
    def generate_report(self) -> Dict[str, Any]:
        """生成完整的代码质量报告"""
        print("🔍 开始代码质量分析...")
        
        self.scan_python_files()
        
        report = {
            "summary": {
                "total_files": len(self.python_files),
                "analysis_date": "2025-08-04",
                "project_root": str(self.project_root)
            },
            "file_structure": self.analyze_file_structure(),
            "imports": self.analyze_imports(),
            "complexity": self.analyze_code_complexity(),
            "duplicates": self.find_duplicate_code(),
            "naming": self.analyze_naming_conventions()
        }
        
        print("✅ 代码质量分析完成")
        return report
    
    def print_summary(self, report: Dict[str, Any]):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("📊 代码质量分析报告摘要")
        print("="*60)
        
        # 文件统计
        print(f"📁 总文件数: {report['summary']['total_files']}")
        print(f"📏 总代码行数: {report['complexity']['total_lines']:,}")
        print(f"🔧 总函数数: {report['complexity']['total_functions']}")
        print(f"🏗️  总类数: {report['complexity']['total_classes']}")
        
        # 问题统计
        high_complexity = len(report['complexity']['high_complexity_functions'])
        long_functions = len(report['complexity']['long_functions'])
        duplicate_functions = len(report['duplicates']['duplicate_functions'])
        naming_violations = (
            len(report['naming']['snake_case_violations']) +
            len(report['naming']['class_naming_violations']) +
            len(report['naming']['constant_violations'])
        )
        
        print(f"\n⚠️  发现的问题:")
        print(f"  🔴 高复杂度函数: {high_complexity}")
        print(f"  📏 过长函数: {long_functions}")
        print(f"  🔄 重复函数: {duplicate_functions}")
        print(f"  📝 命名规范违反: {naming_violations}")
        
        # 建议
        print(f"\n💡 优化建议:")
        if high_complexity > 0:
            print(f"  - 重构 {high_complexity} 个高复杂度函数")
        if long_functions > 0:
            print(f"  - 拆分 {long_functions} 个过长函数")
        if duplicate_functions > 0:
            print(f"  - 消除 {duplicate_functions} 个重复函数")
        if naming_violations > 0:
            print(f"  - 修正 {naming_violations} 个命名规范问题")


def main():
    """主函数"""
    analyzer = CodeAnalyzer(".")
    report = analyzer.generate_report()
    analyzer.print_summary(report)
    
    # 保存详细报告
    import json
    with open("code_quality_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: code_quality_report.json")


if __name__ == "__main__":
    main()
