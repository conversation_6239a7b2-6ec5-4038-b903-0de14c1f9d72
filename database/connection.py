from sqlmodel import SQLModel, create_engine, Session
from config import settings

# Create database engine
engine = create_engine(
    settings.database_url,
    echo=settings.debug,
    pool_pre_ping=True
)


def get_session():
    """Get database session"""
    with Session(engine) as session:
        yield session


def create_db_and_tables():
    """Create database tables"""
    SQLModel.metadata.create_all(engine)
