#!/usr/bin/env python3
"""
测试第三阶段优化功能
验证模块化、性能优化和代码质量改进
"""

import asyncio
import time
from typing import Dict, Any

def test_modular_config():
    """测试模块化配置"""
    print("🧪 测试模块化配置")
    
    try:
        from config import settings
        from config.base import (
            BaseConfig, DatabaseConfig, RedisConfig, 
            CeleryConfig, NotificationConfig, WebRTCConfig, 
            PaymentConfig, FeatureFlags
        )
        
        print("✅ 模块化配置导入成功")
        
        # 测试配置访问
        assert hasattr(settings, 'base')
        assert hasattr(settings, 'database')
        assert hasattr(settings, 'redis')
        assert hasattr(settings, 'features')
        print("✅ 配置模块结构正确")
        
        # 测试向后兼容性
        assert hasattr(settings, 'app_name')
        assert hasattr(settings, 'database_url')
        assert hasattr(settings, 'redis_url')
        print("✅ 向后兼容性保持")
        
        # 测试功能开关
        webrtc_enabled = settings.is_feature_enabled("webrtc")
        order_enabled = settings.is_feature_enabled("order_system")
        print(f"✅ 功能开关测试: WebRTC={webrtc_enabled}, Order={order_enabled}")
        
        # 测试配置字典转换
        config_dict = settings.to_dict()
        assert "base" in config_dict
        assert "features" in config_dict
        print("✅ 配置字典转换正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块化配置测试失败: {str(e)}")
        return False


def test_performance_tools():
    """测试性能优化工具"""
    print("\n🧪 测试性能优化工具")
    
    try:
        from utils.performance import (
            PerformanceMonitor, timing_decorator, cache_result,
            simple_cache, performance_monitor, get_performance_report
        )
        
        print("✅ 性能工具导入成功")
        
        # 测试性能监控器
        performance_monitor.record_metric("test.metric", 1.5, {"tag": "test"})
        stats = performance_monitor.get_stats("test.metric")
        assert stats["count"] == 1
        assert stats["latest"] == 1.5
        print("✅ 性能监控器正常")
        
        # 测试计时装饰器
        @timing_decorator("test.function")
        def test_function():
            time.sleep(0.1)
            return "test"
        
        result = test_function()
        assert result == "test"
        print("✅ 计时装饰器正常")
        
        # 测试缓存
        simple_cache.set("test_key", "test_value", 60)
        cached_value = simple_cache.get("test_key")
        assert cached_value == "test_value"
        print("✅ 简单缓存正常")
        
        # 测试缓存装饰器
        call_count = 0
        
        @cache_result(ttl=60, key_prefix="test.cached")
        def cached_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        result1 = cached_function(5)
        result2 = cached_function(5)
        assert result1 == result2 == 10
        assert call_count == 1  # 第二次调用应该使用缓存
        print("✅ 缓存装饰器正常")
        
        # 测试性能报告
        report = get_performance_report()
        assert "uptime_seconds" in report
        assert "cache_stats" in report
        assert "metrics" in report
        print("✅ 性能报告生成正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能工具测试失败: {str(e)}")
        return False


def test_router_manager():
    """测试路由管理器"""
    print("\n🧪 测试路由管理器")
    
    try:
        from core.router_manager import RouterModule, RouterManager, create_app
        from fastapi import APIRouter, FastAPI
        
        print("✅ 路由管理器导入成功")
        
        # 测试路由模块
        test_router = APIRouter()
        
        @test_router.get("/test")
        async def test_endpoint():
            return {"message": "test"}
        
        module = RouterModule(
            name="test_module",
            router=test_router,
            prefix="/api/test",
            tags=["test"],
            description="测试模块"
        )
        
        assert module.name == "test_module"
        assert module.enabled == True
        print("✅ 路由模块创建正常")
        
        # 测试应用创建
        app = create_app()
        assert isinstance(app, FastAPI)
        print("✅ FastAPI应用创建正常")
        
        # 测试路由注册
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/ping", "/info"]
        
        for expected in expected_routes:
            assert expected in routes, f"缺少路由: {expected}"
        print("✅ 基础路由注册正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 路由管理器测试失败: {str(e)}")
        return False


async def test_async_performance_tools():
    """测试异步性能工具"""
    print("\n🧪 测试异步性能工具")
    
    try:
        from utils.performance import timing_decorator, cache_result
        
        # 测试异步计时装饰器
        @timing_decorator("test.async_function")
        async def async_test_function():
            await asyncio.sleep(0.1)
            return "async_test"
        
        result = await async_test_function()
        assert result == "async_test"
        print("✅ 异步计时装饰器正常")
        
        # 测试异步缓存装饰器
        async_call_count = 0
        
        @cache_result(ttl=60, key_prefix="test.async_cached")
        async def async_cached_function(x):
            nonlocal async_call_count
            async_call_count += 1
            await asyncio.sleep(0.01)
            return x * 3
        
        result1 = await async_cached_function(4)
        result2 = await async_cached_function(4)
        assert result1 == result2 == 12
        assert async_call_count == 1  # 第二次调用应该使用缓存
        print("✅ 异步缓存装饰器正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步性能工具测试失败: {str(e)}")
        return False


def test_code_quality_tools():
    """测试代码质量工具"""
    print("\n🧪 测试代码质量工具")
    
    try:
        from tools.code_quality_check import CodeAnalyzer
        
        print("✅ 代码质量工具导入成功")
        
        # 创建分析器实例
        analyzer = CodeAnalyzer(".")
        assert analyzer.project_root.name in ["robot", "."]
        print("✅ 代码分析器创建正常")
        
        # 测试文件扫描
        files = analyzer.scan_python_files()
        assert len(files) > 0
        print(f"✅ 文件扫描正常，发现 {len(files)} 个Python文件")
        
        # 测试文件结构分析
        structure = analyzer.analyze_file_structure()
        assert "total_files" in structure
        assert "directories" in structure
        assert structure["total_files"] > 0
        print("✅ 文件结构分析正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码质量工具测试失败: {str(e)}")
        return False


def test_optimized_main_app():
    """测试优化的主应用"""
    print("\n🧪 测试优化的主应用")
    
    try:
        # 测试导入
        import main_optimized
        
        print("✅ 优化主应用导入成功")
        
        # 测试应用实例
        app = main_optimized.app
        assert hasattr(app, 'routes')
        print("✅ 应用实例正常")
        
        # 检查新增的系统端点
        routes = [route.path for route in app.routes]
        system_routes = [
            "/api/v1/system/performance",
            "/api/v1/system/config",
            "/api/v1/system/routes"
        ]
        
        for route in system_routes:
            if route in routes:
                print(f"✅ 系统端点存在: {route}")
            else:
                print(f"⚠️  系统端点缺失: {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化主应用测试失败: {str(e)}")
        return False


def test_documentation():
    """测试文档完整性"""
    print("\n🧪 测试文档完整性")
    
    try:
        import os
        
        # 检查关键文档文件
        docs = [
            "docs/FEATURE_EVALUATION_REPORT.md",
            "docs/DEPLOYMENT_GUIDE.md",
            "docs/PRD_TDD.md",
            "docs/openapi.json"
        ]
        
        missing_docs = []
        for doc in docs:
            if os.path.exists(doc):
                print(f"✅ 文档存在: {doc}")
            else:
                missing_docs.append(doc)
                print(f"❌ 文档缺失: {doc}")
        
        if not missing_docs:
            print("✅ 所有关键文档都存在")
            return True
        else:
            print(f"⚠️  缺失 {len(missing_docs)} 个文档")
            return False
        
    except Exception as e:
        print(f"❌ 文档检查失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始第三阶段优化功能测试")
    
    results = []
    
    # 运行同步测试
    results.append(("模块化配置", test_modular_config()))
    results.append(("性能优化工具", test_performance_tools()))
    results.append(("路由管理器", test_router_manager()))
    results.append(("代码质量工具", test_code_quality_tools()))
    results.append(("优化主应用", test_optimized_main_app()))
    results.append(("文档完整性", test_documentation()))
    
    # 运行异步测试
    results.append(("异步性能工具", await test_async_performance_tools()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 第三阶段测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 第三阶段所有优化功能测试通过！")
        print("\n📋 第三阶段开发完成情况:")
        print("✅ 功能评估报告 - 完整的功能分析和建议")
        print("✅ 模块化配置 - 清晰的配置管理结构")
        print("✅ 性能优化工具 - 缓存、监控、计时等")
        print("✅ 路由管理器 - 模块化的路由注册")
        print("✅ 代码质量工具 - 自动化代码分析")
        print("✅ 部署指南 - 完整的部署文档")
        print("✅ 优化主应用 - 更好的应用结构")
        print("\n🎯 项目已完成所有三个阶段的开发！")
        print("🚀 可以进行端到端集成测试了")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要修复")
    
    # 运行代码质量检查
    print("\n🔍 运行代码质量检查...")
    try:
        from tools.code_quality_check import CodeAnalyzer
        analyzer = CodeAnalyzer(".")
        report = analyzer.generate_report()
        analyzer.print_summary(report)
    except Exception as e:
        print(f"⚠️  代码质量检查失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
