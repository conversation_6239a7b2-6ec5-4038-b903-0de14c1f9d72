# Alembic 数据库迁移指南

## 概述

本项目使用 Alembic 作为数据库迁移工具。Alembic 是 SQLAlchemy 的官方数据库迁移工具，提供了版本控制、自动生成迁移脚本、回滚等功能。

## 目录结构

```
robot/
├── alembic/                    # Alembic 配置目录
│   ├── versions/              # 迁移脚本目录
│   │   └── xxxx_migration.py  # 具体的迁移文件
│   ├── env.py                 # Alembic 环境配置
│   ├── script.py.mako         # 迁移脚本模板
│   └── README                 # Alembic 说明文件
├── alembic.ini               # Alembic 主配置文件
└── models/                   # SQLModel 模型定义
    ├── user.py
    ├── device.py
    └── ...
```

## 安装和配置

### 1. 安装依赖

```bash
pip install alembic==1.14.0
```

### 2. 配置说明

#### alembic.ini 配置
- 数据库连接URL从 `config.py` 动态获取
- 支持 SQLite 和 PostgreSQL

#### alembic/env.py 配置
- 自动导入项目模型
- 使用 SQLModel.metadata 作为目标元数据
- 支持从环境变量读取数据库配置

## 常用命令

### 1. 创建新迁移

#### 自动生成迁移（推荐）
```bash
# 自动检测模型变化并生成迁移
alembic revision --autogenerate -m "描述信息"

# 示例
alembic revision --autogenerate -m "Add user profile fields"
alembic revision --autogenerate -m "Create payment tables"
```

#### 手动创建空迁移
```bash
# 创建空的迁移文件，需要手动编写
alembic revision -m "描述信息"
```

### 2. 执行迁移

#### 升级到最新版本
```bash
alembic upgrade head
```

#### 升级到指定版本
```bash
alembic upgrade <revision_id>
# 示例: alembic upgrade 6981140045fe
```

#### 升级指定步数
```bash
alembic upgrade +2  # 向前升级2个版本
```

### 3. 回滚迁移

#### 回滚到指定版本
```bash
alembic downgrade <revision_id>
# 示例: alembic downgrade 6981140045fe
```

#### 回滚指定步数
```bash
alembic downgrade -1  # 回滚1个版本
alembic downgrade -2  # 回滚2个版本
```

#### 回滚到初始状态
```bash
alembic downgrade base
```

### 4. 查看迁移状态

#### 查看当前版本
```bash
alembic current
```

#### 查看迁移历史
```bash
alembic history
alembic history --verbose  # 详细信息
```

#### 查看待执行的迁移
```bash
alembic show head
```

## 工作流程

### 1. 开发新功能时

1. **修改模型**：在 `models/` 目录下修改或添加模型
2. **生成迁移**：
   ```bash
   alembic revision --autogenerate -m "Add new feature"
   ```
3. **检查迁移文件**：查看生成的迁移文件是否正确
4. **执行迁移**：
   ```bash
   alembic upgrade head
   ```

### 2. 团队协作时

1. **拉取最新代码**后，执行迁移：
   ```bash
   git pull
   alembic upgrade head
   ```

2. **提交代码前**，确保迁移文件已提交：
   ```bash
   git add alembic/versions/
   git commit -m "Add migration for new feature"
   ```

### 3. 生产环境部署

1. **备份数据库**（重要！）
2. **执行迁移**：
   ```bash
   alembic upgrade head
   ```
3. **验证结果**

## 最佳实践

### 1. 迁移文件命名
- 使用有意义的描述信息
- 英文描述，简洁明了
- 示例：
  - `Add user authentication`
  - `Create order tables`
  - `Add device binding fields`

### 2. 检查自动生成的迁移
自动生成的迁移可能不完美，需要手动检查：
- 确认字段类型正确
- 检查外键约束
- 验证索引创建
- 确认数据迁移逻辑

### 3. 数据迁移
当需要迁移数据时，在迁移文件中添加数据操作：
```python
def upgrade():
    # 结构变更
    op.add_column('users', sa.Column('email', sa.String(255)))
    
    # 数据迁移
    connection = op.get_bind()
    connection.execute(
        "UPDATE users SET email = username || '@example.com' WHERE email IS NULL"
    )
```

### 4. 回滚策略
- 每个 `upgrade()` 都应该有对应的 `downgrade()`
- 测试回滚功能
- 生产环境谨慎使用回滚

### 5. 分支管理
- 不同功能分支可能有不同的迁移
- 合并时注意迁移冲突
- 使用 `alembic merge` 解决分支冲突

## 故障排除

### 1. 迁移冲突
```bash
# 查看冲突
alembic history

# 合并分支
alembic merge -m "Merge migrations" <rev1> <rev2>
```

### 2. 迁移失败
```bash
# 查看当前状态
alembic current

# 手动标记版本（谨慎使用）
alembic stamp <revision_id>
```

### 3. 重置迁移历史
```bash
# 删除所有迁移记录（危险操作）
alembic stamp base
alembic upgrade head
```

## 环境配置

### 开发环境
```bash
# .env 文件
DATABASE_URL=sqlite:///./robot.db
```

### 生产环境
```bash
# .env 文件
DATABASE_URL=postgresql://user:pass@localhost:5432/robot_db
```

## 示例场景

### 场景1：添加新字段
```bash
# 1. 修改模型文件
# 2. 生成迁移
alembic revision --autogenerate -m "Add user phone field"
# 3. 执行迁移
alembic upgrade head
```

### 场景2：创建新表
```bash
# 1. 创建新模型文件
# 2. 在 models/__init__.py 中导入
# 3. 生成迁移
alembic revision --autogenerate -m "Create notification table"
# 4. 执行迁移
alembic upgrade head
```

### 场景3：删除字段
```bash
# 1. 从模型中删除字段
# 2. 生成迁移
alembic revision --autogenerate -m "Remove deprecated field"
# 3. 检查迁移文件，确保数据安全
# 4. 执行迁移
alembic upgrade head
```

## 注意事项

1. **生产环境操作前必须备份数据库**
2. **仔细检查自动生成的迁移文件**
3. **测试迁移的升级和回滚功能**
4. **团队协作时及时同步迁移**
5. **不要手动修改已提交的迁移文件**

## 相关文件

- `alembic.ini` - Alembic 主配置
- `alembic/env.py` - 环境配置
- `alembic/versions/` - 迁移脚本目录
- `models/` - SQLModel 模型定义
- `config.py` - 项目配置（包含数据库URL）
