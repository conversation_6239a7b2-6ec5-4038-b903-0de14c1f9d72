{"openapi": "3.1.0", "info": {"title": "Robot Control API", "description": "Robot Control API for Orange Pi and WebRTC integration", "version": "1.0.0"}, "paths": {"/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON>", "description": "User login endpoint", "operationId": "login_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register", "description": "User registration endpoint", "operationId": "register_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/device/register": {"post": {"tags": ["Device Management"], "summary": "Register Device", "description": "Register a new Orange Pi device", "operationId": "register_device_api_v1_device_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceRegister"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceRegisterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/device/heartbeat": {"post": {"tags": ["Device Management"], "summary": "<PERSON>ce Heartbeat", "description": "Update device heartbeat and status", "operationId": "device_heartbeat_api_v1_device_heartbeat_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceHeartbeat"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceHeartbeatResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/device/bind_access": {"post": {"tags": ["Device Management"], "summary": "Bind Device Access", "description": "Bind WebRTC App user to device using QR code", "operationId": "bind_device_access_api_v1_device_bind_access_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceBindAccess"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceBindResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/device/bound_devices": {"get": {"tags": ["Device Management"], "summary": "Get Bound Devices", "description": "Get all devices accessible by the current user", "operationId": "get_bound_devices_api_v1_device_bound_devices_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DeviceResponse"}, "type": "array", "title": "Response Get Bound Devices Api V1 Device Bound Devices Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/device/bound_users": {"get": {"tags": ["Device Management"], "summary": "Get Bound Users", "description": "Get all users bound to the current device", "operationId": "get_bound_users_api_v1_device_bound_users_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/llm/process_intent": {"post": {"tags": ["LLM Integration"], "summary": "Process Intent", "description": "Process LLM intent and handle shopping requests", "operationId": "process_intent_api_v1_llm_process_intent_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LLMIntentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LLMIntentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/orders/": {"post": {"tags": ["Order"], "summary": "Create Order", "description": "创建订单\n\n根据序列图步骤1: 设备创建订单，返回订单ID", "operationId": "create_order_api_v1_orders__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}": {"get": {"tags": ["Order"], "summary": "Get Order Details", "description": "获取订单详情", "operationId": "get_order_details_api_v1_orders__order_id__get", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}/pay": {"post": {"tags": ["Order"], "summary": "Initiate Payment", "description": "发起支付\n\n根据序列图步骤2-5: 设备发起支付，调用支付平台API，返回二维码URL", "operationId": "initiate_payment_api_v1_orders__order_id__pay_post", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentInitRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}/payment-status": {"get": {"tags": ["Order"], "summary": "Get Payment Status", "description": "查询支付状态\n\n根据序列图步骤11: 设备轮询查询支付状态", "operationId": "get_payment_status_api_v1_orders__order_id__payment_status_get", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/connections": {"get": {"tags": ["WebRTC"], "summary": "Get Active Connections", "description": "获取所有活跃连接", "operationId": "get_active_connections_api_v1_webrtc_connections_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/connections/{connection_id}": {"get": {"tags": ["WebRTC"], "summary": "Get Connection Info", "description": "获取特定连接信息", "operationId": "get_connection_info_api_v1_webrtc_connections__connection_id__get", "parameters": [{"name": "connection_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Connection Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/connections/{connection_id}/terminate": {"post": {"tags": ["WebRTC"], "summary": "Terminate Connection", "description": "终止特定连接", "operationId": "terminate_connection_api_v1_webrtc_connections__connection_id__terminate_post", "parameters": [{"name": "connection_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Connection Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/features": {"get": {"tags": ["WebRTC"], "summary": "Get Available Features", "description": "获取可用功能列表", "operationId": "get_available_features_api_v1_webrtc_features_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/stats": {"get": {"tags": ["WebRTC"], "summary": "Get Webrtc Stats", "description": "获取WebRTC统计信息", "operationId": "get_webrtc_stats_api_v1_webrtc_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/health": {"get": {"tags": ["WebRTC"], "summary": "Webrtc V2 Health", "description": "健康检查", "operationId": "webrtc_v2_health_api_v1_webrtc_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/devices": {"get": {"tags": ["WebRTC"], "summary": "Get User Devices", "description": "获取用户绑定的设备列表", "operationId": "get_user_devices_api_v1_webrtc_devices_get", "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string", "description": "用户认证token", "title": "Token"}, "description": "用户认证token"}, {"name": "include_inactive", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含非活跃设备", "default": false, "title": "Include Inactive"}, "description": "是否包含非活跃设备"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/device/{device_id}/status": {"get": {"tags": ["WebRTC"], "summary": "Get Device Status", "description": "获取设备状态", "operationId": "get_device_status_api_v1_webrtc_device__device_id__status_get", "parameters": [{"name": "device_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON>ce Id"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string", "description": "用户认证token", "title": "Token"}, "description": "用户认证token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/device/{device_id}/bind": {"post": {"tags": ["WebRTC"], "summary": "Bind Device", "description": "绑定设备到用户", "operationId": "bind_device_api_v1_webrtc_device__device_id__bind_post", "parameters": [{"name": "device_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON>ce Id"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string", "description": "用户认证token", "title": "Token"}, "description": "用户认证token"}, {"name": "device_name", "in": "query", "required": false, "schema": {"type": "string", "description": "设备显示名称", "title": "Device Name"}, "description": "设备显示名称"}, {"name": "permission_level", "in": "query", "required": false, "schema": {"type": "string", "description": "权限级别", "default": "control", "title": "Permission Level"}, "description": "权限级别"}, {"name": "expires_days", "in": "query", "required": false, "schema": {"type": "integer", "description": "过期天数", "title": "Expires Days"}, "description": "过期天数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["WebRTC"], "summary": "Unbind <PERSON>ce", "description": "解绑设备", "operationId": "unbind_device_api_v1_webrtc_device__device_id__bind_delete", "parameters": [{"name": "device_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON>ce Id"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string", "description": "用户认证token", "title": "Token"}, "description": "用户认证token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/device/{device_id}/generate-binding-code": {"post": {"tags": ["WebRTC"], "summary": "Generate Device Binding Code", "description": "为设备生成绑定码", "operationId": "generate_device_binding_code_api_v1_webrtc_device__device_id__generate_binding_code_post", "parameters": [{"name": "device_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON>ce Id"}}, {"name": "expires_minutes", "in": "query", "required": false, "schema": {"type": "integer", "description": "绑定码过期时间（分钟）", "default": 30, "title": "Expires Minutes"}, "description": "绑定码过期时间（分钟）"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/device/bind-with-code": {"post": {"tags": ["WebRTC"], "summary": "Bind Device With Code", "description": "使用绑定码绑定设备", "operationId": "bind_device_with_code_api_v1_webrtc_device_bind_with_code_post", "parameters": [{"name": "device_id", "in": "query", "required": true, "schema": {"type": "string", "description": "设备ID", "title": "<PERSON>ce Id"}, "description": "设备ID"}, {"name": "binding_code", "in": "query", "required": true, "schema": {"type": "string", "description": "6位绑定码", "title": "Binding Code"}, "description": "6位绑定码"}, {"name": "device_name", "in": "query", "required": false, "schema": {"type": "string", "description": "设备显示名称", "title": "Device Name"}, "description": "设备显示名称"}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string", "description": "用户认证token", "title": "Token"}, "description": "用户认证token"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/device/{device_id}/binding-info": {"get": {"tags": ["WebRTC"], "summary": "Get Device Binding Info", "description": "获取设备绑定信息（包括绑定码和二维码）", "operationId": "get_device_binding_info_api_v1_webrtc_device__device_id__binding_info_get", "parameters": [{"name": "device_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON>ce Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/status": {"get": {"tags": ["WebRTC"], "summary": "Get Webrtc V2 Status", "description": "获取WebRTC V2状态", "operationId": "get_webrtc_v2_status_api_v1_webrtc_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/sessions/{session_id}": {"get": {"tags": ["WebRTC"], "summary": "Get Session Info", "description": "获取特定会话信息", "operationId": "get_session_info_api_v1_webrtc_sessions__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/sessions/{session_id}/close": {"post": {"tags": ["WebRTC"], "summary": "Close Session", "description": "关闭会话", "operationId": "close_session_api_v1_webrtc_sessions__session_id__close_post", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/webrtc/test/status": {"get": {"tags": ["WebRTC"], "summary": "Get Test Status", "description": "获取测试连接状态", "operationId": "get_test_status_api_v1_webrtc_test_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/webrtc/ping": {"get": {"tags": ["WebRTC"], "summary": "Test Ping", "description": "简单的ping测试", "operationId": "test_ping_api_v1_webrtc_ping_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/shop/payment/callback/{payment_method}": {"post": {"tags": ["Shop & Payment"], "summary": "Payment Callback", "description": "Handle payment platform callback", "operationId": "payment_callback_api_v1_shop_payment_callback__payment_method__post", "parameters": [{"name": "payment_method", "in": "path", "required": true, "schema": {"type": "string", "title": "Payment Method"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/shop/payment/query/{payment_method}/{order_id}": {"get": {"tags": ["Shop & Payment"], "summary": "Query Payment", "description": "查询支付状态", "operationId": "query_payment_api_v1_shop_payment_query__payment_method___order_id__get", "parameters": [{"name": "payment_method", "in": "path", "required": true, "schema": {"type": "string", "title": "Payment Method"}}, {"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/shop/payment/refund/{payment_method}": {"post": {"tags": ["Shop & Payment"], "summary": "Refund Payment", "description": "申请退款", "operationId": "refund_payment_api_v1_shop_payment_refund__payment_method__post", "parameters": [{"name": "payment_method", "in": "path", "required": true, "schema": {"type": "string", "title": "Payment Method"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Refund Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/shop/payment/return/{payment_method}": {"get": {"tags": ["Shop & Payment"], "summary": "Payment Return", "description": "处理支付宝同步跳转返回 (return_url)\n\n用户完成支付后，支付宝会跳转到此地址，并在URL参数中携带支付结果信息\n这个接口主要用于用户体验，显示支付结果页面", "operationId": "payment_return_api_v1_shop_payment_return__payment_method__get", "parameters": [{"name": "payment_method", "in": "path", "required": true, "schema": {"type": "string", "title": "Payment Method"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/staff/orders/pending": {"get": {"tags": ["Staff Operations"], "summary": "Get Pending Orders", "description": "Get pending orders for staff processing", "operationId": "get_pending_orders_api_v1_staff_orders_pending_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderResponse"}, "type": "array", "title": "Response Get Pending Orders Api V1 Staff Orders Pending Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/staff/orders/{order_id}/status": {"put": {"tags": ["Staff Operations"], "summary": "Update Order Status", "description": "Update order processing status", "operationId": "update_order_status_api_v1_staff_orders__order_id__status_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin/login": {"post": {"tags": ["Admin Operations"], "summary": "<PERSON><PERSON>", "description": "Admin login endpoint", "operationId": "admin_login_api_v1_admin_login_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "User Login"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin/users": {"get": {"tags": ["Admin Operations"], "summary": "Get All Users", "description": "Get all users", "operationId": "get_all_users_api_v1_admin_users_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserResponse"}, "type": "array", "title": "Response Get All Users Api V1 Admin Users Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/users/{user_id}": {"get": {"tags": ["Admin Operations"], "summary": "Get User Details", "description": "Get user details", "operationId": "get_user_details_api_v1_admin_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/admin/devices": {"get": {"tags": ["Admin Operations"], "summary": "Get All Devices", "description": "Get all devices", "operationId": "get_all_devices_api_v1_admin_devices_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/DeviceResponse"}, "type": "array", "title": "Response Get All Devices Api V1 Admin Devices Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/orders": {"get": {"tags": ["Admin Operations"], "summary": "Get All Orders", "description": "Get all orders", "operationId": "get_all_orders_api_v1_admin_orders_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderResponse"}, "type": "array", "title": "Response Get All Orders Api V1 Admin Orders Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/system_status": {"get": {"tags": ["Admin Operations"], "summary": "Get System Status", "description": "Get system overview status", "operationId": "get_system_status_api_v1_admin_system_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/logs/info": {"get": {"tags": ["Admin Operations"], "summary": "Get Logs Info", "description": "获取日志配置和状态信息", "operationId": "get_logs_info_api_v1_admin_logs_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/logs/cleanup": {"post": {"tags": ["Admin Operations"], "summary": "Cleanup Logs", "description": "手动清理旧日志文件", "operationId": "cleanup_logs_api_v1_admin_logs_cleanup_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/": {"get": {"summary": "Root", "description": "Root endpoint", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/ping": {"get": {"summary": "Test Ping", "description": "简单的ping测试", "operationId": "test_ping_ping_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/test-status": {"get": {"summary": "Test Status", "description": "获取测试状态", "operationId": "test_status_test_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"DeviceBindAccess": {"properties": {"qr_code_data": {"type": "string", "title": "Qr Code Data"}}, "type": "object", "required": ["qr_code_data"], "title": "DeviceBindAccess"}, "DeviceBindResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "device_id": {"type": "string", "title": "<PERSON>ce Id"}, "user_id": {"type": "string", "title": "User Id"}}, "type": "object", "required": ["message", "device_id", "user_id"], "title": "DeviceBindResponse"}, "DeviceHeartbeat": {"properties": {"status": {"type": "string", "title": "Status"}, "battery_level": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Battery Level"}, "firmware_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firmware Version"}}, "type": "object", "required": ["status"], "title": "DeviceHeartbeat"}, "DeviceHeartbeatResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}}, "type": "object", "required": ["message", "timestamp"], "title": "DeviceHeartbeatResponse"}, "DeviceRegister": {"properties": {"device_id": {"type": "string", "title": "<PERSON>ce Id"}, "device_name": {"type": "string", "title": "Device Name"}, "device_type": {"type": "string", "title": "Device Type"}}, "type": "object", "required": ["device_id", "device_name", "device_type"], "title": "DeviceRegister"}, "DeviceRegisterResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "device_token": {"type": "string", "title": "<PERSON><PERSON>"}, "device_id": {"type": "string", "title": "<PERSON>ce Id"}, "binding_qr_code_data": {"type": "string", "title": "Binding Qr Code Data"}}, "type": "object", "required": ["message", "device_token", "device_id", "binding_qr_code_data"], "title": "DeviceRegisterResponse"}, "DeviceResponse": {"properties": {"device_id": {"type": "string", "title": "<PERSON>ce Id"}, "device_name": {"type": "string", "title": "Device Name"}, "status": {"type": "string", "title": "Status"}, "last_heartbeat_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Heartbeat At"}}, "type": "object", "required": ["device_id", "device_name", "status"], "title": "DeviceResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Ingredient": {"properties": {"name": {"type": "string", "title": "Name"}, "quantity": {"type": "number", "title": "Quantity"}, "unit": {"type": "string", "title": "Unit"}}, "type": "object", "required": ["name", "quantity", "unit"], "title": "Ingredient"}, "LLMIntentRequest": {"properties": {"device_id": {"type": "string", "title": "<PERSON>ce Id"}, "intent_type": {"type": "string", "title": "Intent Type"}, "raw_query": {"type": "string", "title": "Raw Query"}, "structured_data": {"$ref": "#/components/schemas/StructuredData"}}, "type": "object", "required": ["device_id", "intent_type", "raw_query", "structured_data"], "title": "LLMIntentRequest"}, "LLMIntentResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "action_required": {"type": "string", "title": "Action Required"}, "shopping_list": {"items": {"$ref": "#/components/schemas/ShoppingListItem"}, "type": "array", "title": "Shopping List", "default": []}, "total_amount": {"type": "number", "title": "Total Amount", "default": 0.0}, "payment_qr_code_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Qr Code Url"}}, "type": "object", "required": ["message", "action_required"], "title": "LLMIntentResponse"}, "OrderCreate": {"properties": {"device_id": {"type": "string", "title": "<PERSON>ce Id"}, "items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Items"}, "total_amount": {"type": "number", "title": "Total Amount"}}, "type": "object", "required": ["device_id", "items", "total_amount"], "title": "OrderCreate"}, "OrderItemResponse": {"properties": {"product_id": {"type": "string", "title": "Product Id"}, "product_name": {"type": "string", "title": "Product Name"}, "quantity": {"type": "integer", "title": "Quantity"}, "price_at_purchase": {"type": "number", "title": "Price At Purchase"}, "total_price": {"type": "number", "title": "Total Price"}}, "type": "object", "required": ["product_id", "product_name", "quantity", "price_at_purchase", "total_price"], "title": "OrderItemResponse"}, "OrderResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "device_id": {"type": "string", "title": "<PERSON>ce Id"}, "total_amount": {"type": "number", "title": "Total Amount"}, "status": {"type": "string", "title": "Status"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "items": {"items": {"$ref": "#/components/schemas/OrderItemResponse"}, "type": "array", "title": "Items", "default": []}}, "type": "object", "required": ["id", "device_id", "total_amount", "status", "created_at"], "title": "OrderResponse"}, "OrderStatusUpdate": {"properties": {"status": {"type": "string", "title": "Status"}}, "type": "object", "required": ["status"], "title": "OrderStatusUpdate"}, "PaymentInitRequest": {"properties": {"payment_method": {"type": "string", "title": "Payment Method"}}, "type": "object", "required": ["payment_method"], "title": "PaymentInitRequest", "description": "支付发起请求"}, "PaymentResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "payment_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Url"}, "qr_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code"}, "order_id": {"type": "string", "title": "Order Id"}, "payment_method": {"type": "string", "title": "Payment Method"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}, "error_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Code"}}, "type": "object", "required": ["success", "order_id", "payment_method"], "title": "PaymentResponse", "description": "支付响应"}, "ShoppingListItem": {"properties": {"product_id": {"type": "string", "title": "Product Id"}, "product_name": {"type": "string", "title": "Product Name"}, "matched_ingredient": {"type": "string", "title": "Matched Ingredient"}, "price_per_unit": {"type": "number", "title": "Price Per Unit"}, "quantity": {"type": "integer", "title": "Quantity"}, "total_price": {"type": "number", "title": "Total Price"}, "unit": {"type": "string", "title": "Unit"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}}, "type": "object", "required": ["product_id", "product_name", "matched_ingredient", "price_per_unit", "quantity", "total_price", "unit"], "title": "ShoppingListItem"}, "StructuredData": {"properties": {"recipe_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recipe Name"}, "ingredients": {"items": {"$ref": "#/components/schemas/Ingredient"}, "type": "array", "title": "Ingredients", "default": []}}, "type": "object", "title": "StructuredData"}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In"}, "user_id": {"anyOf": [{"type": "integer"}, {"type": "string"}], "title": "User Id", "description": "用户ID，支持整数或字符串"}, "username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Role"}}, "type": "object", "required": ["access_token", "expires_in", "user_id"], "title": "Token"}, "UserCreate": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "role": {"type": "string", "title": "Role", "default": "webrtc_app_user"}}, "type": "object", "required": ["username", "password"], "title": "UserCreate"}, "UserLogin": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "password"], "title": "UserLogin"}, "UserResponse": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "string"}], "title": "Id", "description": "用户ID，支持整数或字符串"}, "username": {"type": "string", "title": "Username"}, "role": {"type": "string", "title": "Role"}, "is_active": {"type": "boolean", "title": "Is Active"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "username", "role", "is_active", "created_at"], "title": "UserResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}