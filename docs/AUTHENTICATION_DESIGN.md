# 统一认证设计文档

**版本**: v2.0  
**更新日期**: 2025年8月4日  
**变更**: 统一所有角色的登录接口

## 🎯 设计原则

### KISS原则 (Keep It Simple and Stupid)
- **单一登录接口**: 所有角色使用同一个登录端点
- **统一认证流程**: 避免重复的认证逻辑
- **简化维护**: 减少代码重复和维护成本

## 👥 用户角色定义

### 1. webrtc_app_user (普通用户)
- **描述**: 使用WebRTC功能的普通用户
- **权限**: 
  - 创建和管理自己的订单
  - 使用WebRTC视频通话功能
  - 查看自己的订单状态
- **典型场景**: 手机APP用户、设备用户

### 2. staff (员工)
- **描述**: 负责订单履约的员工
- **权限**:
  - 查看活动订单列表
  - 更新订单备料状态
  - 记录视频流信息
  - 操作订单状态转换
- **典型场景**: 备料员工、厨房工作人员

### 3. admin (管理员)
- **描述**: 系统管理员
- **权限**:
  - 所有员工权限
  - 用户管理
  - 系统监控
  - 配置管理
- **典型场景**: 系统运维人员、店长

## 🔐 统一认证接口

### 登录接口
```
POST /api/v1/auth/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应体**:
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_id": "uuid",
  "username": "string",
  "role": "webrtc_app_user|staff|admin"
}
```

### 注册接口
```
POST /api/v1/auth/register
```

**请求体**:
```json
{
  "username": "string",
  "password": "string",
  "role": "webrtc_app_user|staff|admin"
}
```

## 🛡️ 权限控制

### 基于角色的访问控制 (RBAC)

#### 接口权限矩阵

| 接口路径 | webrtc_app_user | staff | admin |
|---------|----------------|-------|-------|
| `POST /api/v1/orders/` | ✅ 自己的订单 | ❌ | ✅ |
| `GET /api/v1/orders/{id}` | ✅ 自己的订单 | ✅ | ✅ |
| `GET /api/v1/staff/orders/active` | ❌ | ✅ | ✅ |
| `PUT /api/v1/staff/orders/{id}/kitchen_status` | ❌ | ✅ | ✅ |
| `POST /api/v1/orders/{id}/confirm_payment` | ❌ | ❌ | ✅ |
| `GET /api/v1/admin/*` | ❌ | ❌ | ✅ |
| `POST /api/v1/webrtc/*` | ✅ | ✅ | ✅ |

### 权限验证中间件

```python
# 示例：员工权限验证
def require_staff(current_user: User = Depends(get_current_user)):
    if current_user.role not in ["staff", "admin"]:
        raise HTTPException(
            status_code=403,
            detail="Staff access required"
        )
    return current_user

# 示例：管理员权限验证  
def require_admin(current_user: User = Depends(get_current_user)):
    if current_user.role != "admin":
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    return current_user
```

## 🔄 迁移说明

### 删除的接口
- ❌ `POST /api/v1/admin/login` - 已删除，使用统一登录接口

### 保留的接口
- ✅ `POST /api/v1/auth/login` - 统一登录接口，支持所有角色
- ✅ `POST /api/v1/auth/register` - 统一注册接口

### 客户端迁移指南

#### 管理员登录 (修改前)
```javascript
// 旧方式 - 不再支持
const response = await fetch('/api/v1/admin/login', {
    method: 'POST',
    body: JSON.stringify({username, password})
});
```

#### 统一登录 (修改后)
```javascript
// 新方式 - 所有角色统一使用
const response = await fetch('/api/v1/auth/login', {
    method: 'POST', 
    body: JSON.stringify({username, password})
});

const result = await response.json();
// 根据返回的role字段判断用户类型
if (result.role === 'admin') {
    // 管理员逻辑
} else if (result.role === 'staff') {
    // 员工逻辑  
} else {
    // 普通用户逻辑
}
```

## 📊 优势总结

### 1. 简化架构
- **单一认证端点**: 减少接口数量
- **统一认证逻辑**: 避免代码重复
- **一致的错误处理**: 统一的错误响应格式

### 2. 提高安全性
- **集中的安全控制**: 所有认证逻辑在一处管理
- **统一的日志记录**: 便于安全审计
- **一致的令牌管理**: 统一的JWT生成和验证

### 3. 改善用户体验
- **统一的前端逻辑**: 简化客户端实现
- **一致的响应格式**: 减少前端适配工作
- **清晰的角色区分**: 基于role字段的权限判断

### 4. 便于维护
- **减少重复代码**: 遵循DRY原则
- **集中的权限管理**: 便于权限策略调整
- **简化测试**: 减少需要测试的接口数量

## 🧪 测试用例

### 普通用户登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "user1", "password": "password"}'
```

### 员工登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "staff1", "password": "password"}'
```

### 管理员登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin1", "password": "password"}'
```

## 🔮 未来扩展

### 可能的角色扩展
- `kitchen_manager`: 厨房经理
- `delivery_staff`: 配送员工
- `customer_service`: 客服人员

### 权限细化
- 基于资源的权限控制
- 动态权限分配
- 权限继承机制

---

**注意**: 此设计变更需要更新所有相关的客户端代码，确保使用统一的登录接口。
