### **完整文档：AI订单履约系统 (MVP阶段)**

**文档版本:** MVP v1.0
**日期:** 2025年8月4日
**状态:** 已批准开发

---

### **第一部分: 产品需求文档 (PRD)**

#### **1. 引言与愿景**

*   **1.1 产品:** AI订单履约系统 (MVP)
*   **1.2 愿景:** 我们的愿景是成为AI驱动的点餐体验中最可靠、最透明的**备料管理与追溯引擎**。在MVP阶段，系统将精准聚焦于从**订单接收到原料处理完成**的全过程，通过视频录像功能确保备料环节的透明与安全，为后续的烹饪流程输出标准化、可追溯的原料。
*   **1.3 MVP范围定义:**
    *   **范围内 (In Scope):**
        *   用户认证服务。
        *   用于接收订单的API接口。
        *   支付处理与安全验证。
        *   **备料工单**的生成与管理。
        *   **备料状态**（待处理、处理中、已完成）的实时更新。
        *   **备料过程**的视频录制与订单关联。
        *   为下游环节提供备料完成的通知。
    *   **范围外 (Out of Scope):**
        *   AI对话与菜品推荐界面 (由外部服务负责)。
        *   餐品的**最终烹饪**环节。
        *   餐品的**打包与装箱**。
        *   面向最终顾客的**配送**流程。

#### **2. 用户画像**

| 角色 | 描述 | 核心需求 |
| :--- | :--- | :--- |
| **备料员工** | 餐厅中负责根据订单要求，处理和准备烹饪前原料的员工。 | 1. 清晰的备料任务列表。<br>2. 简单的操作更新任务状态（开始/完成）。<br>3. 无需分心的自动化视频录制。 |
| **下游环节人员 (如厨师)** | 在备料员工完成后，从备料区领取已处理好原料的员工。 | 1. 明确知道哪些订单的原料已经备好，可以随时领取。<br>2. 减少等待时间，提升整体厨房效率。 |
| **系统管理员** | 餐厅经理、品控负责人或客服人员。 | 1. 能够追溯任何订单的原料处理过程。<br>2. 通过查看备料视频来确保操作合规或解决原料质量争议。 |
| **AI助手服务 (系统角色)** | 外部的AI对话与推荐系统。 | 一个稳定、安全且文档清晰的API，用于将用户最终确认的菜品订单传递给我们的系统进行处理。 |

#### **3. 功能与用户故事**

**Epic 1: 核心认证与订单接收**
*   **F-1.1: 用户认证:** 提供标准的用户认证功能。
    *   **用户故事:** 作为一个客户端App，我需要通过履约系统对我的用户进行身份验证，以获取一个安全令牌（Token），然后我会将这个令牌传递给AI助手服务用于所有后续操作。
*   **F-1.2: 订单创建接口:**
    *   **用户故事:** 作为一个AI助手服务，在用户与我完成对话并确认菜品后，我希望能调用一个接口，将用户选择的菜品列表和他的Token传递过去，从而在履约系统中创建一个待处理的订单。

**Epic 2: 备料工作流与视频追溯**
*   **F-2.1: 自动生成备料任务:**
    *   **用户故事:** 作为一个**备料员工**，当一个新订单支付成功后，我希望在我的员工端App上能立刻看到一个新的备料任务，上面清楚地列出了需要准备的原料和规格，我无需手动抄写。
*   **F-2.2: 备料状态更新:**
    *   **用户故事:** 作为一个**备料员工**，我需要能在我App上清晰地点击“**开始备料**”按钮，让系统和同事知道我已接手这个任务。完成后，我再点击“**备料完成**”，将它从我的待办列表中移除。
*   **F-2.3: 备料过程视频追溯:**
    *   **用户故事:** 作为一个**系统管理员**，我需要任何一份备料任务都有对应的处理视频。当出现食品安全问题或原料质量争议时，我能立刻调出视频作为客观依据，而不是依赖口头描述。
*   **F-2.4: 备料完成通知:**
    *   **用户故事:** 作为一个**下游环节的厨师**，我希望在我工作台的屏幕上能实时看到哪些订单的原料已经“备料完成”，这样我就可以直接去领取，而不需要反复询问备料员工，从而无缝衔接我的烹饪工作。

#### **4. 非功能性需求**

*   **性能:** `POST /api/v1/orders` 接口的p95响应时间必须在300毫秒以下。
*   **安全:** 所有接口必须经过认证。支付流程必须包含服务端验证。
*   **可靠性:** 系统必须保证已支付订单的零丢失。订单状态的转换必须是事务性的，防止出现中间状态。
*   **可扩展性:** 架构设计应能平滑地从MVP阶段演进到完整版，例如方便地在状态机中加入“烹饪中”、“待配送”等新状态。

---

### **第二部分: 技术设计文档 (TDD风格)**

**文档版本:** MVP v1.0
**架构:** 模块化单体
**技术栈:** Python 3.11+, FastAPI, SQLModel, Celery, Redis, PostgreSQL, Boto3, Python-JOSE。

#### **1. 系统架构与项目结构**

系统宏观架构（参考上一版图示）和模块化单体的目录结构依然有效，无需修改。我们将主要在`orders`和`kitchen`模块内部进行逻辑和模型的调整。

#### **2. `orders` 模块设计**

*   **核心模型变更 (`models.py`):**
    *   **`OrderStatus` 枚举是本次MVP最核心的修改点，它定义了新的工作流。**
    ```python
    from enum import Enum
    
    # MVP阶段的订单状态机
    class OrderStatus(str, Enum):
        PENDING_PAYMENT = "PENDING_PAYMENT"  # 待支付
        PAID = "PAID"                        # 已支付 (对备料员工来说，这是“待处理”的任务)
        PROCESSING = "PROCESSING"            # 原料处理中
        READY_FOR_PICKUP = "READY_FOR_PICKUP" # 备料完成，待（下游）领取
        COMPLETED = "COMPLETED"              # 已被下游领取，流程完成
        CANCELLED = "CANCELLED"              # 已取消
    ```
    *   `Order` 和 `OrderItem` 模型的字段结构无需改变，其`status`字段将直接使用上述新的`OrderStatus`枚举。

*   **API接口与TDD案例:**
    *   `POST /api/v1/orders`:
        *   **说明:** 此接口的对外契约（请求体、响应体）**保持不变**。这确保了与AI助手服务的解耦，他们无需关心我们内部流程的简化。
        *   **内部逻辑:** 当订单创建时，它依然被设置为`PENDING_PAYMENT`。
    *   `POST /api/v1/orders/{order_id}/confirm_payment`:
        *   **说明:** 此接口的契约和调用方（客户端App）也**保持不变**。
        *   **TDD案例 (逻辑微调):**
            1.  `test_confirm_payment_success`: **Given** 一个有效的支付凭证, **When** 调用此接口, **Then** 模拟支付网关验证成功, 数据库中订单状态更新为`PAID`, 并且一个`send_kitchen_new_order_notification` Celery任务被成功触发（通知备料员工有新任务）。

#### **3. `kitchen` 模块设计**

*   **API接口与TDD案例 (`router.py`):**
    *   `GET /api/v1/staff/orders/active`:
        *   **业务逻辑变更:** 此接口现在用于获取备料员工的活动任务列表。它应查询数据库中状态为 `PAID` (待开始处理) 或 `PROCESSING` (正在处理中) 的所有订单。
    *   `PUT /api/v1/staff/orders/{order_id}/status`:
        *   **请求体Schema变更 (`schemas.py`):**
          ```python
          from pydantic import BaseModel
          from typing import Literal
          from app.orders.models import OrderStatus # 引入新的枚举
          
          class KitchenStatusUpdate(BaseModel):
              status: Literal[OrderStatus.PROCESSING, OrderStatus.READY_FOR_PICKUP]
              video_stream_id: Optional[str] = None
          ```
        *   **TDD案例 (根据新流程重写):**
            1.  `test_update_status_from_paid_to_processing_success`:
                *   **Given:** 一个ID为`123`，状态为`PAID`的订单。
                *   **When:** 备料员工调用`PUT /api/v1/staff/orders/123/status`，请求体为`{"status": "PROCESSING", "video_stream_id": "vid_abc"}`。
                *   **Then:** 数据库中该订单的状态变为`PROCESSING`，`video_stream_id`字段被更新为`vid_abc`，接口返回`200 OK`。
            2.  `test_update_status_from_processing_to_ready_for_pickup_success`:
                *   **Given:** 一个ID为`123`，状态为`PROCESSING`的订单。
                *   **When:** 备料员工调用此接口，请求体为`{"status": "READY_FOR_PICKUP"}`。
                *   **Then:** 订单状态变为`READY_FOR_PICKUP`，并成功触发一个名为`notify_downstream_on_pickup_ready`的Celery异步任务，接口返回`200 OK`。
            3.  `test_update_status_fails_on_invalid_transition`:
                *   **Given:** 一个ID为`123`，状态为`PAID`的订单。
                *   **When:** 尝试直接调用接口将其状态更新为`READY_FOR_PICKUP`。
                *   **Then:** 接口应返回`409 Conflict`，响应体中包含错误信息，如`{"detail": "无效的状态转换：无法从 PAID 直接变为 READY_FOR_PICKUP"}`。

#### **4. `notifications` 模块设计**

*   **Celery 任务 (`tasks.py`):**
    ```python
    from celery import shared_task
    import logging

    @shared_task
    def send_kitchen_new_order_notification(order_id: int):
        # 逻辑不变：通知备料员工有新的任务 (例如通过WebSocket或轮询刷新)
        logging.info(f"通知备料区：新任务 {order_id} 已到达。")
        ...

    # 任务重命名并修改逻辑以适应MVP
    @shared_task
    def notify_downstream_on_pickup_ready(order_id: int):
        # MVP逻辑：通知下游环节原料已备好
        # 在MVP阶段，这可能只是记录一条详细日志，或向一个预设的webhook URL发送HTTP POST请求
        logging.info(f"通知下游环节：订单 {order_id} 的原料已备好，可以领取。")
        # 示例：requests.post('https://kitchen-display.internal/api/update', json={'order_id': order_id, 'status': 'READY'})
        ...
    ```
*   **TDD案例 (任务单元测试):**
    1.  `test_notify_downstream_task_logs_correctly`:
        *   **Given:** 一个 `order_id`。
        *   **When:** 直接调用 `notify_downstream_on_pickup_ready(123)`。
        *   **Then:** 断言`logging`模块捕获到了包含`"订单 123 的原料已备好"`的INFO级别日志。
