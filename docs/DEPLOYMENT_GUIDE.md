# 部署指南

**版本:** v1.0  
**更新日期:** 2025年8月4日  

## 📋 概述

本指南提供了Robot AI订单履约系统的完整部署方案，包括开发环境、测试环境和生产环境的配置。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端客户端     │    │   负载均衡器     │    │   FastAPI应用    │
│  (WebRTC客户端) │◄──►│   (Nginx)      │◄──►│   (多实例)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   消息队列      │    │   数据库        │
                       │   (Redis)      │◄──►│ (PostgreSQL)   │
                       └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   异步任务      │
                       │   (Celery)     │
                       └─────────────────┘
```

## 🔧 环境要求

### 最低系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐生产环境
- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 4核心+
- **内存**: 8GB+ RAM
- **存储**: 100GB+ SSD
- **网络**: 1Gbps+ 带宽

### 软件依赖
- **Python**: 3.9+
- **PostgreSQL**: 13+
- **Redis**: 6.0+
- **Nginx**: 1.18+ (生产环境)
- **Docker**: 20.10+ (可选)

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd robot
```

### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 安装依赖
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 4. 数据库初始化
```bash
# 创建数据库
createdb robot_db

# 运行迁移
alembic upgrade head
```

### 5. 启动服务
```bash
# 开发环境
python main_optimized.py

# 或使用uvicorn
uvicorn main_optimized:app --host 0.0.0.0 --port 8000 --reload
```

## 🐳 Docker部署

### 1. 构建镜像
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main_optimized:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/robot_db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: robot_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  celery:
    build: .
    command: celery -A celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=**********************************/robot_db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:
  redis_data:
```

### 3. 启动容器
```bash
docker-compose up -d
```

## ⚙️ 环境配置

### 开发环境 (.env.development)
```env
# 应用配置
DEBUG=true
LOG_LEVEL=DEBUG

# 数据库
DATABASE_URL=postgresql://user:password@localhost:5432/robot_dev

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 功能开关
FEATURE_ENABLE_WEBRTC=true
FEATURE_ENABLE_LLM_INTEGRATION=true
FEATURE_ENABLE_ADMIN_PANEL=true
```

### 生产环境 (.env.production)
```env
# 应用配置
DEBUG=false
LOG_LEVEL=INFO
SECRET_KEY=your-super-secret-key-here

# 数据库
DATABASE_URL=*****************************************/robot_prod
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# Redis
REDIS_HOST=redis-server
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 安全配置
ALLOWED_ORIGINS=["https://yourdomain.com"]

# 支付配置
PAYMENT_ALIPAY_APP_ID=your-alipay-app-id
PAYMENT_ALIPAY_PRIVATE_KEY=your-private-key

# 通知配置
NOTIFICATION_ENABLE_PUSH_NOTIFICATIONS=true
NOTIFICATION_PUSH_APP_KEY=your-push-app-key
```

## 🔒 安全配置

### 1. SSL/TLS配置
```nginx
# /etc/nginx/sites-available/robot
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 2. 防火墙配置
```bash
# UFW配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 3. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER robot_user WITH PASSWORD 'strong_password';
GRANT CONNECT ON DATABASE robot_db TO robot_user;
GRANT USAGE ON SCHEMA public TO robot_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO robot_user;
```

## 📊 监控和日志

### 1. 日志配置
```python
# config/logging.py
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/app.log",
            "maxBytes": 100 * 1024 * 1024,  # 100MB
            "backupCount": 10,
            "formatter": "detailed",
        },
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file"],
    },
}
```

### 2. 健康检查
```bash
# 健康检查脚本
#!/bin/bash
curl -f http://localhost:8000/health || exit 1
```

### 3. 性能监控
```bash
# 使用systemd监控服务
sudo systemctl status robot-api
sudo journalctl -u robot-api -f
```

## 🔄 CI/CD配置

### GitHub Actions示例
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
      - name: Run tests
        run: |
          python -m pytest tests/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to server
        run: |
          # 部署脚本
          ssh user@server 'cd /app && git pull && docker-compose up -d --build'
```

## 🛠️ 故障排查

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   sudo systemctl status postgresql
   
   # 检查连接
   psql -h localhost -U user -d robot_db
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   sudo systemctl status redis
   
   # 测试连接
   redis-cli ping
   ```

3. **端口占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :8000
   
   # 杀死进程
   sudo kill -9 <PID>
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la logs/
   
   # 修改权限
   chmod 755 logs/
   chown user:user logs/
   ```

### 日志分析
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看性能日志
grep "Slow operation" logs/app.log
```

## 📈 性能优化

### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX CONCURRENTLY idx_orders_status_created ON orders(status, created_at);
CREATE INDEX CONCURRENTLY idx_orders_device_id ON orders(device_id);

-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM orders WHERE status = 'processing';
```

### 2. 应用优化
```python
# 使用连接池
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

### 3. 缓存优化
```python
# Redis缓存配置
REDIS_CONFIG = {
    "max_connections": 50,
    "retry_on_timeout": True,
    "socket_timeout": 5.0,
}
```

## 🔄 备份和恢复

### 数据库备份
```bash
# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump robot_db > backup_${DATE}.sql
aws s3 cp backup_${DATE}.sql s3://your-backup-bucket/
```

### 恢复数据
```bash
# 恢复数据库
psql robot_db < backup_20250804_120000.sql
```

## 📞 支持和维护

### 联系信息
- **技术支持**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx
- **文档更新**: <EMAIL>

### 维护计划
- **日常检查**: 每日监控系统状态
- **定期备份**: 每日自动备份数据库
- **安全更新**: 每月更新系统补丁
- **性能优化**: 每季度性能评估
