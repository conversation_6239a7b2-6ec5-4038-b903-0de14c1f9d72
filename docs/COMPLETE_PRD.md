# 智能设备管理与订单履约系统 - 完整产品需求文档 (PRD)

**文档版本**: v2.0  
**创建日期**: 2025年8月4日  
**更新日期**: 2025年8月4日  
**项目状态**: 生产就绪  
**目标客户**: 中国市场客户

---

## 📋 目录

1. [产品概述](#产品概述)
2. [产品愿景与目标](#产品愿景与目标)
3. [用户角色定义](#用户角色定义)
4. [核心功能模块](#核心功能模块)
5. [业务流程](#业务流程)
6. [技术架构](#技术架构)
7. [非功能性需求](#非功能性需求)
8. [客户端应用](#客户端应用)
9. [集成与扩展](#集成与扩展)
10. [实施计划](#实施计划)

---

## 🎯 产品概述

### 产品定义
智能设备管理与订单履约系统是一个综合性的数字化平台，集成了**WebRTC实时通信**、**智能设备管理**、**订单处理**、**支付集成**和**推送通知**等核心功能，为中国市场客户提供完整的智能设备远程管理和订单履约解决方案。

### 核心价值主张
- 🤖 **智能设备远程管理**: 支持Orange Pi等智能设备的注册、监控和远程控制
- 📹 **实时视频通信**: 基于WebRTC的高质量视频通话和监控
- 🛒 **完整订单履约**: 从订单创建到支付完成的全流程管理
- 💳 **本土化支付**: 深度集成支付宝支付，支持QR码支付流程
- 📱 **多端协同**: 支持设备端、移动端、Web端和管理端的无缝协作
- 🔔 **智能推送**: 基于极光推送的实时通知系统

---

## 🚀 产品愿景与目标

### 愿景声明
成为中国市场领先的**智能设备管理与订单履约一体化平台**，通过先进的WebRTC技术和本土化的支付集成，为企业提供高效、安全、易用的数字化解决方案。

### 产品目标
1. **技术目标**
   - WebRTC连接成功率 > 95%
   - 支付处理响应时间 < 300ms
   - 系统可用性 > 99.5%
   - 支持1000+并发设备连接

2. **业务目标**
   - 支持多种智能设备类型
   - 完整的订单履约闭环
   - 本土化支付体验
   - 可扩展的推送通知架构

3. **用户体验目标**
   - 一键设备绑定和管理
   - 流畅的视频通话体验
   - 简化的支付流程
   - 实时的状态通知

---

## 👥 用户角色定义

### 1. 普通用户 (webrtc_app_user)
**描述**: 使用移动应用或Web客户端的最终用户
**核心需求**:
- 设备绑定和管理
- 发起视频通话
- 创建和管理订单
- 查看订单状态和支付

**典型场景**:
- 家庭用户远程监控智能摄像头
- 企业用户管理办公设备
- 客户通过设备下单购买产品

### 2. 设备 (device)
**描述**: Orange Pi等智能硬件设备
**核心需求**:
- 自动注册和认证
- 接收和处理视频通话
- 订单信息显示
- 状态上报和监控

**典型场景**:
- 智能摄像头设备
- 智能显示屏
- IoT传感器设备

### 3. 员工 (staff)
**描述**: 负责订单履约和设备维护的工作人员
**核心需求**:
- 查看活动订单列表
- 更新订单处理状态
- 记录视频流信息
- 接收推送通知

**典型场景**:
- 备料员工处理订单
- 技术人员维护设备
- 客服人员处理问题

### 4. 管理员 (admin)
**描述**: 系统管理员和业务管理人员
**核心需求**:
- 用户和设备管理
- 订单监控和分析
- 系统配置和维护
- 数据统计和报告

**典型场景**:
- IT管理员系统维护
- 业务经理监控运营
- 财务人员对账管理

---

## 🔧 核心功能模块

### 1. 用户认证系统
**功能描述**: 统一的多角色认证和授权管理

**核心特性**:
- 统一登录接口 (`POST /api/v1/auth/login`)
- JWT Token认证机制
- 基于角色的权限控制 (RBAC)
- 安全的密码存储和验证

**API接口**:
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
GET  /api/v1/auth/me             # 获取当前用户信息
POST /api/v1/auth/refresh        # 刷新Token
```

### 2. 设备管理系统
**功能描述**: 智能设备的注册、绑定、监控和管理

**核心特性**:
- 设备自动注册和认证
- 用户设备绑定机制
- 设备状态实时监控
- 设备权限管理

**API接口**:
```
POST /api/v1/devices/register    # 设备注册
GET  /api/v1/devices/            # 获取设备列表
POST /api/v1/devices/bind        # 设备绑定
PUT  /api/v1/devices/{id}/status # 更新设备状态
```

**设备绑定流程**:
1. 设备生成绑定码和QR码
2. 用户扫描QR码或输入绑定码
3. 系统验证并建立绑定关系
4. 设置访问权限级别

### 3. WebRTC通信系统
**功能描述**: 基于WebRTC的实时视频通话和数据传输

**核心特性**:
- P2P视频通话建立
- 信令服务器协调
- 多媒体流管理
- 连接状态监控

**WebSocket接口**:
```
WS /api/v1/webrtc/connect        # WebRTC连接建立
```

**通话流程**:
1. 用户发起通话请求
2. 信令服务器协调连接
3. 建立P2P连接
4. 开始音视频传输
5. 通话结束和资源清理

### 4. 订单管理系统
**功能描述**: 完整的订单生命周期管理

**核心特性**:
- 订单创建和编辑
- 订单状态机管理
- 订单项目管理
- 订单历史追踪

**订单状态流转**:
```
PENDING_PAYMENT → PAID → PROCESSING → READY_FOR_PICKUP → COMPLETED
                    ↓
                CANCELLED
```

**API接口**:
```
POST /api/v1/orders/             # 创建订单
GET  /api/v1/orders/             # 获取订单列表
GET  /api/v1/orders/{id}         # 获取订单详情
PUT  /api/v1/orders/{id}/status  # 更新订单状态
```

### 5. 支付集成系统
**功能描述**: 支付宝支付集成和支付流程管理

**核心特性**:
- 支付宝SDK集成
- QR码支付支持
- 支付状态查询
- 支付回调处理
- 支付安全验证

**支付流程**:
1. 创建支付订单
2. 生成支付宝支付链接
3. 用户扫码或跳转支付
4. 接收支付回调通知
5. 验证支付结果
6. 更新订单状态

**API接口**:
```
POST /api/v1/orders/{id}/pay     # 发起支付
GET  /api/v1/orders/{id}/payment # 查询支付状态
POST /api/v1/payment/callback    # 支付回调接口
```

### 6. 推送通知系统
**功能描述**: 基于适配器模式的多渠道推送通知

**核心特性**:
- 极光推送 (JPUSH) 集成
- 多渠道通知支持
- 推送模板管理
- 通知历史记录

**推送渠道**:
- 📱 移动推送 (极光推送)
- 🌐 WebSocket实时通知
- 📧 邮件通知 (可扩展)
- 💾 数据库通知记录

**通知类型**:
- 💰 支付成功通知
- 📦 订单状态变更
- ⚠️ 系统异常告警
- 📱 设备状态变化

---

## 🔄 业务流程

### 主要业务流程图

#### 1. 设备注册与绑定流程
```
设备启动 → 设备注册 → 生成绑定码 → 用户扫码 → 建立绑定 → 设备可用
```

#### 2. 视频通话流程
```
用户登录 → 选择设备 → 发起通话 → 设备响应 → 建立连接 → 开始通话 → 结束通话
```

#### 3. 订单支付流程
```
创建订单 → 选择支付方式 → 生成支付链接 → 用户支付 → 支付回调 → 更新状态 → 推送通知
```

#### 4. 订单履约流程
```
支付成功 → 生成工单 → 员工接单 → 开始处理 → 完成处理 → 通知下游 → 订单完成
```

---

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动客户端     │    │   Web客户端     │    │   设备客户端     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │  (FastAPI)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   认证服务       │    │   设备服务       │    │   订单服务       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据存储层     │
                    │ PostgreSQL/SQLite│
                    └─────────────────┘
```

### 技术栈详情

**后端技术**:
- **框架**: FastAPI 0.104+
- **数据库**: SQLModel + PostgreSQL/SQLite
- **缓存**: Redis 6.0+
- **消息队列**: Celery + Redis
- **WebRTC**: aiortc
- **认证**: JWT + python-jose

**前端技术**:
- **Web客户端**: HTML5 + JavaScript + WebRTC API
- **移动客户端**: 响应式Web应用
- **设备客户端**: 嵌入式Web界面

**第三方集成**:
- **支付**: 支付宝SDK (alipay-sdk-python)
- **推送**: 极光推送 (JPUSH)
- **通信**: TURN/STUN服务器 (coturn)

---

## ⚡ 非功能性需求

### 性能要求
- **API响应时间**: P95 < 300ms
- **WebRTC连接建立**: < 3秒
- **支付处理时间**: < 5秒
- **并发用户**: 支持1000+在线用户
- **设备连接**: 支持10000+设备注册

### 可用性要求
- **系统可用性**: 99.5%
- **WebRTC连接成功率**: 95%
- **支付成功率**: 99%
- **推送到达率**: 95%

### 安全要求
- **数据传输**: 全程HTTPS/WSS加密
- **身份认证**: JWT Token + 角色权限
- **支付安全**: 支付宝官方SDK + 签名验证
- **数据存储**: 敏感数据加密存储

### 扩展性要求
- **水平扩展**: 支持多实例部署
- **模块化设计**: 功能模块可独立部署
- **插件架构**: 支持第三方服务集成
- **配置管理**: 环境配置分离

---

## 📱 客户端应用

### 1. 设备WebRTC客户端 (`device_webrtc_client.html`)
**功能特性**:
- 🔧 设备注册和配置
- 📹 本地摄像头管理
- 📞 接听视频通话
- 💳 支付系统测试
- 📊 设备状态监控

**核心操作**:
- 设备自动注册
- WebRTC连接建立
- 视频流处理
- 支付流程测试

### 2. 移动应用客户端 (`mobile_app_client.html`)
**功能特性**:
- 👤 用户登录认证
- 📱 设备列表管理
- 📞 发起视频通话
- 🔗 设备绑定操作
- 📋 通话历史记录

**核心操作**:
- 用户身份验证
- 设备发现和绑定
- 视频通话发起
- 设备状态查看

### 3. 商城客户端 (`shop_client_app.html`)
**功能特性**:
- 🛒 商品浏览购买
- 📝 订单创建管理
- 💳 支付流程处理
- 📊 订单状态跟踪

**核心操作**:
- 用户登录
- 订单创建
- 支付处理
- 状态查询

### 4. 管理员客户端 (`admin.html`)
**功能特性**:
- 👥 用户管理
- 🤖 设备管理
- 📊 系统监控
- ⚙️ 配置管理

---

## 🔌 集成与扩展

### 支付集成
**当前支持**:
- ✅ 支付宝支付 (生产就绪)
- 🔄 微信支付 (客户自行实现)

**扩展计划**:
- 银联支付
- 数字人民币
- 国际支付方式

### 推送服务集成
**当前支持**:
- ✅ 极光推送 (JPUSH)
- ✅ WebSocket实时推送
- ✅ 数据库通知记录

**扩展计划**:
- Firebase Cloud Messaging (FCM)
- 个推 (GetUI)
- 华为推送服务

### 设备类型支持
**当前支持**:
- ✅ Orange Pi设备
- ✅ 智能摄像头
- ✅ Web浏览器客户端

**扩展计划**:
- 树莓派设备
- Android设备
- iOS设备
- 其他IoT设备

---

## 📅 实施计划

### 第一阶段: 核心功能完善 (已完成)
- ✅ 用户认证系统
- ✅ 设备管理系统
- ✅ WebRTC通信系统
- ✅ 基础订单管理
- ✅ 支付宝集成

### 第二阶段: 功能增强 (进行中)
- 🔄 推送通知系统优化
- 🔄 订单履约流程完善
- 🔄 客户端功能增强
- 🔄 性能优化和监控

### 第三阶段: 扩展集成 (规划中)
- 📋 更多支付方式集成
- 📋 高级推送功能
- 📋 数据分析和报告
- 📋 移动原生应用

### 第四阶段: 企业级功能 (未来)
- 📋 多租户支持
- 📋 高可用部署
- 📋 国际化支持
- 📋 AI功能集成

---

## 📊 成功指标

### 技术指标
- WebRTC连接成功率 > 95%
- API平均响应时间 < 200ms
- 系统可用性 > 99.5%
- 支付成功率 > 99%

### 业务指标
- 设备注册成功率 > 98%
- 用户活跃度增长
- 订单完成率 > 95%
- 客户满意度 > 4.5/5

### 运营指标
- 系统故障恢复时间 < 30分钟
- 新功能发布周期 < 2周
- 代码测试覆盖率 > 80%
- 文档完整性 > 90%

---

**文档维护**: 本文档将根据产品迭代和用户反馈持续更新
**联系方式**: 如有疑问请联系产品团队
**版权声明**: © 2025 智能设备管理与订单履约系统项目组
