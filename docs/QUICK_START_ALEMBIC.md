# Alembic 快速开始指南

## 🚀 快速开始

### 1. 首次使用（新项目）

```bash
# 初始化数据库
python scripts/migrate.py init
```

### 2. 日常开发流程

#### 修改模型后创建迁移
```bash
# 方式1: 使用便捷脚本
python scripts/migrate.py create -m "Add user profile fields"

# 方式2: 直接使用 alembic
alembic revision --autogenerate -m "Add user profile fields"
```

#### 执行迁移
```bash
# 方式1: 使用便捷脚本
python scripts/migrate.py upgrade

# 方式2: 直接使用 alembic
alembic upgrade head
```

#### 查看状态
```bash
# 使用便捷脚本
python scripts/migrate.py status

# 直接使用 alembic
alembic current
alembic history
```

### 3. 团队协作

#### 拉取代码后同步数据库
```bash
git pull
python scripts/migrate.py upgrade
```

#### 提交代码前检查
```bash
# 确保迁移文件已生成
python scripts/migrate.py status

# 提交迁移文件
git add alembic/versions/
git commit -m "Add migration for new feature"
```

## 📋 常用命令速查

| 操作 | 便捷脚本 | 原生命令 |
|------|----------|----------|
| 创建迁移 | `python scripts/migrate.py create -m "描述"` | `alembic revision --autogenerate -m "描述"` |
| 执行迁移 | `python scripts/migrate.py upgrade` | `alembic upgrade head` |
| 查看状态 | `python scripts/migrate.py status` | `alembic current` |
| 回滚1步 | `python scripts/migrate.py downgrade -1` | `alembic downgrade -1` |
| 初始化 | `python scripts/migrate.py init` | `alembic upgrade head` |
| 重置数据库 | `python scripts/migrate.py reset` | `alembic downgrade base && alembic upgrade head` |

## ⚠️ 重要提醒

1. **生产环境操作前必须备份数据库**
2. **检查自动生成的迁移文件**
3. **测试迁移的升级和回滚**
4. **团队协作时及时同步迁移**

## 🔧 故障排除

### 问题1: 迁移冲突
```bash
# 查看冲突
alembic history

# 解决冲突后重新创建迁移
python scripts/migrate.py create -m "Fix migration conflict"
```

### 问题2: 迁移失败
```bash
# 查看当前状态
python scripts/migrate.py status

# 如果需要，回滚到上一个版本
python scripts/migrate.py downgrade -1
```

### 问题3: 数据库状态不一致
```bash
# 重置数据库（会丢失数据）
python scripts/migrate.py reset
```

## 📚 更多信息

详细文档请参考: [ALEMBIC_MIGRATION_GUIDE.md](./ALEMBIC_MIGRATION_GUIDE.md)
