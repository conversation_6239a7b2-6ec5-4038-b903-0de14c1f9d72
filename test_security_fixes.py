#!/usr/bin/env python3
"""
安全修复验证测试脚本
测试修复后的认证机制是否正常工作
"""

import asyncio
import aiohttp
import websockets
import json
import sys
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

class SecurityTestSuite:
    """安全测试套件"""
    
    def __init__(self):
        self.session = None
        self.test_user_token = None
        self.test_device_token = None
        
    async def setup(self):
        """初始化测试环境"""
        self.session = aiohttp.ClientSession()
        print("🔧 初始化测试环境...")
        
    async def cleanup(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
        print("🧹 清理测试环境完成")
        
    async def test_user_login(self) -> bool:
        """测试用户登录"""
        print("\n📋 测试1: 用户登录认证")
        try:
            async with self.session.post(
                f"{BASE_URL}/api/v1/auth/login",
                json={"username": "testuser", "password": "123456"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.test_user_token = data.get("access_token")
                    print("✅ 用户登录成功")
                    return True
                else:
                    print(f"❌ 用户登录失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 用户登录异常: {e}")
            return False
            
    async def test_device_register(self) -> bool:
        """测试设备注册"""
        print("\n📋 测试2: 设备注册")
        try:
            async with self.session.post(
                f"{BASE_URL}/api/v1/devices/register",
                json={
                    "device_id": "test_device_001",
                    "device_name": "测试设备",
                    "device_type": "smart_camera"
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.test_device_token = data.get("device_token")
                    print("✅ 设备注册成功")
                    return True
                else:
                    print(f"❌ 设备注册失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 设备注册异常: {e}")
            return False
            
    async def test_secure_http_endpoints(self) -> bool:
        """测试HTTP接口的安全认证"""
        print("\n📋 测试3: HTTP接口安全认证")
        
        if not self.test_user_token:
            print("❌ 缺少用户token，跳过测试")
            return False
            
        # 测试使用Authorization头的请求
        headers = {"Authorization": f"Bearer {self.test_user_token}"}
        
        test_endpoints = [
            "/api/v1/webrtc/devices",
            "/api/v1/webrtc/device/test_device_001/status"
        ]
        
        success_count = 0
        for endpoint in test_endpoints:
            try:
                async with self.session.get(f"{BASE_URL}{endpoint}", headers=headers) as response:
                    if response.status in [200, 404]:  # 404也是正常的，说明认证通过了
                        print(f"✅ {endpoint} - 认证成功")
                        success_count += 1
                    else:
                        print(f"❌ {endpoint} - 认证失败: {response.status}")
            except Exception as e:
                print(f"❌ {endpoint} - 请求异常: {e}")
                
        return success_count == len(test_endpoints)
        
    async def test_insecure_http_endpoints(self) -> bool:
        """测试不安全的HTTP请求是否被拒绝"""
        print("\n📋 测试4: 验证不安全请求被拒绝")
        
        # 测试不带Authorization头的请求
        test_endpoints = [
            "/api/v1/webrtc/devices",
            "/api/v1/webrtc/device/test_device_001/status"
        ]
        
        success_count = 0
        for endpoint in test_endpoints:
            try:
                async with self.session.get(f"{BASE_URL}{endpoint}") as response:
                    if response.status == 401:  # 应该返回401未授权
                        print(f"✅ {endpoint} - 正确拒绝未授权请求")
                        success_count += 1
                    else:
                        print(f"❌ {endpoint} - 未正确拒绝未授权请求: {response.status}")
            except Exception as e:
                print(f"❌ {endpoint} - 请求异常: {e}")
                
        return success_count == len(test_endpoints)
        
    async def test_websocket_secure_connection(self) -> bool:
        """测试WebSocket安全连接"""
        print("\n📋 测试5: WebSocket安全连接")
        
        if not self.test_user_token:
            print("❌ 缺少用户token，跳过测试")
            return False
            
        try:
            # 使用子协议传递token
            ws_url = f"{WS_BASE_URL}/api/v1/webrtc/connect?participant_type=user&target_id=test&connection_type=video"
            
            async with websockets.connect(
                ws_url,
                subprotocols=[f"Bearer.{self.test_user_token}"]
            ) as websocket:
                print("✅ WebSocket安全连接成功")
                
                # 发送测试消息
                test_message = {"type": "test", "data": "hello"}
                await websocket.send(json.dumps(test_message))
                
                # 等待响应（超时保护）
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print("✅ WebSocket消息收发正常")
                    return True
                except asyncio.TimeoutError:
                    print("⚠️ WebSocket连接成功但无响应（可能正常）")
                    return True
                    
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False
            
    async def test_websocket_insecure_connection(self) -> bool:
        """测试不安全的WebSocket连接是否被拒绝"""
        print("\n📋 测试6: 验证不安全WebSocket连接被拒绝")
        
        try:
            # 不使用子协议传递token
            ws_url = f"{WS_BASE_URL}/api/v1/webrtc/connect?participant_type=user&target_id=test&connection_type=video"
            
            async with websockets.connect(ws_url) as websocket:
                print("❌ 不安全的WebSocket连接未被拒绝")
                return False
                
        except websockets.exceptions.ConnectionClosedError as e:
            if e.code == 1008:  # 应该返回1008错误码
                print("✅ 正确拒绝不安全的WebSocket连接")
                return True
            else:
                print(f"❌ WebSocket连接关闭但错误码不正确: {e.code}")
                return False
        except Exception as e:
            print(f"✅ 不安全的WebSocket连接被拒绝: {e}")
            return True
            
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始安全修复验证测试")
        print("=" * 60)
        
        await self.setup()
        
        results = {}
        
        try:
            # 基础认证测试
            results["user_login"] = await self.test_user_login()
            results["device_register"] = await self.test_device_register()
            
            # HTTP接口安全测试
            results["secure_http"] = await self.test_secure_http_endpoints()
            results["insecure_http"] = await self.test_insecure_http_endpoints()
            
            # WebSocket安全测试
            results["secure_websocket"] = await self.test_websocket_secure_connection()
            results["insecure_websocket"] = await self.test_websocket_insecure_connection()
            
        finally:
            await self.cleanup()
            
        return results
        
    def print_summary(self, results: Dict[str, bool]):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:20} - {status}")
            
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("🎉 所有安全修复测试通过！")
            return True
        else:
            print("⚠️ 部分测试失败，需要进一步检查")
            return False

async def main():
    """主函数"""
    test_suite = SecurityTestSuite()
    results = await test_suite.run_all_tests()
    success = test_suite.print_summary(results)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
