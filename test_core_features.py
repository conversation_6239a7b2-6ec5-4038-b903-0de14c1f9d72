#!/usr/bin/env python3
"""
测试核心新功能
直接测试状态机和枚举功能
"""

def test_order_status_enum():
    """测试OrderStatus枚举"""
    print("🧪 测试OrderStatus枚举")
    
    try:
        from models.order import OrderStatus
        
        print("✅ OrderStatus枚举导入成功")
        
        # 测试所有状态
        all_statuses = [status.value for status in OrderStatus]
        print(f"所有状态: {all_statuses}")
        
        # 验证新增的READY_FOR_PICKUP状态
        assert OrderStatus.READY_FOR_PICKUP.value == "ready_for_pickup"
        print("✅ READY_FOR_PICKUP状态存在")
        
        # 验证所有必需状态
        required_statuses = [
            "pending_payment", "paid", "processing", 
            "ready_for_pickup", "completed", "cancelled"
        ]
        
        for status in required_statuses:
            assert status in all_statuses
            print(f"✅ 状态 {status} 存在")
        
        return True
        
    except Exception as e:
        print(f"❌ OrderStatus枚举测试失败: {str(e)}")
        return False

def test_state_machine():
    """测试状态机逻辑"""
    print("\n🧪 测试状态机逻辑")
    
    try:
        from services.order_state_machine import OrderStateMachine, OrderWorkflowHelper
        
        print("✅ 状态机模块导入成功")
        
        # 测试有效的状态转换
        valid_transitions = [
            ("pending_payment", "paid"),
            ("paid", "processing"),
            ("processing", "ready_for_pickup"),
            ("ready_for_pickup", "completed"),
            ("pending_payment", "cancelled"),
            ("paid", "cancelled"),
            ("processing", "cancelled")
        ]
        
        print("\n有效状态转换测试:")
        for from_status, to_status in valid_transitions:
            is_valid = OrderStateMachine.is_valid_transition(from_status, to_status)
            assert is_valid, f"转换 {from_status} -> {to_status} 应该是有效的"
            print(f"✅ {from_status} -> {to_status}")
        
        # 测试无效的状态转换
        invalid_transitions = [
            ("pending_payment", "processing"),  # 跳过paid
            ("paid", "ready_for_pickup"),       # 跳过processing
            ("paid", "completed"),              # 跳过中间状态
            ("completed", "processing"),        # 终态不能转换
            ("cancelled", "paid"),              # 终态不能转换
        ]
        
        print("\n无效状态转换测试:")
        for from_status, to_status in invalid_transitions:
            is_valid = OrderStateMachine.is_valid_transition(from_status, to_status)
            assert not is_valid, f"转换 {from_status} -> {to_status} 应该是无效的"
            print(f"✅ {from_status} -> {to_status} (正确拒绝)")
        
        # 测试权限验证
        print("\n权限验证测试:")
        staff_valid, staff_msg = OrderStateMachine.validate_status_update_for_role(
            "paid", "processing", "staff"
        )
        assert staff_valid, "员工应该可以从paid转到processing"
        print("✅ 员工权限验证正常")
        
        staff_invalid, staff_msg = OrderStateMachine.validate_status_update_for_role(
            "pending_payment", "paid", "staff"
        )
        assert not staff_invalid, "员工不应该能确认支付"
        print("✅ 员工权限限制正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态机测试失败: {str(e)}")
        return False

def test_workflow_helper():
    """测试工作流助手"""
    print("\n🧪 测试工作流助手")
    
    try:
        from services.order_state_machine import OrderWorkflowHelper
        from models.order import OrderStatus
        
        print("✅ 工作流助手导入成功")
        
        # 测试状态显示名称
        status_names = {
            "pending_payment": "待支付",
            "paid": "已支付",
            "processing": "备料中",
            "ready_for_pickup": "备料完成",
            "completed": "已完成",
            "cancelled": "已取消"
        }
        
        print("\n状态显示名称测试:")
        for status, expected_name in status_names.items():
            display_name = OrderWorkflowHelper.get_status_display_name(status)
            assert display_name == expected_name, f"状态 {status} 的显示名称应该是 {expected_name}"
            print(f"✅ {status} -> {display_name}")
        
        # 测试员工活动任务判断
        print("\n员工活动任务判断测试:")
        active_statuses = ["paid", "processing"]
        inactive_statuses = ["pending_payment", "ready_for_pickup", "completed", "cancelled"]
        
        for status in active_statuses:
            is_active = OrderWorkflowHelper.is_active_for_staff(status)
            assert is_active, f"状态 {status} 应该是员工活动任务"
            print(f"✅ {status} 是活动任务")
        
        for status in inactive_statuses:
            is_active = OrderWorkflowHelper.is_active_for_staff(status)
            assert not is_active, f"状态 {status} 不应该是员工活动任务"
            print(f"✅ {status} 不是活动任务")
        
        # 测试视频录制需求判断
        print("\n视频录制需求测试:")
        requires_video = OrderWorkflowHelper.requires_video_recording("paid", "processing")
        assert requires_video, "从paid到processing应该需要视频录制"
        print("✅ 开始备料需要视频录制")
        
        no_video = OrderWorkflowHelper.requires_video_recording("processing", "ready_for_pickup")
        assert not no_video, "从processing到ready_for_pickup不需要开始新的视频录制"
        print("✅ 备料完成不需要开始新的视频录制")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流助手测试失败: {str(e)}")
        return False

def test_schema_imports():
    """测试Schema导入"""
    print("\n🧪 测试Schema导入")
    
    try:
        from schemas.order import (
            OrderCreate, OrderResponse, OrderStatusUpdate,
            KitchenStatusUpdate, PaymentConfirmRequest, PaymentInitRequest, PaymentResponse
        )
        
        print("✅ 所有Schema导入成功")
        
        # 测试KitchenStatusUpdate schema
        kitchen_update = KitchenStatusUpdate(
            status="processing",
            video_stream_id="video_123"
        )
        assert kitchen_update.status == "processing"
        assert kitchen_update.video_stream_id == "video_123"
        print("✅ KitchenStatusUpdate schema正常")
        
        # 测试PaymentConfirmRequest schema
        payment_confirm = PaymentConfirmRequest(
            payment_transaction_id="txn_123",
            payment_method="alipay",
            verification_data={"test": "data"}
        )
        assert payment_confirm.payment_transaction_id == "txn_123"
        print("✅ PaymentConfirmRequest schema正常")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始核心功能测试")
    
    results = []
    
    # 运行所有测试
    results.append(("OrderStatus枚举", test_order_status_enum()))
    results.append(("状态机逻辑", test_state_machine()))
    results.append(("工作流助手", test_workflow_helper()))
    results.append(("Schema导入", test_schema_imports()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有核心功能测试通过！")
        print("\n📋 第一阶段开发完成情况:")
        print("✅ 任务1: OrderStatus枚举完善 - 包含READY_FOR_PICKUP状态")
        print("✅ 任务2: 支付确认接口 - PaymentConfirmRequest schema")
        print("✅ 任务3: 视频录制支持 - KitchenStatusUpdate schema")
        print("✅ 任务4: 状态转换验证 - OrderStateMachine完整实现")
        print("\n🚀 可以开始第二阶段开发：Celery异步通知系统")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要修复")

if __name__ == "__main__":
    main()
